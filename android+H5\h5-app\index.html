<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>优易花 - 智能服务平台</title>
    <link rel="stylesheet" href="assets/css/app.css">
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>正在加载...</p>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="app-container" style="display: none;">
        <!-- 头部导航 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">优易花</h1>
                <div class="header-actions">
                    <button id="menuBtn" class="menu-btn">☰</button>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 首页内容 -->
            <div id="homePage" class="page active">
                <div class="welcome-section">
                    <h2>欢迎使用优易花</h2>
                    <p>智能处理系统，便捷服务流程，高效业务办理</p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card" onclick="navigateTo('login')">
                        <div class="feature-icon">🔐</div>
                        <h3>用户登录</h3>
                        <p>安全登录系统</p>
                    </div>

                    <div class="feature-card" onclick="navigateTo('services')">
                        <div class="feature-icon">💼</div>
                        <h3>业务服务</h3>
                        <p>查看业务信息</p>
                    </div>

                    <div class="feature-card" onclick="navigateTo('profile')">
                        <div class="feature-icon">👤</div>
                        <h3>个人中心</h3>
                        <p>管理个人信息</p>
                    </div>

                    <div class="feature-card" onclick="navigateTo('contact')">
                        <div class="feature-icon">📞</div>
                        <h3>联系我们</h3>
                        <p>获取帮助支持</p>
                    </div>
                </div>
            </div>

            <!-- 登录页面 -->
            <div id="loginPage" class="page">
                <div class="login-container">
                    <h2>用户登录</h2>
                    <form id="loginForm" class="login-form">
                        <div class="form-group">
                            <label for="phone">手机号码</label>
                            <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" name="password" placeholder="请输入密码" required>
                        </div>
                        <button type="submit" class="login-btn">登录</button>
                    </form>
                    <div class="login-actions">
                        <a href="#" onclick="showToast('功能开发中')">忘记密码？</a>
                        <a href="#" onclick="showToast('功能开发中')">注册账号</a>
                    </div>
                </div>
            </div>

            <!-- 其他页面占位 -->
            <div id="servicesPage" class="page">
                <h2>业务服务</h2>
                <p>业务服务功能正在开发中...</p>
            </div>

            <div id="profilePage" class="page">
                <h2>个人中心</h2>
                <p>个人中心功能正在开发中...</p>
            </div>

            <div id="contactPage" class="page">
                <h2>联系我们</h2>
                <div class="contact-info">
                    <p>📞 客服电话：400-xxx-xxxx</p>
                    <p>📧 邮箱：<EMAIL></p>
                    <p>🕒 服务时间：9:00-18:00</p>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="app-nav">
            <div class="nav-item active" onclick="navigateTo('home')">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">首页</span>
            </div>
            <div class="nav-item" onclick="navigateTo('services')">
                <span class="nav-icon">💼</span>
                <span class="nav-text">服务</span>
            </div>
            <div class="nav-item" onclick="navigateTo('profile')">
                <span class="nav-icon">👤</span>
                <span class="nav-text">我的</span>
            </div>
        </nav>
    </div>

    <!-- Toast提示 -->
    <div id="toast" class="toast"></div>

    <!-- JavaScript -->
    <script src="assets/js/app.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟加载时间
            setTimeout(function() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('app').style.display = 'block';
                
                // 检测是否在APP中
                if (typeof Android !== 'undefined') {
                    console.log('运行在优易花APP中');
                    // 可以调用原生功能
                    // Android.showToast('欢迎使用优易花APP');
                } else {
                    console.log('运行在浏览器中');
                }
            }, 1500);
        });
    </script>
</body>
</html>
