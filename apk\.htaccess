# APK文件下载配置
# 确保Android APK文件使用正确的MIME类型

# 设置APK文件的MIME类型
AddType application/vnd.android.package-archive .apk

# 允许APK文件下载
<Files "*.apk">
    Header set Content-Type "application/vnd.android.package-archive"
    Header set Content-Disposition "attachment"
</Files>

# 设置缓存控制
<Files "*.apk">
    ExpiresActive On
    ExpiresDefault "access plus 1 day"
</Files>

# 安全设置 - 防止直接访问敏感文件
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 允许诊断脚本访问
<Files "diagnose-apk-download.php">
    Order allow,deny
    Allow from all
</Files>

# 防止访问备份文件
<Files "*.backup">
    Order deny,allow
    Deny from all
</Files>

# 防止访问临时文件
<Files "*.tmp">
    Order deny,allow
    Deny from all
</Files>

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用压缩（如果服务器支持）
<IfModule mod_deflate.c>
    # 不压缩APK文件（已经是压缩格式）
    SetEnvIfNoCase Request_URI \.apk$ no-gzip dont-vary
</IfModule>
