/* 优易花H5应用样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

/* 加载动画 */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 应用容器 */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部导航 */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.app-title {
    font-size: 20px;
    font-weight: 600;
}

.menu-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
}

/* 主要内容区域 */
.app-main {
    flex: 1;
    margin-top: 60px;
    margin-bottom: 70px;
    padding: 20px;
}

/* 页面切换 */
.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 欢迎区域 */
.welcome-section {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.welcome-section h2 {
    font-size: 28px;
    color: #333;
    margin-bottom: 10px;
}

.welcome-section p {
    color: #666;
    font-size: 16px;
}

/* 功能网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.feature-card {
    background: white;
    padding: 25px 15px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.feature-icon {
    font-size: 40px;
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #333;
}

.feature-card p {
    color: #666;
    font-size: 14px;
}

/* 登录表单 */
.login-container {
    max-width: 400px;
    margin: 0 auto;
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.login-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.login-actions a {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
}

/* 联系信息 */
.contact-info {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.contact-info p {
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

/* 底部导航 */
.app-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    border-top: 1px solid #e1e1e1;
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 5px;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: #667eea;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.nav-text {
    font-size: 12px;
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .app-main {
        padding: 15px;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .login-container {
        margin: 0 15px;
        padding: 30px 20px;
    }

    .welcome-section {
        padding: 30px 15px;
    }
}

/* 安全区域适配（刘海屏等） */
@supports (padding: max(0px)) {
    .app-header {
        padding-top: max(10px, env(safe-area-inset-top));
    }

    .app-nav {
        padding-bottom: max(10px, env(safe-area-inset-bottom));
    }
}
