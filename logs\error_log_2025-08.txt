{"timestamp":"2025-08-01 13:32:17","type":"INFO","message":"使用默认还款账号数据","ip":"************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=123","context":{"customer_id":123},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 13:36:27","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=50","context":{"customer_id":50},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 13:48:44","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=123","context":{"customer_id":123},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 13:49:20","type":"INFO","message":"成功获取客户还款账号数据","ip":"***********","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=123","context":{"customer_id":123},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 15:04:07","type":"INFO","message":"使用默认还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=126","context":{"customer_id":126},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 15:13:27","type":"INFO","message":"成功获取客户还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=50","context":{"customer_id":50},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 15:16:43","type":"INFO","message":"成功获取客户还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.1 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=50","context":{"customer_id":50},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 15:23:02","type":"INFO","message":"使用默认还款账号数据","ip":"************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=127","context":{"customer_id":127},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 16:20:38","type":"INFO","message":"成功获取客户还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=114","context":{"customer_id":114},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 16:23:59","type":"INFO","message":"使用默认还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=128","context":{"customer_id":128},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 17:06:47","type":"INFO","message":"成功获取客户还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=128","context":{"customer_id":128},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 17:12:02","type":"INFO","message":"成功获取客户还款账号数据","ip":"*************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=128","context":{"customer_id":128},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 17:57:05","type":"INFO","message":"使用默认还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Linux; U; Android 14; zh-cn; NDL-W09 Build\/HONORNDL-W09) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/121.0.6167.71 MQQBrowser\/19.2 Mobile Safari\/537.36 COVC\/048201","request_uri":"\/get_customer_payment.php?customer_id=129","context":{"customer_id":129},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 20:36:19","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=129","context":{"customer_id":129},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 20:36:34","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=128","context":{"customer_id":128},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 20:36:44","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=127","context":{"customer_id":127},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-01 20:36:55","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=126","context":{"customer_id":126},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:25:58","type":"INFO","message":"使用默认还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=130","context":{"customer_id":130},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:26:33","type":"INFO","message":"使用默认还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=130","context":{"customer_id":130},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":44,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:38:36","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=130","context":{"customer_id":130},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:38:44","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=129","context":{"customer_id":129},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:38:55","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=128","context":{"customer_id":128},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 14:39:06","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=127","context":{"customer_id":127},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-02 23:09:06","type":"INFO","message":"成功获取客户还款账号数据","ip":"**************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","request_uri":"\/get_customer_payment.php?customer_id=130","context":{"customer_id":130},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-03 14:43:14","type":"INFO","message":"成功获取客户还款账号数据","ip":"************","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=129","context":{"customer_id":129},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
{"timestamp":"2025-08-03 14:50:53","type":"INFO","message":"成功获取客户还款账号数据","ip":"***********","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/97.0.4692.98 Safari\/537.36 T7\/13.38 languageType\/0 bdh_dvt\/1 bdh_de\/0 bdh_ds\/1 bdhonorbrowser\/******* (P1 14)","request_uri":"\/get_customer_payment.php?customer_id=129","context":{"customer_id":129},"backtrace":[{"file":"\/www\/sites\/dailuanshej.cn\/index\/error_handler.php","line":230,"function":"logError","class":"ErrorHandler","type":"->"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":39,"function":"logInfo"},{"file":"\/www\/sites\/dailuanshej.cn\/index\/get_customer_payment.php","line":71,"function":"getCustomerPaymentAccounts","class":"PaymentAccountManager","type":"->"}]}
