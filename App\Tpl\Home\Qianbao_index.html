<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>💰 我的钱包 - 小贷系统</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 30px 20px 40px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        /* 钱包卡片 */
        .wallet-card {
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 30px 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 1;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .wallet-title {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .balance-amount {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .balance-unit {
            font-size: 20px;
            margin-left: 8px;
        }

        .last-loan {
            font-size: 14px;
            opacity: 0.8;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .loan-icon {
            font-size: 16px;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 300px);
            padding: 25px 20px 100px;
            margin-top: -20px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 功能按钮区域 */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            padding: 20px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .action-btn:hover::before {
            opacity: 1;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .btn-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        /* 银行卡信息 */
        .bank-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bank-card {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border-radius: 16px;
            padding: 25px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .bank-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .bank-name {
            font-size: 16px;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .bank-number {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 2px;
            position: relative;
            z-index: 1;
        }

        /* 安全提示 */
        .security-notice {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .security-icon {
            font-size: 24px;
            color: #28a745;
            margin-bottom: 10px;
        }

        .security-text {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.5;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            padding: 10px 0 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item-bottom {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 12px;
            transition: all 0.2s ease;
            padding: 5px 10px;
        }

        .nav-item-bottom.active {
            color: #667eea;
        }

        .nav-item-bottom:hover, .nav-item-bottom:active {
            color: #667eea;
            text-decoration: none;
        }

        .nav-icon-bottom {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* 底部安全区域 */
        .bottom-safe-area {
            height: 80px;
            background: transparent;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }

            .header {
                padding: 25px 15px 35px;
            }

            .main-content {
                padding: 20px 15px 80px;
            }

            .wallet-card {
                padding: 25px 20px;
            }

            .balance-amount {
                font-size: 36px;
            }

            .action-buttons {
                gap: 12px;
            }

            .action-btn {
                padding: 18px;
                font-size: 15px;
            }

            .btn-icon {
                font-size: 22px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 320px;
            width: 90%;
            text-align: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .modal-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }

        .modal-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .modal-btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn-cancel {
            background: #f8f9fa;
            color: #6c757d;
        }

        .modal-btn-confirm {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <!-- 钱包卡片 -->
            <div class="wallet-card">
                <div class="wallet-title">账户余额</div>
                <div class="balance-amount">
                    {$user.zhanghuyue|sprintf='%.2f',###}
                    <span class="balance-unit">元</span>
                </div>
                <div class="last-loan">
                    <span class="loan-icon">📊</span>
                    最近一笔借款 <strong>{$order.money|sprintf='%.2f',###}</strong> 元
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 功能按钮 -->
            <div class="action-buttons">
                <button class="action-btn" onclick="showWithdrawModal()">
                    <span class="btn-icon">💸</span>
                    提现
                </button>
                <button class="action-btn" onclick="window.location.href='{:U(\'Qianbao/pay\')}'">
                    <span class="btn-icon">🔑</span>
                    获取提现码
                </button>
            </div>

            <!-- 银行卡信息 -->
            <div class="bank-section">
                <h2 class="section-title">
                    <span>💳</span>
                    我的银行卡
                </h2>
                <div class="bank-card">
                    <div class="bank-name">银行卡</div>
                    <div class="bank-number">{$bankcard1}&nbsp;****&nbsp;****&nbsp;{$bankcard}</div>
                </div>
            </div>

            <!-- 安全提示 -->
            <div class="security-notice">
                <div class="security-icon">🛡️</div>
                <div class="security-text">账户资金安全由银行保障</div>
            </div>

            <!-- 底部安全区域 -->
            <div class="bottom-safe-area"></div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="{:U('Index/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">🏠</span>
                    <span>首页</span>
                </a>
                <a href="{:U('Qianbao/index')}" class="nav-item-bottom active">
                    <span class="nav-icon-bottom">💰</span>
                    <span>钱包</span>
                </a>
                <a href="{:U('Help/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">💬</span>
                    <span>客服</span>
                </a>
                <a href="{:U('User/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">👤</span>
                    <span>我的</span>
                </a>
            </div>
        </div>

        <!-- 提现模态框 -->
        <div class="modal" id="withdrawModal">
            <div class="modal-content">
                <div class="modal-title">💰 钱包提现</div>
                <a href="{:U('Qianbao/pay')}" style="color:#667eea; text-decoration:none; font-size:14px;">获取提现密码请点击</a>
                <input type="hidden" class="modal-input" id="withdrawAmount" readonly value="{$user.zhanghuyue}">
                <input type="number" class="modal-input" id="withdrawPassword" placeholder="请输入提现码">
                <div class="modal-buttons">
                    <button class="modal-btn modal-btn-cancel" onclick="hideWithdrawModal()">取消</button>
                    <button class="modal-btn modal-btn-confirm" onclick="submitWithdraw()">提交</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');
        });

        // 显示提现模态框
        function showWithdrawModal() {
            document.getElementById('withdrawModal').style.display = 'block';
        }

        // 隐藏提现模态框
        function hideWithdrawModal() {
            document.getElementById('withdrawModal').style.display = 'none';
            document.getElementById('withdrawPassword').value = '';
        }

        // 提交提现
        function submitWithdraw() {
            const password = document.getElementById('withdrawPassword').value;
            const amount = document.getElementById('withdrawAmount').value;

            if (!password) {
                alert('请输入提现码');
                return;
            }

            // 发送AJAX请求
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `txpass=${password}&txmoney=${amount}`
            })
            .then(response => response.json())
            .then(data => {
                alert(data.msg);
                if (data.msg === '提现成功') {
                    hideWithdrawModal();
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提现失败，请重试');
            });
        }

        // 点击模态框外部关闭
        document.getElementById('withdrawModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideWithdrawModal();
            }
        });

        // 添加触摸反馈
        document.querySelectorAll('.action-btn, .nav-item-bottom').forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    </script>
</body>
</html>

			<div class="ljtx">
				<if condition="$order['status'] eq 1 ">
				<button>

					<b></b>

				</button>
				<elseif condition="$order['status'] eq 4"/>
				<button>

					<b></b>

				</button>
				
				<else />
				<button id="edit">

					<b>立即提现</b>

				</button>

				</if>
				
			</div>

		</div>

	</div>

	<div class="am-modal am-modal-prompt" tabindex="-1" id="my-prompt">

		<div class="am-modal-dialog">

			<div class="am-modal-hd">钱包提现</div>
			<div class="am-modal-bd">
				<a href="{:U('Qianbao/pay')}" style="color:red">获取提现密码请点击</a>
				<input type="hidden" class="am-modal-prompt-input input_txmoney" readonly="readonly" value="{$user.zhanghuyue}">
				<input type="number" class="am-modal-prompt-input input_txpass" placeholder="请输入提现码">
			</div>

			<div class="am-modal-footer">

				<span class="am-modal-btn" data-am-modal-cancel="">取消</span>

				<span class="am-modal-btn" data-am-modal-confirm="">提交</span>

			</div>

		</div>

	</div>

	<div class="bank_info">

		<div class="am-g">

			<h2>我的银行卡</h2>

		</div>

		<div class="am-g bank_card_box">

			<div class="bank_card_info">

				<span></span>

				<br>

			   <span class="f_number">{$bankcard1}&nbsp;****&nbsp;****&nbsp;{$bankcard}</span>
				<!--<span class="f_number">{$userinfo['bankcard']}</span>-->

			</div>

		</div>

	</div>
	
	<div class="loan">
		<div class="am-g">
			<div class="am-u-sm-12 represent">
				<b>描述：{$order['pending']}</b>
				<p>{$order['error']}</p>
			</div>
		</div>
	</div>


	<div class="bank_bz">

		<i class="am-icon-shield"></i>

		账户资金安全由银行保障

	</div>

	<div class="am-modal am-modal-no-btn" tabindex="-1" id="doc-modal-1">

		<div class="am-modal-dialog" style="width: 340px;background: none;">

			<div class="am-modal-hd">

				<a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close=""
					style="color: #ffffff;font-size: 30px;opacity: 1;">&times;</a>

			</div>

			<div class="am-modal-bd">

				<div>



					<div class="topline">



					</div>

					<div class="sq_box">

						<div class="am-g">

							<h5>每月需还款</h5>

							<div class="am-u-sm-12" style="text-align: center;">

								<b class="f_number rll_number p_jkje">0</b>

								<span class="rll_symbol">元</span>

								<br>

								<span class="loan_title"></span>

							</div>

						</div>



						<div class="am-g p_u_info" style="padding-top: 30px;">

							<div class="am-u-sm-12">订单号</div>

							<div class="am-u-sm-12 p_u_fullname f_number"></div>

						</div>



						<div class="am-g p_u_info">

							<div class="am-u-sm-12">二维码</div>

						</div>



						<div class="am-g p_u_info">

							<div class="am-u-sm-12">说明</div>

							<div class="am-u-sm-12 p_u_showbank"></div>

						</div>



					</div>

					<div class="bottomline"></div>

				</div>

			</div>

		</div>

	</div>



	<div class="message">

		<p></p>

	</div>



	<!-- 底部导航条 -->

	<div data-am-widget="navbar" class="am-navbar am-cf am-navbar-default " id="bm-nav">

		<ul class="am-navbar-nav am-cf am-avg-sm-4" style="background-color: #ffffff;">

			<li class="nva_sy">

				<a href="/" class="">

					<img src="__PUBLIC__/home/<USER>/picture/2-1.png" alt="消息">



					<span class="am-navbar-label">首页</span>

				</a>

			</li>

			<li class="nva_qb">

				<a href="{:U('Qianbao/index')}" class="">

					<img src="__PUBLIC__/home/<USER>/picture/3-1.png" alt="消息">



					<span class="am-navbar-label">钱包</span>

				</a>

			</li>

			<li class="nva_kf">

				<a href="{:U('Help/index')}" class="">

					<img src="__PUBLIC__/home/<USER>/picture/1-1.png" alt="消息">



					<span class="am-navbar-label">客服</span>

				</a>

			</li>

			<li class="nva_wd">

				<a href="{:U('User/index')}" class="">

					<img src="__PUBLIC__/home/<USER>/picture/4-1.png" alt="消息">



					<span class="am-navbar-label">我的</span>

				</a>

			</li>

		</ul>

	</div>





	<div id="kefu"></div>

	<!-- <div id="kefu"></div> -->

	<script type="text/javascript">

		document.documentElement.addEventListener('touchmove', function (event) {

			if (event.touches.length > 1) {

				event.preventDefault();

			}

		}, false);

	</script>

	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>

	<!--<![endif]-->

	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>



	<script>

		$("#qb #bm-nav .nva_qb a img").attr('src', '__PUBLIC__/home/<USER>/picture/3-2.png');





		var txpass, ordernumber, timer;

		// 弹窗



		// 倒计时

		function myTimer() {

			var sec = 3;

			clearInterval(timer);

			timer = setInterval(function () {

				console.log(sec--);

				if (sec == 1) {

					$(".message").addClass("m-hide");

					$(".message").removeClass("m-show");

				}

				if (sec == 0) {

					$(".message").hide();

					$(".message").removeClass("m-hide");

					clearInterval(timer);

				}

			}, 1000);

		}



		// 弹窗内容

		function message(data) {

			msg = $(".message p").html(data);

			$(".message").addClass("m-show");

			$(".message").show();



			myTimer();



		}



		// 初始化弹窗

		function mesg_default() {

			msg = '';

			$(".message").hide();

			$(".message").removeClass("m-show");

			$(".message").removeClass("m-hide");

		}


		$('#edit').on('click', function () {
		
			$('.input_txpass').val('');


			$('#my-prompt').modal({

				relatedTarget: this,

				onConfirm: function (e) {



					mesg_default();
					money = $('.input_txmoney').val();
					password = $('.input_txpass').val();
					if (money == '') {

					message('提现金额不能为空');

					return false;

					}
					if (password == '') {

						message('提现密码不能为空');

						return false;

					}

					$.post('{:U("Qianbao/index")}',

						{
            				money:money,
							password: password

						},

						function (data) {
							if(data.msg=="提现成功"){
									message(data.msg);
									setTimeout(function (){
										location.reload();
									},2000)
								//	window.location.href='{:U("Qianbao/pay")}';
							}else{
									message(data.msg);
							}


						}

					);

				},

				onCancel: function (e) {



				}

			});

		});




	</script>


  <div style="display: none;">
    <Somnus:sitecfg name="sitecode" />
  </div>




</body>

</html>
