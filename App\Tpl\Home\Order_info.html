<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title><Somnus:sitecfg name="sitetitle"/> - 小贷系统</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta name="description" content=" <Somnus:sitecfg name="sitedescription"/> ">
<meta name="Keywords" content=" <Somnus:sitecfg name="sitekeywords"/> ">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/mui.min.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/feiqi-ee5401a8e6.css">
<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newpay-bb7fcb5546.css">
</head>
<script>
document.addEventListener('plusready',function(){
var webview = plus.webview.currentWebview();
plus.key.addEventListener('backbutton', function() {
webview.canBack(function(e) {
        if (e.canBack) {
                webview.back();
        } else {
            webview.close();//hide,quit
        }
    })
});

});
</script>
<body>
    <!-- header -->
    <header class="mui-bar mui-bar-nav hnav">
		<a href="{:U('Order/lists')}" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
		<h1 class="mui-title">借款详情</h1>
	</header>
	<!-- header end-->
<div class="mui-content">
	<article class="jiekuan">
		<div class="oktit">
			借款进度
		</div>
					<div class="cf okdan">
			<div class="kprogress" style="width: 60%;
    margin: 0 auto;">
				<span class="f14 ">
					借款审批
				</span>

				<span class="fr f26 red"><span class="f18">¥</span>{$data.money}</span>

			</div>
			<div class="prgbar">
					<div class="prg1">
						<div>
							<php>
								if($data['status'] == 0)
									echo '<span class="procir3"></span>';
								else
									echo '<span class="procir3"></span>';
							</php>
						</div>
						<div class="mt4">
							<p>
								<php>
									if($data['status'] == 1)
										echo "提交审核";
									else
										echo "提交审核";
								</php>			</p>
							<p class="f12"></p>
						</div>
					</div>
					<div class="prg2">
						<div>
							<php>
								if($data['status'] == 1){
									echo '<span class="procir3"></span>';
								}elseif($data['status'] == 2){
									echo '<span class="procir2"></span>';
								}elseif($data['status'] == 3){
									echo '<span class="procir3"></span>';
								}elseif($data['status'] == 4){
									echo '<span class="procir3"></span>';
								}elseif($data['status'] == 5){
									echo '<span class="procir4"></span>';
								}elseif($data['status'] == 6){
									echo '<span class="procir2"></span>';
								}elseif($data['status'] == 7){
									echo '<span class="procir2"></span>';
								}elseif($data['status'] == 8){
									echo '<span class="procir4"></span>';
								}elseif($data['status'] == 9){
									echo '<span class="procir2"></span>';
								}
							</php>
						</div>
						<div class="mt4">
							<p>
								<php>
									if($data['status'] == 1){
										echo "正在审核";
									}elseif($data['status'] == 2){
										echo "到帐钱包";
									}elseif($data['status'] == 3){
										echo "审核通过";
									}elseif($data['status'] == 4){
										echo "审核不通过";
									}elseif($data['status'] == 5){
										echo "押金";
									}elseif($data['status'] == 6){
										echo "冻结";
									}elseif($data['status'] == 7){
										echo "保险";
									}elseif($data['status'] == 8){
										echo "利息";
									}elseif($data['status'] == 9){
										echo "银行卡异常";
									}elseif($data['status'] == 10){
										echo "征信";
									}elseif($data['status'] == 11){
										echo "提现成功";
									}
								</php>
							</p>
							<p class="f12"></p>
						</div>
					</div>
					<div class="prg3">
						<div>
							<php>
								if($data['status'] == 2){
									echo '<span class="procir3"></span>';
								}else{
									echo '<span class="procir1"></span>';
								}
							</php>
						</div>
						<div class="mt4">
							<p>
								到账成功
							</p>
							<p class="f12"></p>
						</div>
					</div>
			</div>
		</div>
			</article>
			<php>
			if($data['name'] != ''){
		</php>
			<article class="mt10 jiekuan">
		<div class="cf okdan" style="padding: 0px">
			<div class="oktable">
				<div class="fc9 listit" style="width: 25%;float: left;">温馨提示</div>
				<div style="width: 71%;float: right;">
					<span style="color: red;">
						

	
<php>
	echo	$data['name'];
</php><br>
					</span>
				
				</div>
				
			</div>
		</div>
	</article>
	<php>
	}
								
				</php>
	<article class="mt10 jiekuan">
		<div class="oktit">
			借款详情
		</div>
		<div class="cf okdan">
           <div class="oktable">
<!--
			<span class="fc9 listit"><a class="fr f13 danstate datalose" href="/index.php?m=Order&a=hetong&oid=382">>>查看电子合同</a></span>	
-->
				<span></span>

			</div>
			<div class="oktable">
				<span class="fc9 listit">订单编号</span>
				<span>{$data.ordernum}</span>
			</div>
			
			<div class="oktable">
				<span class="fc9 listit">借款金额</span>
				<span>￥{$data.money}元</span>
			</div>
			<div class="oktable">
				<span class="fc9 listit">借款期限</span>
				<span>{$data.months}个月</span>
			</div>
			<div class="oktable">
				<span class="fc9 listit">到账银行</span>
				<span>{$data.bank}</span>
			</div>
			<div class="oktable">
				<span class="fc9 listit">收款户名</span>
				<span>{$user.name}</span>
			</div>
			<div class="oktable">
				<span class="fc9 listit">收款账号</span>
				<span>{$data.banknum}</span>
			</div>
			<div class="oktable">
				<span class="fc9 listit">每期还款</span>
				<span>¥{$data.monthmoney}元</span>
			</div>
			<div class="oktable">
				<span class="fc9 fl listit">还款说明</span>
				<span class="fl deinfo">请您于每月{:C('cfg_huankuanri')}号前,进行还款操作，逾期将给您生活带来不便！</span>
			</div>
		</div>
	</article>
</div>
		<div class="protit sevagreee ">
            <a class="logBtn" href="{:U('Order/shenqing',array('oid' => $data['id']))}">提现</a>
        </div>
        <div class="protit sevagreee " style="text-align: center;">
            <div>查看<a href="javascript:;" id="btnjiekuan" style="color:#fd6f00;">《借款合同》</a></div>
        </div>
<div class="deowin" style="padding: 0px 6%;margin-top: -210px; display: none;" id="deowin33">
    <div class="deocon">
    <h3 style="text-align: center;font-size: 16px; color:#fb6f00;height: 40px;line-height: 40px;border-bottom: 1px solid #fb6f00;">《借款合同》</h3>
        <div class="divpad" style="height: 340px;overflow-y: auto;">
            <iframe src="" style="width:100%;height:100%;border:none;"></iframe>
        </div>
        <div class="wobtn">
            <!-- 一个按钮用这个结构 -->
                <a style="color:#fb6f00;" id="winbtn33" href="javascript:;">关闭</a>
        </div>
    </div>
</div>
<script src="__PUBLIC__/home/<USER>/jquery-1-fe84a54bc0.11.1.min.js"></script>
	<script>
	$(function() {
		$("#btnjiekuan").click(function() {
	        var url="<Somnus:block name="协议2地址" />";
	        $('#deowin33 iframe').attr('src','/457.html');           
	        setTimeout(function (){
	            $('#deowin33').show();
	            $('#mask3').show();  
	        },500);
	    });
	    $('#winbtn33').click(function(){
            $('#deowin33').hide();
            $('#mask3').hide();
            $('#deowin33 iframe').attr('src','');      

       });
	})
		
	</script>
</body>
</html>