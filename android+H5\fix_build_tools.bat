@echo off
echo ========================================
echo Android Build Tools 修复脚本
echo ========================================
echo.

echo 正在检查Android SDK路径...
if not defined ANDROID_HOME (
    echo 错误: ANDROID_HOME 环境变量未设置
    echo 请设置ANDROID_HOME指向您的Android SDK目录
    echo 例如: set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    pause
    exit /b 1
)

echo Android SDK 路径: %ANDROID_HOME%
echo.

echo 1. 删除损坏的Build Tools 34.0.0...
if exist "%ANDROID_HOME%\build-tools\34.0.0" (
    rmdir /s /q "%ANDROID_HOME%\build-tools\34.0.0"
    echo Build Tools 34.0.0 已删除
) else (
    echo Build Tools 34.0.0 目录不存在
)

echo.
echo 2. 使用SDK Manager重新安装Build Tools...
echo 请按照以下步骤操作:
echo.
echo a) 打开Android Studio
echo b) 点击 Tools -> SDK Manager
echo c) 切换到 "SDK Tools" 标签页
echo d) 找到 "Android SDK Build-Tools"
echo e) 取消勾选 34.0.0 (如果已勾选)
echo f) 勾选最新版本 (推荐 34.0.0 或更高版本)
echo g) 点击 "Apply" 下载并安装
echo.

echo 3. 或者使用命令行安装:
echo %ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager "build-tools;34.0.0"
echo.

echo 4. 安装完成后，重新同步Gradle项目
echo.

echo ========================================
echo 修复完成！请重新编译项目
echo ========================================
pause
