<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">

	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>还款详情 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myrepayment.css">
	<style>
		.coupon_zero {
			width: 100%;
			height: 100%;
			text-align: center;
			margin: 70px 0 0;
			color: #a8a6a7;
		}
	</style>
</head>

<body>
	<div class="comm_top_nav">
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('Order/bills')}'"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">还款详情-共{$data.0.months}期</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
	<empty name="data">
		<div class="coupon_zero">
			暂无还款
			<img src="__PUBLIC__/home/<USER>/image/norepay.png" alt="" width="100%">
		</div>
		<else />
		<foreach name="data" item="vo">
		<div class="repayment">
			<div class="am-g">
				<div class="am-u-sm-8">
					<span class="f_number">￥<span class="hk_money">{$vo.monthmoney}</span></span>
				</div>
				<div class="am-u-sm-4 stages_number">
					<span>第<span class="f_number">{$vo.ofnumber}</span>期</span>
				</div>
			</div>
			<div class="am-g state_info">
				<div class="am-u-sm-12">
					还款截止日期：<span class="f_number">{$vo.huantime}</span>
					<br>
					状态：<span class="repayment_state_yq"> <if condition="$vo['zfimg'] eq ''">
						<if condition="$time gt $vo['huantime']">已逾期-立即还款</if>
						<if condition="$time lt $vo['huantime']">提前还款</if>
						<if condition="$time eq $vo['huantime']">立即还款</if>
					 </if>
					 <if condition="$vo['zfimg'] neq ''">
						 <if condition="$vo.status eq 0">待确认还款状态</if>
						 <if condition="$vo.status eq 1">已还款</if>

					 </if>
				</span>
				</div>
			</div>
			<if condition="$vo.status neq 1">
			<div class="am-g">
				<div class="am-u-sm-12">
					<span class="state_button"><a href="{:U('Order/repay',array('type'=>2,'money'=>$vo['monthmoney'],'ordernum'=>$vo['ordernum'],'id'=>$vo['id']))}" style="color: #fff;">提交还款</a></span>
				</div>
			</div>
			</if>
		</div>
</foreach>
</empty>

<!-- 还款账户信息 -->
<if condition="$repayment_accounts">
	<div class="repayment_accounts_section" style="margin: 20px 15px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
		<div style="padding: 15px; border-bottom: 1px solid #f0f0f0;">
			<h3 style="margin: 0; color: #333; font-size: 16px;">💳 还款账户信息</h3>
			<p style="margin: 5px 0 0; color: #666; font-size: 12px;">请选择以下任一账户进行还款</p>
		</div>

		<volist name="repayment_accounts" id="account">
			<div class="account_item" style="padding: 15px; border-bottom: 1px solid #f8f8f8;">
				<div style="display: flex; align-items: center; margin-bottom: 10px;">
					<span style="background: <if condition='$account.account_type eq 1'>#1890ff<else/>#52c41a</if>; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-right: 10px;">
						<if condition="$account.account_type eq 1">对公<else/>对私</if>
					</span>
					<strong style="color: #333; font-size: 14px;">{$account.account_name}</strong>
				</div>

				<div style="margin-bottom: 8px;">
					<span style="color: #666; font-size: 13px;">银行：</span>
					<span style="color: #333; font-size: 13px;">{$account.bank_name}</span>
				</div>

				<div style="margin-bottom: 8px;">
					<span style="color: #666; font-size: 13px;">账号：</span>
					<span style="color: #333; font-size: 13px; font-family: monospace;">{$account.account_number}</span>
				</div>

				<div style="margin-bottom: 10px;">
					<span style="color: #666; font-size: 13px;">户名：</span>
					<span style="color: #333; font-size: 13px;">{$account.account_holder}</span>
				</div>

				<if condition="$account.qr_code_image">
					<div style="text-align: center; margin-top: 10px;">
						<p style="color: #666; font-size: 12px; margin-bottom: 8px;">
							<if condition="$account.qr_code_type eq 'wechat'">微信扫码支付
							<elseif condition="$account.qr_code_type eq 'alipay'"/>支付宝扫码支付
							<else/>扫码转账
							</if>
						</p>
						<img src="{$account.qr_code_image}"
							 style="width: 150px; height: 150px; border: 1px solid #e6e6e6; border-radius: 8px; cursor: pointer;"
							 onclick="showQrCode('{$account.qr_code_image}', '{$account.account_name}')"
							 alt="二维码">
						<p style="color: #999; font-size: 11px; margin-top: 5px;">点击查看大图</p>
					</div>
				</if>

				<if condition="$account.description">
					<div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
						<p style="color: #666; font-size: 12px; margin: 0;">{$account.description}</p>
					</div>
				</if>
			</div>
		</volist>

		<div style="padding: 15px; background: #f8f9fa; border-radius: 0 0 8px 8px;">
			<p style="margin: 0; color: #ff6b35; font-size: 12px; text-align: center;">
				⚠️ 还款后请保留转账凭证，并及时上传支付凭证
			</p>
		</div>
	</div>
</if>

		<div style="height: 20px;"></div>
		<div data-am-widget="gotop" class="am-gotop am-gotop-fixed">
			<a href="#top" title="">
				<i class="am-gotop-icon am-icon-arrow-up"></i>
			</a>
		</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);

		// 复制文本功能
		function copyText(text) {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(text).then(function() {
					alert('账号已复制到剪贴板');
				}).catch(function() {
					fallbackCopyText(text);
				});
			} else {
				fallbackCopyText(text);
			}
		}

		function fallbackCopyText(text) {
			var textArea = document.createElement("textarea");
			textArea.value = text;
			textArea.style.position = "fixed";
			textArea.style.left = "-999999px";
			textArea.style.top = "-999999px";
			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();
			try {
				document.execCommand('copy');
				alert('账号已复制到剪贴板');
			} catch (err) {
				alert('复制失败，请手动复制账号');
			}
			document.body.removeChild(textArea);
		}

		// 显示二维码大图
		function showQrCode(imagePath, accountName) {
			var overlay = document.createElement('div');
			overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999; display: flex; align-items: center; justify-content: center;';

			var container = document.createElement('div');
			container.style.cssText = 'background: white; padding: 20px; border-radius: 10px; text-align: center; max-width: 90%; max-height: 90%;';

			var title = document.createElement('h3');
			title.textContent = accountName + ' - 二维码';
			title.style.cssText = 'margin: 0 0 15px 0; color: #333;';

			var img = document.createElement('img');
			img.src = imagePath;
			img.style.cssText = 'max-width: 100%; max-height: 400px; border-radius: 8px;';

			var closeBtn = document.createElement('button');
			closeBtn.textContent = '关闭';
			closeBtn.style.cssText = 'margin-top: 15px; padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;';
			closeBtn.onclick = function() {
				document.body.removeChild(overlay);
			};

			container.appendChild(title);
			container.appendChild(img);
			container.appendChild(closeBtn);
			overlay.appendChild(container);

			overlay.onclick = function(e) {
				if (e.target === overlay) {
					document.body.removeChild(overlay);
				}
			};

			document.body.appendChild(overlay);
		}
	</script>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
</body>

</html>