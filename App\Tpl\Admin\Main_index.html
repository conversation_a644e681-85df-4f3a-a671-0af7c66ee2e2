
<style>
body{
    background-color: #f2f2f2;
}
.layuiadmin-span-color {
    font-size: 14px
}

.layuiadmin-span-color i {
    padding-left: 5px
}
.layuiadmin-badge,.layuiadmin-btn-group,.layuiadmin-span-color {
    position: absolute;
    right: 15px
}

.layuiadmin-card-list p.layuiadmin-big-font {
    font-size: 36px;
    color: #666;
    line-height: 36px;
    padding: 5px 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: nowrap
}
</style>
<div id="douApi"></div>
<div class="indexBox layui-col-space15">
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#EE0000"><b>今日注册用户数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
               <center><p class="layuiadmin-big-font"><font color="#EE0000"><b>{$data.dayRegNum|num2str}</b></font></p></center> 
                <p>
                    <font color="#D2691E"><b>总注册用户数量</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.sumRegNum|num2str}</b><i class="layui-inline layui-icon layui-icon-flag"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#9A32CD"><b>今日借款申请订单数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#9A32CD"><b>{$data.dayLoanNum|num2str}</b></font></p></center> 
                <p>
                    <font color="#008B45"><b>总借款申请订单数量</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.sumLoanNum|num2str}</b><i class="layui-inline layui-icon layui-icon-face-smile-b"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#FFC125"><b>今日放款订单数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">

                <center><p class="layuiadmin-big-font"><b><font color="FFC125">{$data.dayAgreeOrderNum|num2str}</b></p></font></center>
                <p>
                    <b><font color="0000EE">总放款订单数量</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.sumAgreeOrderNum|num2str}</b><i class="layui-inline layui-icon layui-icon-face-smile-b"></i></span>
                </p>
            </div>
        </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#F08080"><b>今日借款金额（约）</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">

                <center><p class="layuiadmin-big-font"><b><font color="#F08080">{$data.dayAgreeOrderMoney|num2str}</b></font></p></center>
                <p>
                    <b><font color="CDAD00">总借款金额（约）</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.sumAgreeOrderMoney|num2str}</b><i class="layui-inline layui-icon layui-icon-dollar"></i></span>
                </p>
            </div>
        </div>
    </div>

    <!-- 新增通信录统计卡片 -->
    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#20B2AA"><b>今日登录用户数量</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#20B2AA"><b>{$data.todayLoginNum|default='0'}</b></font></p></center>
                <p>
                    <font color="#4169E1"><b>总登录次数</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.totalLoginNum|default='0'}</b><i class="layui-inline layui-icon layui-icon-user"></i></span>
                </p>
            </div>
        </div>
    </div>

    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#FF6347"><b>通信录读取成功数</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#FF6347"><b>{$data.contactsSuccessNum|default='0'}</b></font></p></center>
                <p>
                    <font color="#32CD32"><b>通信录总记录数</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.totalContactsNum|default='0'}</b><i class="layui-inline layui-icon layui-icon-cellphone"></i></span>
                </p>
            </div>
        </div>
    </div>

    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#9370DB"><b>APP登录用户数</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#9370DB"><b>{$data.appLoginNum|default='0'}</b></font></p></center>
                <p>
                    <font color="#FF8C00"><b>独立用户数</b></font>
                    <span class="layuiadmin-span-color"><b>{$data.uniqueUsersNum|default='0'}</b><i class="layui-inline layui-icon layui-icon-app"></i></span>
                </p>
            </div>
        </div>
    </div>

    <div class="layui-col-sm6 layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">
                <center><font color="#DC143C"><b>通信录成功率</b></font></center>
            </div>
            <div class="layui-card-body layuiadmin-card-list">
                <center><p class="layuiadmin-big-font"><font color="#DC143C"><b>{$data.contactsSuccessRate|default='0%'}</b></font></p></center>
                <p>
                    <font color="#228B22"><b>系统状态</b></font>
                    <span class="layuiadmin-span-color"><b>正常运行</b><i class="layui-inline layui-icon layui-icon-ok-circle"></i></span>
                </p>
            </div>
        </div>
    </div>

    </ul>
</div>

<table width="100%" height="400px" border="0" cellspacing="0" cellpadding="0" class="indexBoxTwo">
    <tr>
        <td width="50%" valign="top" class="pr">
            <div id="container" style="height: 100%"></div>
        </td>
        <td valign="top" class="pl">
            <div id="container1" style="height: 100%"></div>
        </td>
    </tr>
</table>




<table width="100%" border="0" cellspacing="0" cellpadding="0" class="indexBoxTwo">
    <tr>
        <td width="50%" valign="top" class="pr">
            <div class="indexBox">

                <ul>
                    <table class="layui-table" lay-size="lg">
                        <colgroup>
                            <col width="150">
                            <col width="200">
                            <col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th><center><b><font color="0000EE">用户名</b></font></center></th>
                            <th><center><b><font color="0000EE">最后登录时间</b></font></center></th>
                        </tr>
                        </thead>
                        <tbody>
                        <foreach name="frontData" item="vo">
                            <tr>
                                <td><center><b><font color="red">{$vo.phone}</b></font></center></td>
                               <td><center><b><font color="red">{$vo.last_time|date='Y/m/d H:i:s',###}</b></font></center></td>
                            </tr>
                        </foreach>
                        </tbody>
                    </table>
                </ul>
            </div>
        </td>
        <td valign="top" class="pl">
            <div class="indexBox">

                <ul>
                    <table class="layui-table" lay-size="lg">
                        <colgroup>
                            <col width="150">
                            <col width="200">
                            <col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th><center><b><font color="0000EE">后台登录IP</b></font></center></th>
                            <th><center><b><font color="0000EE">登录时间</b></font></center></th>
                        </tr>
                        </thead>
                        <tbody>
                        <foreach name="loginData" item="vo">
                            <tr>
                                <td><center><b><font color="red">{$vo.loginip}</b></font></center></td>
                                <td><center><b><font color="red">{$vo.logintime|date='Y/m/d H:i:s',###}</b></font></center></td>
                            </tr>
                        </foreach>
                        </tbody>
                    </table>
                </ul>
            </div>
        </td>
    </tr>
</table>
<script type="text/javascript">
    var dom = document.getElementById("container");
    var myChart = echarts.init(dom);
    var app = {};
    var arr1=[],arr2=[];
    function arrTest(){
        $.ajax({
            type:"post",
            async:false,
            url:"{:U(GROUP_NAME.'/Main/vipnum')}",
            data:{},
            dataType:"json",
            success:function(result){
                if (result) {
                    for (var i = 0; i < result.vipnum.length; i++) {
                        arr1.push(result.vipnum[i]);
                        arr2.push(result.date[i]);
                    }
                }
            }
        })
        return arr1,arr2;
    }
    /*    console.log(arr1);
        console.log(arr2);*/

    arrTest();
    option = {
        title: {
            text: '近7天会员注册情况'
        },
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: arr2
        },
        yAxis: {
            type: 'value'
        },
        series: [

            {
                name:'新增会员数',
                type:'line',
                stack: '总量',
                data:arr1
            }
        ]
    };
    ;
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
</script>
<script type="text/javascript">
    var dom = document.getElementById("container1");
    var myChart = echarts.init(dom);
    var app = {};
    var arr1=[],arr2=[],arr3 = [];
    function arrTest1(){
        $.ajax({
            type:"post",
            async:false,
            url:"{:U(GROUP_NAME.'/Main/orders')}",
            data:{},
            dataType:"json",
            success:function(result){
                //alert(result.date.length);
                if (result) {
                    for (var i = 0; i < result.date.length; i++) {
                        arr1.push(result.ordernum[i]);
                        arr2.push(result.date[i]);
                        arr3.push(result.vouchernum[i]);
                    }
                }
            }
        })
        return arr1,arr2,arr3;
    }
    console.log(arr3);
    console.log(arr1);
    arrTest1();
    option = {
        title: {
            text: '近7天订单情况'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data:['借款申请订单数','放款订单数']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: arr2
        },
        yAxis: {
            type: 'value'
        },
        series: [

            {
                name:'借款申请订单数',
                type:'line',
                stack: '总量',
                data:arr1
            },
            {
                name:'放款订单数',
                type:'line',
                stack: '总量',
                data:arr3
            }
        ]
    };
    ;
    if (option && typeof option === "object") {
        myChart.setOption(option, true);
    }
</script>

