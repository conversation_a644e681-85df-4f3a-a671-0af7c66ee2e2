# 优易花 - 安卓原生壳+H5混合开发项目

## 项目概述
这是一个安卓原生壳+H5内核的混合开发项目，旨在降低APK报毒率，同时保持完整的用户体验。

## 项目结构
```
安卓原生壳+H5/
├── android-shell/          # 安卓原生壳项目
│   ├── app/
│   ├── gradle/
│   ├── build.gradle
│   └── settings.gradle
├── h5-app/                  # H5应用项目
│   ├── index.html
│   ├── pages/
│   ├── assets/
│   └── js/
├── docs/                    # 项目文档
└── README.md               # 项目说明
```

## 技术架构
- **安卓壳**: 使用Android Studio开发，只包含WebView容器
- **H5内核**: 所有业务逻辑用HTML5+JavaScript实现
- **通信机制**: JavaScript Bridge实现原生与H5交互

## 开发环境
- Android Studio 2022.3+
- JDK 11+
- Android SDK 33+
- Node.js 16+ (用于H5开发)

## 预期效果
- 报毒率: 从70-80%降至5-15%
- APK大小: 约3-5MB
- 用户体验: 保持原生APP感觉
- 更新灵活: H5页面可随时更新

## 开发进度
- [ ] 安卓壳基础框架
- [ ] WebView配置
- [ ] H5页面开发
- [ ] JavaScript Bridge
- [ ] 功能测试
- [ ] 打包发布

## 联系信息
项目负责人: [您的姓名]
开发时间: 2025年7月
