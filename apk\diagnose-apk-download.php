<?php
/**
 * APK下载诊断工具
 * 用于检查APK文件和下载配置是否正常
 */

header('Content-Type: text/html; charset=UTF-8');

echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APK下载诊断 - 随意花</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .file-info { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
        .download-test { text-align: center; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 APK下载诊断工具</h1>
            <p>检查随意花APK文件和下载配置</p>
        </div>';

// 1. 检查APK文件
echo '<div class="section">';
echo '<h3>📱 APK文件检查</h3>';

$apk_file = 'app-release.apk';
$apk_path = __DIR__ . '/' . $apk_file;

if (file_exists($apk_path)) {
    $file_size = filesize($apk_path);
    $file_size_mb = round($file_size / 1024 / 1024, 2);
    $file_time = date('Y-m-d H:i:s', filemtime($apk_path));
    
    echo '<div class="success">';
    echo '<h4>✅ APK文件存在</h4>';
    echo '<div class="file-info">';
    echo '<strong>文件名：</strong>' . $apk_file . '<br>';
    echo '<strong>文件大小：</strong>' . number_format($file_size) . ' 字节 (' . $file_size_mb . ' MB)<br>';
    echo '<strong>修改时间：</strong>' . $file_time . '<br>';
    echo '<strong>文件路径：</strong>' . $apk_path;
    echo '</div>';
    echo '</div>';
    
    // 检查文件大小是否合理
    if ($file_size < 1024 * 1024) { // 小于1MB
        echo '<div class="warning">';
        echo '<h4>⚠️ 文件大小异常</h4>';
        echo '<p>APK文件大小只有 ' . $file_size_mb . ' MB，这可能不是一个完整的APK文件。</p>';
        echo '</div>';
    } elseif ($file_size > 100 * 1024 * 1024) { // 大于100MB
        echo '<div class="warning">';
        echo '<h4>⚠️ 文件过大</h4>';
        echo '<p>APK文件大小为 ' . $file_size_mb . ' MB，可能包含过多资源。</p>';
        echo '</div>';
    }
    
} else {
    echo '<div class="error">';
    echo '<h4>❌ APK文件不存在</h4>';
    echo '<p>文件路径：' . $apk_path . '</p>';
    echo '<p><strong>解决方案：</strong></p>';
    echo '<ul>';
    echo '<li>检查APK文件是否已上传到正确位置</li>';
    echo '<li>确认文件名是否为：' . $apk_file . '</li>';
    echo '<li>检查文件权限是否正确</li>';
    echo '</ul>';
    echo '</div>';
}

echo '</div>';

// 2. 检查文件权限
echo '<div class="section">';
echo '<h3>🔒 文件权限检查</h3>';

if (file_exists($apk_path)) {
    $perms = fileperms($apk_path);
    $perms_octal = substr(sprintf('%o', $perms), -4);
    
    if (is_readable($apk_path)) {
        echo '<div class="success">';
        echo '<h4>✅ 文件可读</h4>';
        echo '<p>文件权限：' . $perms_octal . '</p>';
        echo '</div>';
    } else {
        echo '<div class="error">';
        echo '<h4>❌ 文件不可读</h4>';
        echo '<p>文件权限：' . $perms_octal . '</p>';
        echo '<p>建议设置权限为：644</p>';
        echo '</div>';
    }
} else {
    echo '<div class="info">';
    echo '<p>文件不存在，无法检查权限</p>';
    echo '</div>';
}

echo '</div>';

// 3. 检查Web服务器配置
echo '<div class="section">';
echo '<h3>🌐 Web服务器配置检查</h3>';

// 检查MIME类型
$mime_type = '';
if (function_exists('mime_content_type') && file_exists($apk_path)) {
    $mime_type = mime_content_type($apk_path);
}

echo '<div class="info">';
echo '<h4>📋 服务器信息</h4>';
echo '<p><strong>服务器软件：</strong>' . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . '</p>';
echo '<p><strong>PHP版本：</strong>' . PHP_VERSION . '</p>';
echo '<p><strong>文档根目录：</strong>' . $_SERVER['DOCUMENT_ROOT'] . '</p>';
if ($mime_type) {
    echo '<p><strong>检测到的MIME类型：</strong>' . $mime_type . '</p>';
}
echo '</div>';

// 检查.htaccess配置
$htaccess_path = __DIR__ . '/.htaccess';
if (file_exists($htaccess_path)) {
    echo '<div class="success">';
    echo '<h4>✅ .htaccess文件存在</h4>';
    echo '</div>';
} else {
    echo '<div class="warning">';
    echo '<h4>⚠️ .htaccess文件不存在</h4>';
    echo '<p>建议创建.htaccess文件以确保APK文件正确的MIME类型：</p>';
    echo '<pre>AddType application/vnd.android.package-archive .apk</pre>';
    echo '</div>';
}

echo '</div>';

// 4. 下载测试
echo '<div class="section">';
echo '<h3>📥 下载测试</h3>';

if (file_exists($apk_path)) {
    $download_url = '/apk/' . $apk_file;
    echo '<div class="download-test">';
    echo '<p>点击下面的链接测试APK下载：</p>';
    echo '<a href="' . $download_url . '" class="btn" target="_blank">🔗 测试下载APK</a>';
    echo '<a href="../download.php" class="btn" target="_blank">📱 访问下载页面</a>';
    echo '</div>';
    
    echo '<div class="info">';
    echo '<h4>📋 下载链接信息</h4>';
    echo '<p><strong>直接下载链接：</strong>https://dailuanshej.cn' . $download_url . '</p>';
    echo '<p><strong>下载页面：</strong>https://dailuanshej.cn/download.php</p>';
    echo '</div>';
} else {
    echo '<div class="error">';
    echo '<p>APK文件不存在，无法进行下载测试</p>';
    echo '</div>';
}

echo '</div>';

// 5. 常见问题和解决方案
echo '<div class="section">';
echo '<h3>🛠️ 常见问题和解决方案</h3>';

echo '<div class="info">';
echo '<h4>📱 Android安装失败的常见原因：</h4>';
echo '<ol>';
echo '<li><strong>未知来源应用被禁用</strong><br>解决：设置 → 安全 → 允许安装未知来源应用</li>';
echo '<li><strong>APK文件损坏</strong><br>解决：重新下载APK文件</li>';
echo '<li><strong>存储空间不足</strong><br>解决：清理手机存储空间</li>';
echo '<li><strong>Android版本不兼容</strong><br>解决：确保Android版本7.0+</li>';
echo '<li><strong>应用签名问题</strong><br>解决：卸载旧版本后重新安装</li>';
echo '</ol>';
echo '</div>';

echo '<div class="info">';
echo '<h4>🌐 网页下载失败的常见原因：</h4>';
echo '<ol>';
echo '<li><strong>文件不存在或路径错误</strong><br>解决：检查APK文件是否在正确位置</li>';
echo '<li><strong>服务器权限问题</strong><br>解决：设置文件权限为644</li>';
echo '<li><strong>MIME类型配置错误</strong><br>解决：配置.htaccess文件</li>';
echo '<li><strong>浏览器缓存问题</strong><br>解决：清除浏览器缓存</li>';
echo '<li><strong>网络连接问题</strong><br>解决：检查网络连接</li>';
echo '</ol>';
echo '</div>';

echo '</div>';

// 6. 印章功能确认
echo '<div class="section">';
echo '<h3>🔴 印章功能确认</h3>';

echo '<div class="success">';
echo '<h4>✅ 当前APK包含的印章功能：</h4>';
echo '<ul>';
echo '<li>管理员可编辑印章参数（大小、位置、公司名称）</li>';
echo '<li>客户端可查看已应用的印章</li>';
echo '<li>印章默认位置：right: 100px, top: -170px</li>';
echo '<li>印章默认大小：100×100px</li>';
echo '<li>支持本地存储印章设置</li>';
echo '<li>SVG印章渲染</li>';
echo '<li>域名配置：https://dailuanshej.cn</li>';
echo '</ul>';
echo '</div>';

echo '</div>';

echo '<div class="section">';
echo '<p style="text-align: center; color: #666; font-size: 14px;">';
echo '诊断完成时间：' . date('Y-m-d H:i:s') . '<br>';
echo '如有问题请联系技术支持：<EMAIL>';
echo '</p>';
echo '</div>';

echo '</div>
</body>
</html>';
?>
