<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>📝 用户注册 - 小贷系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 40px 20px 30px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .logo {
            font-size: 60px;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            padding: 4px;
            margin: 0 20px 20px;
            position: relative;
            z-index: 1;
        }

        .nav-tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            border-radius: 20px;
            color: rgba(255,255,255,0.7);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 280px);
            padding: 30px 20px 40px;
            margin-top: -10px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 注册表单 */
        .register-form {
            max-width: 100%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #999;
            z-index: 1;
        }

        .form-input {
            width: 100%;
            padding: 16px 16px 16px 50px;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            border-radius: 12px;
        }

        .form-input::placeholder {
            color: #999;
        }

        /* 注册按钮 */
        .register-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }

        .register-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .register-button:hover::before {
            left: 100%;
        }

        .register-button:active {
            transform: scale(0.98);
        }

        .register-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 服务协议 */
        .agreement {
            text-align: center;
            margin: 20px 0;
            font-size: 12px;
            color: #666;
            line-height: 1.5;
        }

        .agreement a {
            color: #667eea;
            text-decoration: underline;
        }

        /* 登录链接 */
        .login-link {
            text-align: center;
            margin-top: 20px;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: #764ba2;
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            display: none;
            max-width: 300px;
            text-align: center;
        }

        .message.show {
            display: block;
            animation: fadeInOut 3s ease;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            10%, 90% { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 30px 15px 25px;
            }
            
            .main-content {
                padding: 25px 15px 35px;
            }
            
            .logo {
                font-size: 50px;
            }
            
            .app-title {
                font-size: 24px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">📝</div>
            <div class="app-title">用户注册</div>
            <div class="app-subtitle">创建您的账户，开始借贷之旅</div>
            
            <!-- 导航标签 -->
            <div class="nav-tabs">
                <div class="nav-tab" onclick="window.location.href='{:U('User/login')}'">登录</div>
                <div class="nav-tab active">注册</div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <form class="register-form" id="form-with-tooltip">
                <!-- 手机号输入 -->
                <div class="form-group">
                    <div class="input-wrapper">
                        <div class="input-icon">📱</div>
                        <input type="tel" 
                               name="phone" 
                               id="phone" 
                               class="form-input"
                               minlength="11" 
                               maxlength="11" 
                               placeholder="请输入手机号码" 
                               required/>
                    </div>
                </div>
                
                <!-- 密码输入 -->
                <div class="form-group">
                    <div class="input-wrapper">
                        <div class="input-icon">🔒</div>
                        <input type="password" 
                               name="password" 
                               id="password" 
                               class="form-input"
                               minlength="6" 
                               maxlength="15" 
                               placeholder="请设置登录密码" 
                               required/>
                    </div>
                </div>

                <!-- 隐藏的推荐码 -->
                <input type="hidden" name="referral" id="referral" value="10086"/>
                
                <!-- 服务协议 -->
                <div class="agreement">
                    注册即代表您已阅读并同意<br>
                    <a href="javascript:void(0)" class="zcfwxy">《注册服务协议》</a>
                </div>
                
                <!-- 注册按钮 -->
                <button type="button" id="register-button" class="register-button">
                    立即注册
                </button>
                
                <!-- 登录链接 -->
                <div class="login-link">
                    <a href="{:U('User/login')}">已有账户？返回登录</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="message" id="message">
        <p></p>
    </div>

    <script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
    <script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
    <script type="text/javascript" src="__PUBLIC__/home/<USER>/js/signup.js"></script>

    <script type="text/javascript">
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');
        });

        // 防止多点触控缩放
        document.documentElement.addEventListener('touchmove', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);

        // 输入框焦点效果
        document.querySelectorAll('.form-input').forEach(function(input) {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // 手机号格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 注册按钮点击效果
        document.getElementById('register-button').addEventListener('click', function() {
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;

            if (!phone) {
                showMessage('请输入手机号码');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showMessage('请输入正确的手机号码');
                return;
            }

            if (!password) {
                showMessage('请输入登录密码');
                return;
            }

            if (password.length < 6) {
                showMessage('密码长度不能少于6位');
                return;
            }

            // 显示加载状态
            this.disabled = true;
            this.innerHTML = '注册中...';

            // 调用原有的注册逻辑
            if (typeof doRegister === 'function') {
                doRegister();
            } else {
                // 如果原有函数不存在，使用AJAX提交
                submitRegister(phone, password);
            }
        });

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            const p = message.querySelector('p');
            p.textContent = text;
            message.className = 'message show';

            setTimeout(function() {
                message.className = 'message';
            }, 3000);
        }

        // AJAX注册提交
        function submitRegister(phone, password) {
            $.ajax({
                url: '{:U("User/dosignup")}',
                type: 'POST',
                data: {
                    phone: phone,
                    password: password,
                    referral: document.getElementById('referral').value
                },
                dataType: 'json',
                success: function(response) {
                    const registerBtn = document.getElementById('register-button');
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = '立即注册';

                    if (response.status == 1) {
                        showMessage('注册成功，正在跳转...', 'success');
                        setTimeout(function() {
                            window.location.href = response.url || '{:U("User/login")}';
                        }, 1000);
                    } else {
                        showMessage(response.info || '注册失败，请重试', 'error');
                    }
                },
                error: function() {
                    const registerBtn = document.getElementById('register-button');
                    registerBtn.disabled = false;
                    registerBtn.innerHTML = '立即注册';
                    showMessage('网络错误，请重试', 'error');
                }
            });
        }

        // 回车键注册
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('register-button').click();
            }
        });

        // 服务协议点击
        document.querySelector('.zcfwxy').addEventListener('click', function() {
            showMessage('服务协议功能待完善');
        });

        var sms_off = {:C('cfg_sms_off')};
    </script>

    <div style="display: none;">
        <Somnus:sitecfg name="sitecode" />
    </div>

</body>
</html>
