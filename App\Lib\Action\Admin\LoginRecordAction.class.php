<?php
/**
 * 后台用户登录记录管理控制器
 */
class LoginRecordAction extends CommonAction
{
    // 登录记录列表
    public function index()
    {
        $page = I('p', 1, 'intval');
        $pageSize = 20;
        
        // 搜索条件
        $where = array();
        $phone = I('phone', '', 'trim');
        if ($phone) {
            $where['phone'] = array('like', '%' . $phone . '%');
        }
        
        $startTime = I('start_time', '', 'trim');
        $endTime = I('end_time', '', 'trim');
        if ($startTime && $endTime) {
            $where['login_time'] = array('between', array(strtotime($startTime), strtotime($endTime . ' 23:59:59')));
        }
        
        // 获取数据
        $LoginModel = M('user_login_records');
        $count = $LoginModel->where($where)->count();
        $list = $LoginModel->where($where)
                          ->order('login_time DESC')
                          ->page($page, $pageSize)
                          ->select();
        
        // 处理数据
        foreach ($list as &$item) {
            $item['login_time_format'] = date('Y-m-d H:i:s', $item['login_time']);
            $item['time_ago'] = $this->timeAgo($item['login_time']);
            
            // 获取用户基本信息
            $userInfo = M('user')->where(array('phone' => $item['phone']))->find();
            if ($userInfo) {
                $item['user_status'] = $userInfo['status'];
                $item['reg_time'] = $userInfo['addtime'];
                $item['reg_time_format'] = date('Y-m-d H:i:s', $userInfo['addtime']);
            }
        }
        
        // 分页
        $Page = new \Think\Page($count, $pageSize);
        $show = $Page->show();
        
        // 实时统计
        $stats = $this->getLoginStats();
        
        $this->assign('list', $list);
        $this->assign('page', $show);
        $this->assign('count', $count);
        $this->assign('phone', $phone);
        $this->assign('start_time', $startTime);
        $this->assign('end_time', $endTime);
        $this->assign('stats', $stats);
        $this->display();
    }
    
    // 实时登录统计
    public function realtime()
    {
        $stats = $this->getLoginStats();
        $this->ajaxReturn(array('status' => 1, 'data' => $stats));
    }
    
    // 获取登录统计数据
    private function getLoginStats()
    {
        $LoginModel = M('user_login_records');
        
        // 今日登录
        $todayStart = strtotime('today');
        $todayEnd = strtotime('tomorrow') - 1;
        $todayLogins = $LoginModel->where(array('login_time' => array('between', array($todayStart, $todayEnd))))->count();
        $todayUniqueUsers = $LoginModel->where(array('login_time' => array('between', array($todayStart, $todayEnd))))->group('phone')->count();
        
        // 昨日登录
        $yesterdayStart = strtotime('yesterday');
        $yesterdayEnd = strtotime('today') - 1;
        $yesterdayLogins = $LoginModel->where(array('login_time' => array('between', array($yesterdayStart, $yesterdayEnd))))->count();
        
        // 本周登录
        $weekStart = strtotime('this week');
        $weekLogins = $LoginModel->where(array('login_time' => array('egt', $weekStart)))->count();
        
        // 本月登录
        $monthStart = strtotime('first day of this month');
        $monthLogins = $LoginModel->where(array('login_time' => array('egt', $monthStart)))->count();
        
        // 最近1小时登录
        $hourAgo = time() - 3600;
        $hourLogins = $LoginModel->where(array('login_time' => array('egt', $hourAgo)))->count();
        
        // 最近10分钟登录
        $tenMinAgo = time() - 600;
        $tenMinLogins = $LoginModel->where(array('login_time' => array('egt', $tenMinAgo)))->count();
        
        // 最近登录用户
        $recentLogins = $LoginModel->order('login_time DESC')->limit(10)->select();
        foreach ($recentLogins as &$login) {
            $login['login_time_format'] = date('Y-m-d H:i:s', $login['login_time']);
            $login['time_ago'] = $this->timeAgo($login['login_time']);
        }
        
        // 每小时登录统计（最近24小时）
        $hourlyStats = array();
        for ($i = 23; $i >= 0; $i--) {
            $hourStart = strtotime("-{$i} hours");
            $hourEnd = strtotime("-" . ($i-1) . " hours") - 1;
            $count = $LoginModel->where(array('login_time' => array('between', array($hourStart, $hourEnd))))->count();
            $hourlyStats[] = array(
                'hour' => date('H:i', $hourStart),
                'count' => $count
            );
        }
        
        return array(
            'today_logins' => $todayLogins,
            'today_unique_users' => $todayUniqueUsers,
            'yesterday_logins' => $yesterdayLogins,
            'week_logins' => $weekLogins,
            'month_logins' => $monthLogins,
            'hour_logins' => $hourLogins,
            'ten_min_logins' => $tenMinLogins,
            'recent_logins' => $recentLogins,
            'hourly_stats' => $hourlyStats,
            'update_time' => date('Y-m-d H:i:s')
        );
    }
    
    // 时间差计算
    private function timeAgo($timestamp)
    {
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return $diff . '秒前';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $timestamp);
        }
    }
    
    // 删除登录记录
    public function delete()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->ajaxReturn(array('status' => 0, 'msg' => '参数错误'));
        }
        
        $LoginModel = M('user_login_records');
        $result = $LoginModel->where(array('id' => $id))->delete();
        
        if ($result) {
            $this->ajaxReturn(array('status' => 1, 'msg' => '删除成功'));
        } else {
            $this->ajaxReturn(array('status' => 0, 'msg' => '删除失败'));
        }
    }
    
    // 清理旧记录
    public function cleanup()
    {
        $days = I('days', 30, 'intval');
        $cutoffTime = time() - ($days * 24 * 3600);
        
        $LoginModel = M('user_login_records');
        $count = $LoginModel->where(array('login_time' => array('lt', $cutoffTime)))->delete();
        
        $this->ajaxReturn(array('status' => 1, 'msg' => "成功清理 {$count} 条记录"));
    }
}
