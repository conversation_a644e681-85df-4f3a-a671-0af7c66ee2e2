# gradle-wrapper.jar 文件说明

这个文件是Gradle Wrapper的二进制文件，需要从Android Studio项目中复制或下载。

## 获取方式：

### 方法1：从Android Studio生成
1. 在Android Studio中创建新项目
2. 从新项目的 gradle/wrapper/ 目录复制 gradle-wrapper.jar
3. 将文件放到这个目录下

### 方法2：手动下载
1. 访问：https://services.gradle.org/distributions/
2. 下载对应版本的gradle-wrapper.jar
3. 将文件重命名为 gradle-wrapper.jar 并放到此目录

### 方法3：使用命令生成
在项目根目录执行：
```
gradle wrapper --gradle-version 8.0
```

## 重要提示：
- 这是一个二进制文件，无法用文本编辑器创建
- 文件大小通常在50-60KB左右
- 没有这个文件，gradlew命令无法执行

## 临时解决方案：
如果暂时没有这个文件，可以：
1. 直接在Android Studio中打开项目
2. Android Studio会自动下载并生成这个文件
3. 或者使用系统安装的gradle命令代替gradlew
