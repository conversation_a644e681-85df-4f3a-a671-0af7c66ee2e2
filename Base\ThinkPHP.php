<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2012 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

//   _______ _     _       _    _____  _    _ _____  
//  |__   __| |   (_)     | |  |  __ \| |  | |  __ \ 
//     | |  | |__  _ _ __ | | _| |__) | |__| | |__) |
//     | |  | '_ \| | '_ \| |/ /  ___/|  __  |  ___/ 
//     | |  | | | | | | | |   <| |    | |  | | |     
//     |_|  |_| |_|_|_| |_|_|\_\_|    |_|  |_|_|     
//
// ThinkPHP 框架入口文件

// 记录开始运行时间
$GLOBALS['_beginTime'] = microtime(TRUE);

// 版本信息
const THINK_VERSION = '3.2.3';

// URL 模式定义
const URL_COMMON     = 0; //普通模式
const URL_PATHINFO   = 1; //PATHINFO模式
const URL_REWRITE    = 2; //REWRITE模式
const URL_COMPAT     = 3; // 兼容模式

// 类文件后缀
const EXT = '.class.php';

// 系统常量定义
defined('THINK_PATH')   or define('THINK_PATH',     dirname(__FILE__).'/');
defined('APP_PATH')     or define('APP_PATH',       dirname($_SERVER['SCRIPT_FILENAME']).'/');
defined('APP_STATUS')   or define('APP_STATUS',     ''); // 应用状态 加载对应的配置文件
defined('APP_DEBUG')    or define('APP_DEBUG',      false); // 是否调试模式

if(function_exists('saeAutoLoader')){ // 如果是SAE环境
    defined('APP_MODE') or define('APP_MODE','sae');
    defined('STORAGE_TYPE') or define('STORAGE_TYPE','Sae');
}else{
    defined('APP_MODE') or define('APP_MODE','common'); // 应用模式 默认为普通模式    
    defined('STORAGE_TYPE') or define('STORAGE_TYPE','File'); // 存储类型 默认为File
}

defined('RUNTIME_PATH') or define('RUNTIME_PATH',   APP_PATH.'Runtime/'); // 系统运行时目录
defined('LIB_PATH')     or define('LIB_PATH',       THINK_PATH.'Lib/'); // 系统核心类库目录
defined('CORE_PATH')    or define('CORE_PATH',      LIB_PATH.'Core/'); // Think类库目录
defined('BEHAVIOR_PATH') or define('BEHAVIOR_PATH', LIB_PATH.'Behavior/'); // 行为类库目录
defined('MODE_PATH')    or define('MODE_PATH',      THINK_PATH.'Mode/'); // 系统应用模式目录
defined('VENDOR_PATH')  or define('VENDOR_PATH',    LIB_PATH.'Vendor/'); // 第三方类库目录
defined('COMMON_PATH')  or define('COMMON_PATH',    THINK_PATH.'Common/'); // 系统公共目录
defined('LANG_PATH')    or define('LANG_PATH',      THINK_PATH.'Lang/'); // 系统语言目录
defined('TMPL_PATH')    or define('TMPL_PATH',      THINK_PATH.'Tpl/'); // 系统模板目录
defined('HTML_PATH')    or define('HTML_PATH',      APP_PATH.'Html/'); // 应用静态目录
defined('LOG_PATH')     or define('LOG_PATH',       RUNTIME_PATH.'Logs/'); // 应用日志目录
defined('TEMP_PATH')    or define('TEMP_PATH',      RUNTIME_PATH.'Temp/'); // 应用缓存目录
defined('DATA_PATH')    or define('DATA_PATH',      RUNTIME_PATH.'Data/'); // 应用数据目录
defined('CACHE_PATH')   or define('CACHE_PATH',     RUNTIME_PATH.'Cache/'); // 应用模板缓存目录
defined('CONF_PATH')    or define('CONF_PATH',      APP_PATH.'Conf/'); // 应用配置目录
defined('CONF_EXT')     or define('CONF_EXT',       '.php'); // 配置文件后缀
defined('CONF_PARSE')   or define('CONF_PARSE',     '');    // 配置文件解析方法

// 系统信息
if(version_compare(PHP_VERSION,'5.3.0','<'))  die('require PHP > 5.3.0 !');

// 环境常量
define('IS_CGI',substr(PHP_SAPI, 0,3)=='cgi' ? 1 : 0 );
define('IS_WIN',strstr(PHP_OS, 'WIN') ? 1 : 0 );
define('IS_CLI',PHP_SAPI=='cli'? 1   : 0);

if(!IS_CLI) {
    // 当前文件名
    if(!defined('_PHP_FILE_')) {
        if(IS_CGI) {
            //CGI/FASTCGI模式下
            $_temp  = explode('.php',$_SERVER['PHP_SELF']);
            define('_PHP_FILE_',    rtrim(str_replace($_SERVER['HTTP_HOST'],'',$_temp[0].'.php'),'/'));
        }else {
            define('_PHP_FILE_',    rtrim($_SERVER['SCRIPT_NAME'],'/'));
        }
    }
    if(!defined('__ROOT__')) {
        $_root  =   rtrim(dirname(_PHP_FILE_),'/');
        define('__ROOT__',  (($_root=='/' || $_root=='\\')?'':$_root));
    }
}

// 先加载公共函数（避免重复加载）
if (!function_exists('C')) {
    if(file_exists(THINK_PATH.'Common/common.php')) {
        require THINK_PATH.'Common/common.php';
    }
}

if (!function_exists('halt')) {
    if(file_exists(THINK_PATH.'Common/functions.php')) {
        require THINK_PATH.'Common/functions.php';
    }
}

// 加载核心Think类
require CORE_PATH.'Think'.EXT;

// 应用程序初始化
Think::start();
?>
