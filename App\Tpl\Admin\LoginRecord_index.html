<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录记录管理 - 后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__PUBLIC__/admin/layui/css/layui.css" media="all">
    <style>
        .login-stats { margin-bottom: 20px; }
        .stat-card { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; position: relative; }
        .stat-number { font-size: 24px; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .stat-realtime { position: absolute; top: 10px; right: 10px; width: 8px; height: 8px; background: #52c41a; border-radius: 50%; animation: pulse 2s infinite; }
        .search-form { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .table-container { background: #fff; border-radius: 8px; overflow: hidden; }
        .recent-logins { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .login-item { padding: 10px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; }
        .login-item:last-child { border-bottom: none; }
        .phone-number { font-weight: bold; color: #1890ff; }
        .time-ago { color: #999; font-size: 12px; }
        .new-user-badge { background: #52c41a; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <!-- 实时统计卡片 -->
        <div class="login-stats">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-realtime"></div>
                        <div class="stat-number" id="tenMinLogins">-</div>
                        <div class="stat-label">10分钟内</div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-number" id="hourLogins">-</div>
                        <div class="stat-label">1小时内</div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-number" id="todayLogins">-</div>
                        <div class="stat-label">今日登录</div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-number" id="todayUsers">-</div>
                        <div class="stat-label">今日用户</div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-number" id="weekLogins">-</div>
                        <div class="stat-label">本周登录</div>
                    </div>
                </div>
                <div class="layui-col-md2">
                    <div class="stat-card">
                        <div class="stat-number" id="updateTime">-</div>
                        <div class="stat-label">更新时间</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space15">
            <!-- 左侧：最近登录 -->
            <div class="layui-col-md4">
                <div class="recent-logins">
                    <h3 style="margin-top: 0;">
                        <i class="layui-icon layui-icon-user"></i> 最近登录用户
                        <span style="float: right; font-size: 12px; color: #999;" id="recentUpdateTime">-</span>
                    </h3>
                    <div id="recentLoginsList">
                        <div style="text-align: center; color: #999; padding: 20px;">加载中...</div>
                    </div>
                </div>
            </div>

            <!-- 右侧：搜索和列表 -->
            <div class="layui-col-md8">
                <!-- 搜索表单 -->
                <div class="search-form">
                    <form class="layui-form" action="{:U('LoginRecord/index')}" method="get">
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md4">
                                <input type="text" name="phone" value="{$phone}" placeholder="请输入手机号" class="layui-input">
                            </div>
                            <div class="layui-col-md3">
                                <input type="text" name="start_time" value="{$start_time}" placeholder="开始时间" class="layui-input" id="start_time">
                            </div>
                            <div class="layui-col-md3">
                                <input type="text" name="end_time" value="{$end_time}" placeholder="结束时间" class="layui-input" id="end_time">
                            </div>
                            <div class="layui-col-md2">
                                <button type="submit" class="layui-btn layui-btn-sm">
                                    <i class="layui-icon layui-icon-search"></i>
                                </button>
                                <a href="{:U('LoginRecord/index')}" class="layui-btn layui-btn-primary layui-btn-sm">重置</a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="layui-table" lay-size="sm">
                        <thead>
                            <tr>
                                <th>手机号</th>
                                <th>登录时间</th>
                                <th>IP地址</th>
                                <th>通信录数</th>
                                <th>新用户</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <volist name="list" id="item">
                            <tr>
                                <td>
                                    <span class="phone-number">{$item.phone}</span>
                                </td>
                                <td>
                                    <div>{$item.login_time_format}</div>
                                    <div class="time-ago">{$item.time_ago}</div>
                                </td>
                                <td>{$item.ip_address|default='未知'}</td>
                                <td>
                                    <span class="layui-badge layui-bg-blue">{$item.contacts_count}</span>
                                </td>
                                <td>
                                    <if condition="$item['is_new_user'] eq 1">
                                        <span class="new-user-badge">新用户</span>
                                    <else/>
                                        <span style="color: #999;">老用户</span>
                                    </if>
                                </td>
                                <td>
                                    <a href="javascript:;" onclick="viewDetails('{$item.phone}')" class="layui-btn layui-btn-xs">
                                        <i class="layui-icon layui-icon-search"></i> 详情
                                    </a>
                                </td>
                            </tr>
                            </volist>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div style="padding: 20px; text-align: center;">
                        {$page}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="__PUBLIC__/admin/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'laydate'], function(){
            var layer = layui.layer;
            var laydate = layui.laydate;

            // 日期选择器
            laydate.render({
                elem: '#start_time',
                type: 'datetime'
            });
            
            laydate.render({
                elem: '#end_time',
                type: 'datetime'
            });

            // 加载实时统计数据
            loadRealtimeStats();
            
            // 每10秒刷新一次实时数据
            setInterval(loadRealtimeStats, 10000);
        });

        // 加载实时统计数据
        function loadRealtimeStats() {
            $.get('{:U("LoginRecord/realtime")}', function(data) {
                if (data.status == 1 && data.data) {
                    var stats = data.data;
                    $('#tenMinLogins').text(stats.ten_min_logins || 0);
                    $('#hourLogins').text(stats.hour_logins || 0);
                    $('#todayLogins').text(stats.today_logins || 0);
                    $('#todayUsers').text(stats.today_unique_users || 0);
                    $('#weekLogins').text(stats.week_logins || 0);
                    $('#updateTime').text(new Date().toLocaleTimeString());
                    
                    // 更新最近登录列表
                    updateRecentLogins(stats.recent_logins || []);
                    $('#recentUpdateTime').text(new Date().toLocaleTimeString());
                }
            }, 'json').fail(function() {
                console.log('实时数据加载失败');
            });
        }

        // 更新最近登录列表
        function updateRecentLogins(recentLogins) {
            var html = '';
            if (recentLogins.length > 0) {
                for (var i = 0; i < Math.min(recentLogins.length, 8); i++) {
                    var login = recentLogins[i];
                    var newUserBadge = login.is_new_user == 1 ? '<span class="new-user-badge">新</span>' : '';
                    html += '<div class="login-item">';
                    html += '<div>';
                    html += '<span class="phone-number">' + login.phone + '</span> ' + newUserBadge;
                    html += '<div class="time-ago">' + login.time_ago + '</div>';
                    html += '</div>';
                    html += '<div style="text-align: right;">';
                    html += '<div style="color: #1890ff;">' + login.contacts_count + '个联系人</div>';
                    html += '<div class="time-ago">' + login.ip_address + '</div>';
                    html += '</div>';
                    html += '</div>';
                }
            } else {
                html = '<div style="text-align: center; color: #999; padding: 20px;">暂无登录记录</div>';
            }
            $('#recentLoginsList').html(html);
        }

        // 查看用户详情
        function viewDetails(phone) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                layer.open({
                    type: 2,
                    title: '用户详情 - ' + phone,
                    shadeClose: true,
                    shade: 0.8,
                    area: ['80%', '80%'],
                    content: '{:U("Contacts/detail")}?phone=' + phone
                });
            });
        }
    </script>
</body>
</html>
