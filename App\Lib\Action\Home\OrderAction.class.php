<?php
class OrderAction extends CommonAction{

    public function checkorder(){
        $data = array('status' => 0,'msg' => '未知错误!');
        if(!$this->getLoginUser()){
            $data['status'] = 1;
        }else{
            $day = $this->yesdaikuan($this->getLoginUser());
            if(!$day){
                $data['status'] = 1;
            }else{
                $data['msg'] = "您最近一次订单审核失败,请".$day."天后再次提交!";
            }
        }
        $this->ajaxReturn($data);
    }

    private function yesdaikuan($user){
        //先查找最近一次失败订单
        $Order = D("order");
        $info = $Order->where(array('user' => $this->getLoginUser()))->order("addtime Desc")->find();
        if(!$info){
            return 0;
        }
        if($info['status'] != '-1'){
            return 0;
        }
        $tmptime = $info['addtime'];
        $tmptime = time()-$tmptime;
        $tmptime = $tmptime/(24*60*60);
        $disdkdleyday = C("cfg_disdkdleyday");
        if(!$disdkdleyday) $disdkdleyday = 30;
        if($tmptime > $disdkdleyday){
            return 0;
        }
        return ceil($disdkdleyday-$tmptime);
    }

    public function daikuan(){
    	
        if(!$this->getLoginUser()){
            $this->redirect('User/login');
        }
        $Userinfo = D("userinfo");
        $info = $Userinfo->where(array('user' => $this->getLoginUser()))->find();

        if(!$info){
            $this->redirect('Info/index');
        }
        foreach($info as $key => $value){
            if($value == ''){
                $this->redirect('Info/index');
            }
        }
        //判断用户最近一次失败订单是否超过预期时间
        $yesdaikuan = $this->yesdaikuan($this->getLoginUser());
        if($yesdaikuan){
            $this->redirect('Index/index');
        }

        $money = I("money",0,'trim');
        $month = I("month",0,'trim');
        $money = (float)$money;
        $month = (int)$month;
        $dismonths = C("cfg_dkmonths");
        $dismonths = explode(",",$dismonths);
        $fuwufei = C('cfg_fuwufei');
        $fuwufei = explode(",",$fuwufei);
        
        if($money > C('cfg_maxmoney') || $money < C('cfg_minmoney')){
            //借款金额不允许
            $this->redirect('Index/index');
        }
        if(!in_array($month,$dismonths)){
            //不允许的分期月
            $this->redirect('Index/index');
        }
        
        $rixi = round($fuwufei[$month-1] / 30,2);

        //优惠券
        $users = D('user')->where(array('phone'=>$this->getLoginUser()))->find();
        if($users['Discount'] == 1 && $users['Discount_date'] > date("Y/m/d")){
            $fuwufei = 0;
        }else{
            $fuwufei = $fuwufei[$month-1] * $money / 100;
        }
        $order = array(
            'money'   => $money,
            'fuwufei' => $fuwufei,
            'month'   => $month,
            'huankuan'=> ceil((float)($money/$month)),
            'bank'	  => $info['bankname'],
            'banknum' => $info['bankcard'],
            'rixi'	  => $rixi
        );
        $ordernum = neworderNum();
        $Order = D("order");
        $channelInfo = D("admin")->where(array('link'=>$_SERVER['HTTP_HOST']))->find();
        //获取预设状态值
        $ConfigModel = D("config");
        $config = $ConfigModel->where(array('id' => 1))->find();

        $arr = array(
            'user' => $this->getLoginUser(),
            'money' => $money,
            'months' => $month,
            'monthmoney' => ceil($order['huankuan']+$order['fuwufei']),
            'donemonth' => 0,
            'addtime' => time(),
            'status' => 0,
            'pid' => 0,
            'bank' => $info['bankname'],
            'banknum' => $info['bankcard'],
            'ordernum' => $ordernum,
            'pending' => $config['status'],
            'error' => $config['des'],
            'pending_time' => time(),
            'color' => '3ed050'
        );

        //查询用户是否已经提交了借款订单，如果存在则提示已经借款无法再次借款
        $count = $Order->where(array('user'=>$this->getLoginUser()))->count();
        if($count==0){
            $status = $Order->add($arr);
            if(!$status){
                $data['msg'] = '创建订单失败!';
            }else{
                $data['status'] = 1;
                $data['payurl'] = U('Order/lists');
            }
        }else{
            $data['msg'] = '您已经有一笔借款正在处理中，请勿重复提交!';
        }
        $this->ajaxReturn($data);
        exit;
        /*$addorder = I("get.trueorder",0,'trim');
        if($addorder){
            $data = array('status' => 0,'msg' => '未知错误','payurl' => '');
            //创建订单
            $ordernum = neworderNum();
            $arr = array(
                'ordernum' => $ordernum,
                'type'	   => '审核费',
                'money'	   => C('cfg_shenhefei'),
                'addtime'  => time(),
                'status'   => 0,
                'user'	   => $this->getLoginUser()
            );
            $Payorder = D("payorder");
            $status = $Payorder->add($arr);
            if(!$status){
                $data['msg'] = '创建订单失败!';
            }else{

            }
            $this->ajaxReturn($data);
            exit;
        }else{
            $this->order = $order;
            $this->display();
        }*/
    }
    public function vip(){
        if(!$this->getLoginUser()){
            $this->redirect('User/login');
        }
        $Userinfo = D("userinfo");
        $info = $Userinfo->where(array('user' => $this->getLoginUser()))->find();
        if(!$info){
            $this->redirect('Info/index');
        }
        foreach($info as $key => $value){
            if($value == '' && $key != "dwphone"){
                $this->redirect('Info/index');
            }
        }
        //判断用户最近一次失败订单是否超过预期时间
        $yesdaikuan = $this->yesdaikuan($this->getLoginUser());
        if($yesdaikuan){
            $this->redirect('Index/index');
        }

        $data = array('status' => 0,'msg' => '未知错误','payurl' => '');
        //创建订单
        $ordernum = neworderNum();
        $arr = array(
            'ordernum' => $ordernum,
            'type'	   => 'VIP费用',
            'money'	   => '198',
            'addtime'  => time(),
            'status'   => 0,
            'user'	   => $this->getLoginUser()
        );
        $Payorder = D("payorder");
        $status = $Payorder->add($arr);
        if(!$status){
            $data['msg'] = '创建订单失败!';
        }else{
            $data['msg'] = '升级VIP订单生成!';
            $data['payurl'] = U('yhb',array('ordernum' => $ordernum));
        }
        $this->redirect($data['payurl']);
        exit;
    }

    public function yhb(){
        $pay_memberid = "10002";   //商户ID
        $pay_orderid = I("ordernum",0,'trim');    //订单号
        $pay_amount = "198.00";    //交易金额
        $pay_applydate = date("Y-m-d H:i:s");  //订单时间
        $pay_notifyurl = "http://".$_SERVER['HTTP_HOST']."/yhb/server.php";   //服务端返回地址
        $pay_callbackurl = "http://".$_SERVER['HTTP_HOST']."/yhb/page.php";  //页面跳转返回地址
        $Md5key = "t4ig5acnpx4fet4zapshjacjd9o4bhbi";   //密钥
        $tjurl = "http://www.yinhuibaopay.com/Pay_Index.html";   //提交地址
        $pay_bankcode = "912";   //银行编码
        //扫码
        $native = array(
            "pay_memberid" => $pay_memberid,
            "pay_orderid" => $pay_orderid,
            "pay_amount" => $pay_amount,
            "pay_applydate" => $pay_applydate,
            "pay_bankcode" => $pay_bankcode,
            "pay_notifyurl" => $pay_notifyurl,
            "pay_callbackurl" => $pay_callbackurl,
        );
        ksort($native);
        $md5str = "";
        foreach ($native as $key => $val) {
            $md5str = $md5str . $key . "=" . $val . "&";
        }
        //echo($md5str . "key=" . $Md5key);
        $sign = strtoupper(md5($md5str . "key=" . $Md5key));
        $native["pay_md5sign"] = $sign;
        $native['pay_attach'] = "1234|456";
        $native['pay_productname'] ='VIP基础服务';
        $code  = "\n<body onLoad='document.dinpayForm.submit();'><form action=".$tjurl." method='post' name='dinpayForm' target='_self'>\n";
        foreach ($native as $key => $val) {
            $code .= '<input type="hidden" name="' . $key . '" value="' . $val . '">';
        }
        $code .= "</form></body>\n";
        echo $code;
    }
    public function lists(){
        $Order = D("order");
        $Contract = D('contract');
        $user = $this->getLoginUser();
        if(!$user){
            $this->redirect('User/login');
        }
        $data = $Order->where(array('user' => $user))->order("addtime Desc")->select();
        $userinfo = D("userinfo")->where(array('user' => $user))->find();
        $this->data = $data;
        $js=$this->userinfo = $userinfo;
        $this->assign('yhkh',substr($js['bankcard'],-4));
        $order = $Order->where(array('user' => $user))->find();
        $datas = $Contract->where(array('id'=>1))->find();
        $datas = $datas['contract'];
        $sign = '<img src=\'' . $userinfo['signature'] . '\' width=\'110px\' />';
        $fuwufei = C('cfg_fuwufei');
        $fuwufei = explode(",",$fuwufei);
        $rixi = round($fuwufei[$order['months']-1] / 30,2);
        $datas = str_replace('{ 合同编号 }' , $order['ordernum'] , $datas);
        $datas = str_replace('{ 合同日期 }' , date('Y-m-d h:i:s',$order['addtime']) , $datas);
        $datas = str_replace('{ 出借方 }' , $userinfo['name'] , $datas);
        $datas = str_replace('{ 借款方 }' , $userinfo['name'] , $datas);
        $datas = str_replace('{ 身份证号 }' , $userinfo['usercard'] , $datas);
        $datas = str_replace('{ 手机号码 }' , $order['user'] , $datas);
        $datas = str_replace('{ 个人签名 }' , $sign , $datas);
        $datas = str_replace('{ 借款日期 }' , date('Y-m-d h:i:s',$order['addtime']) , $datas);
        $datas = str_replace('{ 借款金额 }' , $order['money'] , $datas);
        $datas = str_replace('{ 借款期限 }' , $order['months'] , $datas);
        $datas = str_replace('{ 传人用户名 }' , $order['user'] , $datas);
        $datas = str_replace('{ 收款银行 }' , $order['bank'] , $datas);
        $datas = str_replace('{ 收款账号 }' , $order['banknum'] , $datas);
        $datas = str_replace('{ 收款姓名 }' , $userinfo['name'] , $datas);
        $datas = str_replace('{ 每期还款 }' , $order['monthmoney'] , $datas);
        $datas = str_replace('{ 日利率 }' , $rixi , $datas);
        $datas = str_replace('{ 还款日期 }' , date('d',$order['addtime']) , $datas);
        
        
        $this->rixi = $rixi;
        $this->datas = $datas;
        $this->order = $order;
        $this->display();
    }
    public function info(){
        $oid = I("oid",0,"trim");
        if(!$oid){
            $this->redirect('Order/lists');
        }
        $user = $this->getLoginUser();
        if(!$user){
            $this->redirect('User/login');
        }
        $name = D("userinfo")->where(array('user'=>$user))->find();

        $this->user = $name;
        $Order = D("order");
        $order = $Order->where(array('id' => $oid,'user' => $user))->find();
        if(!$order){
            $this->redirect('Order/lists');
        }
        $this->data = $order;
        $this->display();
    }

    //我的还款
    public function bills(){
        $where['user'] = $this->getLoginUser();
        $where['status'] = array('in','50');
        $data = M('order')->where($where)->order('id desc')->select();
        // var_dump($data);
        $this->data=$data;
        $this->display();
    }

    //还款
    public function billinfo(){
        $ordernum = I("ordernum",0,'trim');
        $where['user'] = $this->getLoginUser();
        $where['ordernum'] = $ordernum;
        $data = M('voucher')->where($where)->order('huantime asc')->select();

        // 获取还款账户信息
        $repayment_accounts = M('repayment_accounts')->where(array('is_active' => 1))->order('sort_order ASC, id DESC')->select();

        $this->data=$data;
        $this->time=date('Y-m-d');
        $this->repayment_accounts = $repayment_accounts;
        $this->display();
    }
    function qianbao() {

        $Userinfo = D("userinfo");
        $info = $Userinfo->where(array('user' => $this->getLoginUser()))->find();

        $Order = D("order");
        $order = $Order->where(array('user' => $this->getLoginUser()))->find();

        $this->assign("info",$info);
        $this->assign("order",$order);
        $this->display();

    }
    function shenqing() {
        $oid = I("oid",0,"trim");
        if(!$oid){
            $this->redirect('Order/lists');
        }
        $user = $this->getLoginUser();
        if(!$user){
            $this->redirect('User/login');
        }
        $name = D("userinfo")->where(array('user'=>$user))->find();
        $this->user = $name;
        $Order = D("order");
        $order = $Order->where(array('id' => $oid,'user' => $user))->find();
        if(!$order){
            $this->redirect('Order/lists');
        }
        $this->data = $order;
        $this->display();
    }
    function qianhetong () {
        $oid = I("oid",0,"trim");
        if(!$oid){
            $this->redirect('Order/lists');
        }
        $user = $this->getLoginUser();
        if(!$user){
            $this->redirect('User/login');
        }
        $name = D("userinfo")->where(array('user'=>$user))->find();
        $this->user = $name;
        $Order = D("order");
        $order = $Order->where(array('id' => $oid,'user' => $user))->find();
        if(!$order){
            $this->redirect('Order/lists');
        }
        $this->data = $order;
        $this->display();

    }

    //缴纳费用
    public function repay(){
        $data= I('get.');
        //type 1:为验资费，2：还款
        $msg = '支付完成后,请点击我已支付,上传支付凭证';

        if($data['type']==1){
            $msg = '验证您的还款能力,该笔资金将与您的贷款金额一起下放至您的银行账户,支付完成后,请点击我已支付,上传支付凭证';
        }


        $this->assign('data',$data);
        $this->assign('msg',$msg);
        $this->display();
    }


    //跳转凭证
    public function voucher(){

        $data = I("get.");
        if($data['type']==2){
            $img = M('voucher')->field('zfimg')->where(array('id'=>$data['id']))->find();
            $data['img'] = $img['zfimg'];
        }else{
            $img = M('order')->field('yzpz_img')->where(array('id'=>$data['id']))->find();
            $data['img'] = $img['yzpz_img'];
        }
        $data['img'] = $img['zfimg'];
        $this->data=$data;

        $this->display();
    }


    //保存凭证
    public function savevoucher(){
        $id = I("id",0,'trim');
        $img = I("img",'','trim');
        $type = I("type",'','trim');
        $ordernum = I("ordernum",'','trim');
        $data = array('status' => 0,'msg' => '未知错误');
        $time = date('Y-m-d');
        ///	dump($id);dump($img);dump($type);exit;
        if(!$id || !$type || !$img){
            $data['msg'] = "参数错误!";
        }else{
            if($type==2){
                $res = M('voucher')->where(array('id' => $id))->save(array('zfimg' => $img,'paytime'=>$time));
                $url = U('Order/billinfo',array('ordernum' =>$ordernum));
            }else{
                $res = M('order')->where(array('id' => $id))->save(array('yzpz_img' => $img,'status'=>14));
                $url = U('Order/info',array('oid' =>$id));
            }

            if(!$res){
                $data['msg'] = "操作失败!";
            }else{
                $data['status'] = 1;
                $data['payurl'] = $url;
            }
        }
        $this->ajaxReturn($data);
    }
}
