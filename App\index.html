<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>优易花 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: white;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            background: rgba(255,255,255,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        .content {
            flex: 1;
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }
        
        .feature-card h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .feature-card p {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .app-info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
        }
        
        .test-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        @media (max-width: 480px) {
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌟 优易花APP</h1>
        <p>安卓原生壳+H5混合应用</p>
    </div>

    <div class="content">
        <div class="feature-grid">
            <div class="feature-card" onclick="navigateToLogin()">
                <div class="feature-icon">🔐</div>
                <h3>用户登录</h3>
                <p>安全登录系统</p>
            </div>

            <div class="feature-card" onclick="testFunction('业务服务')">
                <div class="feature-icon">💼</div>
                <h3>业务服务</h3>
                <p>查看业务信息</p>
            </div>

            <div class="feature-card" onclick="testFunction('个人中心')">
                <div class="feature-icon">👤</div>
                <h3>个人中心</h3>
                <p>管理个人信息</p>
            </div>

            <div class="feature-card" onclick="testFunction('联系我们')">
                <div class="feature-icon">📞</div>
                <h3>联系我们</h3>
                <p>获取帮助支持</p>
            </div>
        </div>

        <div class="app-info">
            <h3>🚀 应用信息</h3>
            <p>版本：<span id="appVersion">1.0.0</span></p>
            <p>运行环境：<span id="environment">检测中...</span></p>
            
            <div style="margin-top: 15px;">
                <button class="test-btn" onclick="testToast()">测试Toast</button>
                <button class="test-btn" onclick="testShare()">测试分享</button>
                <button class="test-btn" onclick="goToOriginalSite()">访问原站</button>
            </div>
        </div>
    </div>

    <script>
        // 检测运行环境
        function detectEnvironment() {
            const envElement = document.getElementById('environment');
            const versionElement = document.getElementById('appVersion');
            
            if (typeof Android !== 'undefined') {
                envElement.textContent = '优易花APP';
                try {
                    const version = Android.getAppVersion();
                    versionElement.textContent = version;
                } catch (e) {
                    console.log('获取版本失败:', e);
                }
            } else {
                envElement.textContent = '浏览器';
            }
        }

        // 测试功能
        function testFunction(name) {
            showMessage('点击了：' + name);
        }

        // 测试Toast
        function testToast() {
            showMessage('这是一个测试消息');
        }

        // 测试分享
        function testShare() {
            const shareText = '我正在使用优易花APP，体验很不错！';
            
            if (typeof Android !== 'undefined') {
                try {
                    Android.shareText(shareText);
                } catch (e) {
                    showMessage('分享功能调用失败');
                }
            } else {
                if (navigator.share) {
                    navigator.share({
                        title: '优易花',
                        text: shareText,
                        url: window.location.href
                    });
                } else {
                    showMessage('浏览器不支持分享功能');
                }
            }
        }

        // 访问原站
        function goToOriginalSite() {
            window.open('https://dailuanshej.cn/', '_blank');
        }

        // 显示消息
        function showMessage(message) {
            if (typeof Android !== 'undefined') {
                try {
                    Android.showToast(message);
                } catch (e) {
                    alert(message);
                }
            } else {
                alert(message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectEnvironment();
            console.log('优易花H5应用加载完成');
        });
    </script>
</body>
</html>
