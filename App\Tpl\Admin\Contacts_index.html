<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通信录管理 - 后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__PUBLIC__/admin/layui/css/layui.css" media="all">
    <style>
        .contacts-stats { margin-bottom: 20px; }
        .stat-card { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .search-form { background: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .table-container { background: #fff; border-radius: 8px; overflow: hidden; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <!-- 统计卡片 -->
        <div class="contacts-stats">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-number">{$count}</div>
                        <div class="stat-label">总用户数</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-number" id="todayCount">-</div>
                        <div class="stat-label">今日新增</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-number" id="avgContacts">-</div>
                        <div class="stat-label">平均通信录数</div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="stat-card">
                        <div class="stat-number" id="updateTime">-</div>
                        <div class="stat-label">最后更新</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form class="layui-form" action="{:U('Contacts/index')}" method="get">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <input type="text" name="phone" value="{$phone}" placeholder="请输入手机号" class="layui-input">
                    </div>
                    <div class="layui-col-md3">
                        <input type="text" name="start_time" value="{$start_time}" placeholder="开始时间" class="layui-input" id="start_time">
                    </div>
                    <div class="layui-col-md3">
                        <input type="text" name="end_time" value="{$end_time}" placeholder="结束时间" class="layui-input" id="end_time">
                    </div>
                    <div class="layui-col-md3">
                        <button type="submit" class="layui-btn">
                            <i class="layui-icon layui-icon-search"></i> 搜索
                        </button>
                        <a href="{:U('Contacts/index')}" class="layui-btn layui-btn-primary">重置</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户手机号</th>
                        <th>通信录数量</th>
                        <th>有效联系人</th>
                        <th>IP地址</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <volist name="list" id="item">
                    <tr>
                        <td>{$item.id}</td>
                        <td>
                            <span style="font-weight: bold; color: #1890ff;">{$item.user_phone}</span>
                        </td>
                        <td>
                            <span class="layui-badge layui-bg-blue">{$item.contacts_count}</span>
                        </td>
                        <td>
                            <span class="layui-badge layui-bg-green">{$item.valid_contacts}</span>
                        </td>
                        <td>{$item.ip_address|default='未知'}</td>
                        <td>{$item.created_time_format}</td>
                        <td>{$item.updated_time_format}</td>
                        <td>
                            <a href="{:U('Contacts/detail', array('id' => $item['id']))}" class="layui-btn layui-btn-xs">
                                <i class="layui-icon layui-icon-search"></i> 查看详情
                            </a>
                            <a href="javascript:;" onclick="deleteRecord({$item.id})" class="layui-btn layui-btn-xs layui-btn-danger">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </a>
                        </td>
                    </tr>
                    </volist>
                </tbody>
            </table>

            <!-- 分页 -->
            <div style="padding: 20px; text-align: center;">
                {$page}
            </div>
        </div>
    </div>

    <script src="__PUBLIC__/admin/layui/layui.js"></script>
    <script>
        layui.use(['layer', 'laydate'], function(){
            var layer = layui.layer;
            var laydate = layui.laydate;

            // 日期选择器
            laydate.render({
                elem: '#start_time',
                type: 'date'
            });
            
            laydate.render({
                elem: '#end_time',
                type: 'date'
            });

            // 加载统计数据
            loadStats();
            
            // 每30秒刷新一次统计数据
            setInterval(loadStats, 30000);
        });

        // 加载统计数据
        function loadStats() {
            $.get('{:U("Contacts/stats")}', function(data) {
                if (data && data.stats) {
                    $('#todayCount').text(data.stats.today_users || 0);
                    $('#avgContacts').text(data.stats.avg_contacts || 0);
                    $('#updateTime').text(new Date().toLocaleTimeString());
                }
            }, 'json').fail(function() {
                console.log('统计数据加载失败');
            });
        }

        // 删除记录
        function deleteRecord(id) {
            layui.use('layer', function(){
                var layer = layui.layer;
                
                layer.confirm('确定要删除这条通信录记录吗？', {
                    btn: ['确定', '取消']
                }, function(index) {
                    $.post('{:U("Contacts/delete")}', {id: id}, function(data) {
                        if (data.status == 1) {
                            layer.msg('删除成功', {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(data.msg || '删除失败', {icon: 2});
                        }
                    }, 'json');
                    layer.close(index);
                });
            });
        }
    </script>
</body>
</html>
