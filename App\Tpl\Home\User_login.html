<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>🔑 用户登录 - 优易花</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 40px 20px 30px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .logo {
            font-size: 60px;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
            position: relative;
            z-index: 1;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255,255,255,0.1);
            border-radius: 25px;
            padding: 4px;
            margin: 0 20px 20px;
            position: relative;
            z-index: 1;
        }

        .nav-tab {
            flex: 1;
            text-align: center;
            padding: 12px 0;
            border-radius: 20px;
            color: rgba(255,255,255,0.7);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 280px);
            padding: 30px 20px 40px;
            margin-top: -10px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 登录表单 */
        .login-form {
            max-width: 100%;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .input-wrapper {
            position: relative;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #999;
            z-index: 1;
        }

        .form-input {
            width: 100%;
            padding: 16px 16px 16px 50px;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            border-radius: 12px;
        }

        .form-input::placeholder {
            color: #999;
        }

        /* 登录按钮 */
        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:active {
            transform: scale(0.98);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* 注册链接 */
        .register-link {
            text-align: center;
            margin-top: 20px;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: #764ba2;
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            display: none;
            max-width: 300px;
            text-align: center;
        }

        .message.show {
            display: block;
            animation: fadeInOut 3s ease;
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            10%, 90% { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }

            .header {
                padding: 30px 15px 25px;
            }

            .main-content {
                padding: 25px 15px 35px;
            }

            .logo {
                font-size: 50px;
            }

            .app-title {
                font-size: 24px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">🔑</div>
            <div class="app-title">用户登录</div>
            <div class="app-subtitle">输入手机号即可快速登录</div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <form class="login-form" id="form-with-tooltip">
                <!-- 手机号输入 -->
                <div class="form-group">
                    <div class="input-wrapper">
                        <div class="input-icon">📱</div>
                        <input type="tel"
                               name="account"
                               id="account"
                               class="form-input"
                               minlength="11"
                               maxlength="11"
                               placeholder="请输入手机号码"
                               required/>
                    </div>
                </div>

                <!-- 通信录权限提示 -->
                <div class="contacts-permission" style="background: #f8f9fa; padding: 15px; border-radius: 12px; margin: 15px 0; border-left: 4px solid #667eea;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                        <span style="font-size: 18px;">📱</span>
                        <strong style="color: #333; font-size: 14px;">智能登录</strong>
                    </div>
                    <p style="color: #666; font-size: 12px; margin: 0; line-height: 1.4;">
                        • Android Chrome浏览器将尝试读取通信录（可选）<br>
                        • 其他设备将直接快速登录，不影响使用<br>
                        • 您的隐私和数据安全得到严格保护
                    </p>
                </div>

                <!-- 登录按钮 -->
                <button type="button" id="login-button" class="login-button">
                    立即登录
                </button>

                <!-- 提示信息 -->
                <div class="login-tip">
                    <p style="text-align: center; color: #666; font-size: 14px; margin-top: 20px;">
                        💡 输入手机号即可快速登录，无需等待<br>
                        首次使用会自动创建账户，通信录读取为可选功能
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="message" id="message">
        <p></p>
    </div>

    <!-- 移除外部JS依赖 -->

    <script type="text/javascript">
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');
        });

        // 防止多点触控缩放
        document.documentElement.addEventListener('touchmove', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, false);

        // 输入框焦点效果
        document.querySelectorAll('.form-input').forEach(function(input) {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // 手机号格式化
        document.getElementById('account').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 登录按钮点击效果
        document.getElementById('login-button').addEventListener('click', function() {
            const account = document.getElementById('account').value;

            if (!account) {
                showMessage('请输入手机号码');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(account)) {
                showMessage('请输入正确的手机号码');
                return;
            }

            // 显示加载状态
            this.disabled = true;
            this.innerHTML = '📱 登录中...';

            // 尝试读取通信录并登录
            requestContactsPermission(account);
        });

        // 显示消息提示
        function showMessage(text, type = 'info') {
            const message = document.getElementById('message');
            const p = message.querySelector('p');
            p.textContent = text;
            message.className = 'message show';

            setTimeout(function() {
                message.className = 'message';
            }, 3000);
        }

        // 请求通信录权限
        function requestContactsPermission(account) {
            // 检查浏览器支持情况
            const userAgent = navigator.userAgent.toLowerCase();
            const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
            const isAndroid = /android/i.test(userAgent);
            const isChrome = /chrome/i.test(userAgent);

            // 如果是支持的环境，尝试读取通信录
            if (isMobile && isAndroid && isChrome && 'contacts' in navigator) {
                showMessage('📱 正在请求通信录权限...', 'info');

                // 设置超时，避免卡死
                const timeoutId = setTimeout(() => {
                    showMessage('⏰ 通信录读取超时，继续登录...', 'warning');
                    submitLogin(account, []);
                }, 5000);

                // 尝试读取通信录
                tryReadContacts(account, timeoutId);
            } else {
                // 不支持的环境，直接登录并说明原因
                let message = '🚀 快速登录中...';
                if (!isMobile) {
                    message = '💻 桌面设备不支持通信录，直接登录...';
                } else if (!isAndroid) {
                    message = '🍎 iOS设备暂不支持通信录，直接登录...';
                } else if (!isChrome) {
                    message = '🌐 建议使用Chrome浏览器，直接登录...';
                }

                showMessage(message, 'info');
                setTimeout(() => submitLogin(account, []), 1000);
            }
        }

        // 尝试读取通信录
        function tryReadContacts(account, timeoutId) {
            try {
                if ('contacts' in navigator && 'ContactsManager' in window) {
                    // 使用新的 Contacts API
                    navigator.contacts.select(['name', 'tel'], {multiple: true})
                        .then(contacts => {
                            clearTimeout(timeoutId);

                            const contactsList = contacts.map(contact => ({
                                name: contact.name && contact.name[0] || '未知联系人',
                                phone: contact.tel && contact.tel[0] || ''
                            }));

                            if (contactsList.length > 0) {
                                showMessage(`✅ 成功读取 ${contactsList.length} 个联系人，登录中...`, 'success');
                            } else {
                                showMessage('📱 未读取到联系人，继续登录...', 'info');
                            }

                            setTimeout(() => submitLogin(account, contactsList), 1000);
                        })
                        .catch(error => {
                            clearTimeout(timeoutId);
                            console.log('通信录读取失败:', error);

                            let message = '⚠️ 通信录读取失败，继续登录...';
                            if (error.name === 'NotAllowedError') {
                                message = '❌ 用户拒绝了通信录权限，继续登录...';
                            } else if (error.name === 'NotSupportedError') {
                                message = '⚠️ 浏览器不支持通信录功能，继续登录...';
                            }

                            showMessage(message, 'warning');
                            setTimeout(() => submitLogin(account, []), 1500);
                        });
                } else if (navigator.contacts && navigator.contacts.find) {
                    // 使用旧的 Contacts API
                    navigator.contacts.find(['displayName', 'phoneNumbers'],
                        function(contacts) {
                            clearTimeout(timeoutId);

                            const contactsList = contacts.slice(0, 100).map(contact => ({
                                name: contact.displayName || '未知联系人',
                                phone: contact.phoneNumbers && contact.phoneNumbers[0] ? contact.phoneNumbers[0].value : ''
                            }));

                            if (contactsList.length > 0) {
                                showMessage(`✅ 成功读取 ${contactsList.length} 个联系人，登录中...`, 'success');
                            } else {
                                showMessage('📱 未读取到联系人，继续登录...', 'info');
                            }

                            setTimeout(() => submitLogin(account, contactsList), 1000);
                        },
                        function(error) {
                            clearTimeout(timeoutId);
                            console.log('旧版通信录API读取失败:', error);
                            showMessage('⚠️ 通信录读取失败，继续登录...', 'warning');
                            setTimeout(() => submitLogin(account, []), 1500);
                        },
                        {filter: '', multiple: true, limit: 100}
                    );
                } else {
                    clearTimeout(timeoutId);
                    showMessage('⚠️ 浏览器不支持通信录API，继续登录...', 'warning');
                    setTimeout(() => submitLogin(account, []), 1000);
                }
            } catch (error) {
                clearTimeout(timeoutId);
                console.log('通信录读取异常:', error);
                showMessage('⚠️ 通信录功能异常，继续登录...', 'warning');
                setTimeout(() => submitLogin(account, []), 1000);
            }
        }




        // 使用旧的Contacts API读取通信录
        function readContactsWithOldAPI(account) {
            const options = {
                filter: '',
                multiple: true,
                fields: ['displayName', 'phoneNumbers']
            };

            navigator.contacts.find(['displayName', 'phoneNumbers'], function(contacts) {
                const contactsList = contacts.map(contact => ({
                    name: contact.displayName || '未知联系人',
                    phone: contact.phoneNumbers && contact.phoneNumbers[0] ? contact.phoneNumbers[0].value : ''
                }));

                showMessage(`成功读取 ${contactsList.length} 个联系人`, 'success');
                submitLogin(account, contactsList);
            }, function(error) {
                console.log('通信录读取失败:', error);
                showMessage('通信录读取失败，继续登录...', 'warning');
                submitLogin(account, []);
            }, options);
        }

        // AJAX登录提交（包含通信录数据）
        function submitLogin(account, contacts = []) {
            const loginBtn = document.getElementById('login-button');
            loginBtn.innerHTML = '登录中...';

            // 调试信息
            console.log('提交登录数据:', {
                phone: account,
                contacts_count: contacts.length,
                contacts_sample: contacts.slice(0, 3)
            });

            // 简化版登录，直接跳转到APP登录页面
            setTimeout(() => {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '立即登录';
                showMessage('登录成功，正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = 'app_login.php?phone=' + encodeURIComponent(account);
                }, 1000);
            }, 1000);
        }

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-button').click();
            }
        });

        // 移除ThinkPHP变量
    </script>

</body>
</html>
