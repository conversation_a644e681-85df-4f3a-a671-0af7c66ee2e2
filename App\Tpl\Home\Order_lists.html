<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>我的借款 - 小贷系统</title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/myloan.css">
</head>
<style>
	.coupon_zero {
		width: 100%;
		height: 100%;
		text-align: center;
		margin: 70px 0 0;
		color: #a8a6a7;
	}
</style>

<body>
	<div class="comm_top_nav" data-am-sticky>
		<div class="am-g">
			<b>
				<div class="am-u-sm-2" onclick="javascript:window.location.replace('{:U('User/index')}');"><i
						class="am-icon-angle-left am-icon-fw"></i></div>
				<div class="am-u-sm-8">我的借款</div>
				<div class="am-u-sm-2"></div>
			</b>
		</div>
	</div>
	<div style="display: none;" id="feilv">{:C('cfg_fuwufei')}</div>
	<empty name="data">
		<div class="coupon_zero">
			暂无订单
			<img src="__PUBLIC__/home/<USER>/image/noloan.png" alt="" width="100%">
		</div>
		<else />
		<foreach name="data" item="vo">
			<div class="loan">
				<div class="loan_list">
					<div class="am-g">
						<div class="am-u-sm-8">
							借款编号:<span class="f_number">{$vo.ordernum}</span>
						</div>
						<div class="am-u-sm-4 state">
							<span>
								{$vo.pending}
							</span>
						</div>
					</div>
					<div class="am-g loan_box">
						<div class="am-u-sm-4">
							<b class="f_number rll_number">{$rixi}</b>
							<span class="f_number rll_symbol">%</span>
							<br>
							<span class="loan_title">日利率</span>
						</div>
						<div class="am-u-sm-4">
							<b class="f_number jksj_number">{$vo.months}</b>
							<span class="jksj_symbol">个月</span>
							<br>
							<span class="loan_title">借款时间</span>
						</div>
						<div class="am-u-sm-4">
							<b class="f_number jkje_number">{$vo.money}</b>
							<span class="jkje_symbol">元</span>
							<br>
							<span class="loan_title">借款金额</span>
						</div>
					</div>

					<div class="am-g">
						<div class="am-u-sm-4">
							<span class="f_number" style="text-align: left; color: #c2c2c2;">{$vo.addtime|date='Y-m-d',
								###}</span>
						</div>
						<div class="am-u-sm-8 loan_btn">
							<span class="contract-show" data-am-modal="{target: '#my-popup-contract'}"
								data-ordernumber="{$vo.ordernum}">查看合同</span>
							<span class="loan-show" data-am-modal="{target: '#my-popup-loaninfo'}"
								data-ordernumber="{$vo.ordernum}" data-status="{$vo.status}" data-rll="0.02" data-addtime="{$vo.addtime|date='Y-m-d',
								###}">借款详情</span>
						</div>
					</div>
				</div>
			</div>
		</foreach>
	</empty>
	<div style="height: 20px;"></div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>
	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/myloan.js"></script>
	
</body>

</html>

<!-- 借款详情 -->
<div class="am-popup" id="my-popup-loaninfo">
	<div class="am-popup-hd">
		<h4 class="am-popup-title">借款详情</h4>
		<span data-am-modal-close class="am-close">&times;</span>
	</div>
	<div class="am-popup-inner">
		<div class="am-popup-bd" style="padding: 0;">
			<div class="am-g loan-info-title">
				<div class="am-u-sm-12">
					<span>•</span>
					<span>借款进度</span>
				</div>
			</div>
			<div class="loan">
				<div class="loan_list">
					<div class="am-g">
						<div class="f_number am-u-sm-8">
							编号:<span class="ordernumber">{$vo.ordernum}</span>
						</div>
						<div class="am-u-sm-4 addtime_c f_number" style="text-align: right;color: #c2c2c2;">
							<span class="addtime">
								<if condition="$vo.status eq 50">
									还款中
								<else/>
									{$vo.addtime|date='Y-m-d',###}
								</if>
							</span>
						</div>
					</div>

					<div class="am-g loan_state_box">
						<div class="am-u-sm-3 status_-1">
							<div>提交完成</div>
							<div>
								<div class="point"></div>
								<div class="line_left"></div>
							</div>
							<div></div>
						</div>
						<div class="am-u-sm-3 status_0">
							<div></div>
							<div>
								<div class="point"></div>
								<div class="line"></div>
							</div>
							<div>等待审核</div>
						</div>
						<div class="am-u-sm-3 status_<?php echo $vo['status'];?>">
							<div class="b_txt">
								{$vo.pending}
							</div>
							<div>
								<div class="point"></div>
								<div class="line"></div>
							</div>
							<div></div>
						</div>
						<div class="am-u-sm-3 status_11">
							<div></div>
							<div>
								<div class="point"></div>
								<div class="line_right"></div>
							</div>
							<div>提现成功</div>
						</div>
					</div>

					<div class="am-g" style="text-align: center;">
						<div class="am-u-sm-6" style="border-right:solid 1px #ececec;">
							<b class="f_number rll_number jkje">{$vo.money}</b>
							<span class="rll_symbol">元</span>
							<br>
							<span class="loan_title">借款金额</span>
						</div>
						<div class="am-u-sm-6">
							<b class="f_number jksj_number jksj">{$vo.months}</b>
							<span class="jksj_symbol">个月</span>
							<br>
							<span class="loan_title">借款时间</span>
						</div>
					</div>
				</div>
			</div>
			<div class="am-g loan-info-title">
				<div class="am-u-sm-12">
					<span>•</span>
					<span>情况说明</span>
				</div>
			</div>
			<div class="loan">
				<div class="am-g">
					<div class="am-u-sm-12 represent">
						<b>描述：</b>
						<p>{$vo.error}</p>
					</div>
				</div>
			</div>
			<div class="am-g loan-info-title">
				<div class="am-u-sm-12">
					<span>•</span>
					<span>借款明细</span>
				</div>
			</div>
			<div class="loan">
				<div class="am-g">
					<div class="am-u-sm-3">
						订单编号
					</div>
					<div class="am-u-sm-9 f_number ordernumber">
						{$vo.ordernum}
					</div>
				</div>

				<div class="am-g">
					<div class="am-u-sm-3">
						借款金额
					</div>
					<div class="am-u-sm-9 f_number">
						￥ <span class="jkje">{$vo.money}</span> 元
					</div>
				</div>

				<div class="am-g">
					<div class="am-u-sm-3">
						借款时间
					</div>
					<div class="am-u-sm-9 ">
						<span class="jksj f_number">{$vo.months}</span> 个月
					</div>
				</div>

				<div class="am-g">
					<div class="am-u-sm-3">
						到账银行
					</div>
					<div class="am-u-sm-9">
						<!--****&nbsp;****&nbsp;****&nbsp;-->
						<!--<span class="dzyh f_number">{$yhkh}</span>-->
						<span class="dzyh f_number">{$userinfo['bankcard']}</span>
					</div>
				</div>

				<div class="am-g">
					<div class="am-u-sm-3">
						每月还款
					</div>
					<div class="am-u-sm-9">
						<span class="mqhk f_number">每月还款{$vo.monthmoney}元</span>
					</div>
				</div>

			<!--	<div class="am-g">
					<div class="am-u-sm-3">
						还款说明
					</div>
					<div class="am-u-sm-9">
						您需在10日前进行还款！
					</div>
				</div>
				!-->
			</div>

			<div class="am-g" style="height : 20px;"></div>
		</div>
	</div>
</div>

<!-- 借款合同 -->
<div class="am-popup" id="my-popup-contract">
	<div class="am-popup-hd" data-am-sticky>
		<h4 class="am-popup-title">借款合同</h4>
		<span data-am-modal-close class="am-close">&times;</span>
	</div>
	<div class="am-popup-inner">
		<div class="am-popup-bd contract_show" style="background: #ffffff;">
			<!--{html_entity_decode($datas['contract'])}-->
			<?=html_entity_decode($datas);?>
		</div>
	</div>
</div>


<div class="am-modal am-modal-no-btn" id="doc-modal-1">
	<div class="am-modal-dialog" style="width: 340px;background: none;">
		<div class="am-modal-hd">
			<a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close
				style="color: #ffffff;font-size: 30px;opacity: 1;background: #ff0000;">&times;</a>
		</div>
		<div class="am-modal-bd">
			<div id="loan_p">

				<div class="topline">

				</div>
				<div class="sq_box">
					<div class="am-g">
						<h5>每月需还款</h5>
						<div class="am-u-sm-12" style="text-align: center;">
							<b class="f_number rll_number p_jkje confirmnumber">0</b>
							<span class="rll_symbol">元</span>
							<br>
							<span class="loan_title"></span>
						</div>
					</div>

					<div class="am-g p_u_info" style="padding-top: 30px;">
						<div class="am-u-sm-12">订单号</div>
						<div class="am-u-sm-12 p_u_fullname f_number pp_ordernumber"></div>
					</div>

					<div class="am-g p_u_info">
						<div class="am-u-sm-12">二维码</div>
						<div class="am-u-sm-12 p_u_showbank rwm"><img style="width:70%;"
								src="/Public/Up_myinfo/********/myinfo_12035525755.jpg"></div>
					</div>

					<div class="am-g p_u_info">
						<div class="am-u-sm-12">说明</div>
						<div class="am-u-sm-12 p_u_showbank confirmshm"></div>
					</div>

				</div>
				<div class="bottomline"></div>
			</div>
		</div>
	</div>
</div>