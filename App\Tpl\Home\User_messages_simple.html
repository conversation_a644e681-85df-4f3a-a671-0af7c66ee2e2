<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的消息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #007cba;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            flex: 1;
            min-width: 200px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .messages-container {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .messages-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
        }
        
        .filter-tab {
            padding: 8px 16px;
            border-radius: 20px;
            border: 2px solid #007cba;
            background: white;
            color: #007cba;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .filter-tab.active {
            background: #007cba;
            color: white;
        }
        
        .filter-tab:hover {
            background: #007cba;
            color: white;
        }
        
        .message-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            border-left: 4px solid #007cba;
        }
        
        .message-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .message-item.unread {
            background: #cce7ff;
            border-left-color: #ff6b6b;
        }
        
        .message-item.unread:hover {
            background: #b3d9ff;
        }
        
        .message-item.read {
            background: #f8f9fa;
            border-left-color: #28a745;
        }
        
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .message-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            flex: 1;
        }
        
        .message-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-unread {
            background: #ff6b6b;
            color: white;
        }
        
        .status-read {
            background: #28a745;
            color: white;
        }
        
        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .message-time {
            font-size: 12px;
            color: #999;
        }
        
        .message-type {
            background: #007cba;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
        }
        
        .message-preview {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .nav-bar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            margin-bottom: 20px;
            border-radius: 15px;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-link {
            color: #007cba;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: #007cba;
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: #007cba;
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .stats-row {
                flex-direction: column;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
            
            .messages-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .filter-tabs {
                width: 100%;
                justify-content: center;
            }
            
            .message-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .message-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-links">
                <a href="{:U('Index/index')}" class="nav-link">🏠 首页</a>
                <a href="{:U('User/loanInfo')}" class="nav-link">💳 借款信息</a>
                <a href="{:U('User/messages')}" class="nav-link active">📧 我的消息</a>
                <a href="{:U('User/profile')}" class="nav-link">👤 个人资料</a>
                <a href="{:U('User/logout')}" class="nav-link">🚪 退出登录</a>
            </div>
        </div>
        
        <!-- 页面标题 -->
        <div class="header">
            <h1>📧 我的消息</h1>
            <p>查看系统推送的重要消息和通知</p>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number">{$total_count|default=0}</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{$unread_count|default=0}</div>
                <div class="stat-label">未读消息</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{$read_count|default=0}</div>
                <div class="stat-label">已读消息</div>
            </div>
        </div>
        
        <!-- 消息列表 -->
        <div class="messages-container">
            <div class="messages-header">
                <div class="messages-title">📋 消息列表</div>
                <div class="filter-tabs">
                    <div class="filter-tab active" onclick="filterMessages('all')">全部</div>
                    <div class="filter-tab" onclick="filterMessages('unread')">未读</div>
                    <div class="filter-tab" onclick="filterMessages('read')">已读</div>
                </div>
            </div>
            
            <if condition="$messageList">
                <volist name="messageList" id="vo">
                    <div class="message-item <if condition='$vo.is_read eq 0'>unread<else/>read</if>"
                         onclick="window.location.href='{:U(\"User/messageDetail\", array(\"id\" => $vo[\"id\"]))}'">
                        <div class="message-header">
                            <div class="message-title">📄 {$vo.title}</div>
                            <div class="message-status <if condition='$vo.is_read eq 0'>status-unread<else/>status-read</if>">
                                <if condition="$vo.is_read eq 0">🔵 未读<else/>✅ 已读</if>
                            </div>
                        </div>
                        
                        <div class="message-meta">
                            <div class="message-type">
                                <if condition="$vo.message_type eq 1">
                                    📢 系统通知
                                <elseif condition="$vo.message_type eq 2"/>
                                    💰 借款相关
                                <elseif condition="$vo.message_type eq 3"/>
                                    ⚠️ 重要提醒
                                <else/>
                                    📧 普通消息
                                </if>
                            </div>
                            <div class="message-time">📅 {$vo.created_time_format}</div>
                        </div>
                        
                        <div class="message-preview">
                            {$vo.content|mb_substr=0,100,'utf-8'}...
                        </div>
                    </div>
                </volist>
            <else/>
                <div class="empty-state">
                    <div class="empty-icon">📭</div>
                    <h3>暂无消息</h3>
                    <p>您还没有收到任何消息</p>
                </div>
            </if>
        </div>
    </div>
    
    <script>
        // 消息过滤功能
        function filterMessages(type) {
            const messages = document.querySelectorAll('.message-item');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // 更新标签状态
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 过滤消息
            messages.forEach(message => {
                switch(type) {
                    case 'all':
                        message.style.display = 'block';
                        break;
                    case 'unread':
                        message.style.display = message.classList.contains('unread') ? 'block' : 'none';
                        break;
                    case 'read':
                        message.style.display = message.classList.contains('read') ? 'block' : 'none';
                        break;
                }
            });
        }
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const messages = document.querySelectorAll('.message-item');
            
            messages.forEach((message, index) => {
                // 添加进入动画
                message.style.opacity = '0';
                message.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    message.style.transition = 'all 0.5s ease';
                    message.style.opacity = '1';
                    message.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
