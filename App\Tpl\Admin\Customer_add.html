<include file="Public:header" />

<div class="main-content">
    <div class="breadcrumbs" id="breadcrumbs">
        <ul class="breadcrumb">
            <li>
                <i class="icon-home home-icon"></i>
                <a href="{:U('Main/index')}">首页</a>
            </li>
            <li>
                <a href="{:U('Customer/index')}">客户管理</a>
            </li>
            <li class="active">添加客户</li>
        </ul>
    </div>

    <div class="page-content">
        <div class="row">
            <div class="col-xs-12">
                <form class="form-horizontal" method="post" action="">
                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="customer_name">客户姓名 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="customer_name" name="customer_name" class="form-control" placeholder="请输入客户姓名" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="id_card">身份证号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="id_card" name="id_card" class="form-control" placeholder="请输入身份证号" maxlength="18" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="bank_card">银行卡号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="bank_card" name="bank_card" class="form-control" placeholder="请输入银行卡号" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="phone">手机号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="phone" name="phone" class="form-control" placeholder="请输入手机号" maxlength="11" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_amount">借款金额 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="number" id="loan_amount" name="loan_amount" class="form-control" placeholder="请输入借款金额" step="0.01" min="0" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_periods">借款分期 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <select id="loan_periods" name="loan_periods" class="form-control" required>
                                <option value="">请选择分期数</option>
                                <option value="3">3期</option>
                                <option value="6">6期</option>
                                <option value="12">12期</option>
                                <option value="24">24期</option>
                                <option value="36">36期</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_time">借款时间 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="datetime-local" id="loan_time" name="loan_time" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="due_time">到期时间 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="datetime-local" id="due_time" name="due_time" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="overdue_interest">逾期利息率(%)</label>
                        <div class="col-sm-9">
                            <input type="number" id="overdue_interest" name="overdue_interest" class="form-control" placeholder="请输入逾期利息率" step="0.01" min="0" value="0.05">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="status">状态</label>
                        <div class="col-sm-9">
                            <select id="status" name="status" class="form-control">
                                <option value="1">正常</option>
                                <option value="0">已结清</option>
                                <option value="-1">逾期</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="contract_content">借款合同</label>
                        <div class="col-sm-9">
                            <textarea id="contract_content" name="contract_content" class="form-control" rows="6" placeholder="请输入借款合同内容"></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="remarks">备注</label>
                        <div class="col-sm-9">
                            <textarea id="remarks" name="remarks" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>

                    <div class="clearfix form-actions">
                        <div class="col-md-offset-3 col-md-9">
                            <button class="btn btn-info" type="submit">
                                <i class="icon-ok bigger-110"></i>
                                提交
                            </button>

                            &nbsp; &nbsp; &nbsp;
                            <button class="btn" type="reset">
                                <i class="icon-undo bigger-110"></i>
                                重置
                            </button>

                            &nbsp; &nbsp; &nbsp;
                            <a href="{:U('Customer/index')}" class="btn btn-warning">
                                <i class="icon-arrow-left bigger-110"></i>
                                返回
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 设置默认时间
document.addEventListener('DOMContentLoaded', function() {
    var now = new Date();
    var year = now.getFullYear();
    var month = String(now.getMonth() + 1).padStart(2, '0');
    var day = String(now.getDate()).padStart(2, '0');
    var hours = String(now.getHours()).padStart(2, '0');
    var minutes = String(now.getMinutes()).padStart(2, '0');
    
    var currentDateTime = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;
    document.getElementById('loan_time').value = currentDateTime;
    
    // 计算到期时间（默认12个月后）
    var dueDate = new Date(now);
    dueDate.setMonth(dueDate.getMonth() + 12);
    var dueYear = dueDate.getFullYear();
    var dueMonth = String(dueDate.getMonth() + 1).padStart(2, '0');
    var dueDay = String(dueDate.getDate()).padStart(2, '0');
    var dueDateTime = dueYear + '-' + dueMonth + '-' + dueDay + 'T' + hours + ':' + minutes;
    document.getElementById('due_time').value = dueDateTime;
});
</script>

<include file="Public:footer" />
