<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的借款信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #007cba;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            flex: 1;
            min-width: 200px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .loan-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .loan-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #007cba;
        }
        
        .loan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .loan-card.status-danger {
            border-left-color: #dc3545;
        }
        
        .loan-card.status-warning {
            border-left-color: #ffc107;
        }
        
        .loan-card.status-success {
            border-left-color: #28a745;
        }
        
        .loan-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .loan-amount {
            font-size: 28px;
            font-weight: bold;
            color: #007cba;
        }
        
        .loan-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: auto;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-settled {
            background: #cce7ff;
            color: #004085;
        }
        
        .loan-details {
            margin-bottom: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            color: #666;
            font-weight: 500;
        }
        
        .detail-value {
            color: #333;
            font-weight: 600;
        }
        
        .masked-info {
            font-family: monospace;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .remaining-days {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .remaining-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .remaining-positive {
            color: #28a745;
        }
        
        .remaining-warning {
            color: #ffc107;
        }
        
        .remaining-danger {
            color: #dc3545;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.6s ease;
        }
        
        .progress-success {
            background: #28a745;
        }
        
        .progress-warning {
            background: #ffc107;
        }
        
        .progress-danger {
            background: #dc3545;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .nav-bar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            margin-bottom: 20px;
            border-radius: 15px;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-link {
            color: #007cba;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: #007cba;
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: #007cba;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .stats-row {
                flex-direction: column;
            }
            
            .loan-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-links">
                <a href="{:U('Index/index')}" class="nav-link">🏠 首页</a>
                <a href="{:U('User/loanInfo')}" class="nav-link active">💳 借款信息</a>
                <a href="{:U('User/messages')}" class="nav-link">📧 我的消息</a>
                <a href="{:U('User/logout')}" class="nav-link">🚪 退出登录</a>
            </div>
        </div>
        
        <!-- 页面标题 -->
        <div class="header">
            <h1>💰 我的借款信息</h1>
            <p>查看您的所有借款记录和还款状态</p>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number" id="totalLoans">0</div>
                <div class="stat-label">借款笔数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">¥<span id="totalAmount">0</span></div>
                <div class="stat-label">总借款金额</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="normalLoans">0</div>
                <div class="stat-label">正常借款</div>
            </div>
        </div>
        
        <!-- 借款列表 -->
        <if condition="$loanList">
            <div class="loan-grid">
                <volist name="loanList" id="vo">
                    <div class="loan-card status-{$vo.status_class}">
                        <div class="loan-header">
                            <div class="loan-amount">¥{$vo.loan_amount}</div>
                            <div class="loan-status status-{$vo.status_text eq '正常' ? 'normal' : ($vo.status_text eq '逾期' ? 'overdue' : 'settled')}">
                                {$vo.status_text}
                            </div>
                        </div>
                        
                        <div class="loan-details">
                            <div class="detail-row">
                                <span class="detail-label">📅 借款期数</span>
                                <span class="detail-value">{$vo.loan_periods}期</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">⏰ 借款时间</span>
                                <span class="detail-value">{$vo.loan_time_format}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">📆 到期时间</span>
                                <span class="detail-value">{$vo.due_time_format}</span>
                            </div>
                            <if condition="$vo.id_card_masked">
                                <div class="detail-row">
                                    <span class="detail-label">🆔 身份证号</span>
                                    <span class="detail-value masked-info">{$vo.id_card_masked}</span>
                                </div>
                            </if>
                            <if condition="$vo.bank_card_masked">
                                <div class="detail-row">
                                    <span class="detail-label">💳 银行卡号</span>
                                    <span class="detail-value masked-info">{$vo.bank_card_masked}</span>
                                </div>
                            </if>
                            <!-- 合同显示 - 强制显示 -->
                            <div class="detail-row">
                                <span class="detail-label">📄 借款合同</span>
                                <span class="detail-value">
                                    <a href="javascript:void(0)" onclick="showContract({$vo.id})" style="color: #007cba; text-decoration: none; background: #f0f8ff; padding: 5px 10px; border-radius: 15px; font-size: 12px; cursor: pointer;">
                                        📎 查看借款合同
                                    </a>
                                </span>
                            </div>
                        </div>
                        
                        <div class="remaining-days">
                            <div class="remaining-number 
                                <if condition='$vo.remaining_days gt 7'>remaining-positive
                                <elseif condition='$vo.remaining_days gt 0'/>remaining-warning
                                <else/>remaining-danger</if>">
                                <if condition="$vo.remaining_days gt 0">
                                    还有 {$vo.remaining_days} 天到期
                                <elseif condition="$vo.remaining_days eq 0"/>
                                    今日到期
                                <else/>
                                    已逾期 {$vo.overdue_days} 天
                                </if>
                            </div>
                            
                            <if condition="$vo.remaining_days gt -1">
                                <div class="progress-bar">
                                    <div class="progress-fill 
                                        <if condition='$vo.remaining_days gt 7'>progress-success
                                        <elseif condition='$vo.remaining_days gt 0'/>progress-warning
                                        <else/>progress-danger</if>" 
                                        style="width: <if condition='$vo.remaining_days gt 30'>100<else/>{:max(10, min(100, ($vo['remaining_days']+30)*100/60))}</if>%">
                                    </div>
                                </div>
                            </if>
                        </div>
                    </div>
                </volist>
            </div>
        <else/>
            <div class="empty-state">
                <div class="empty-icon">📄</div>
                <h3>暂无借款记录</h3>
                <p>您还没有任何借款记录</p>
            </div>
        </if>
    </div>
    
    <script>
        // 显示合同内容
        function showContract(loanId) {
            // 创建合同内容弹窗
            const contractContent = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 15px; padding: 30px; max-width: 600px; max-height: 80%; overflow-y: auto; margin: 20px;" onclick="event.stopPropagation()">
                        <div style="text-align: center; margin-bottom: 20px;">
                            <h2 style="color: #333; margin-bottom: 10px;">📄 借款合同</h2>
                            <p style="color: #666;">借款ID: ${loanId}</p>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; line-height: 1.8; color: #333;">
                            <h3 style="text-align: center; margin-bottom: 20px;">借款合同</h3>

                            <p><strong>甲方（出借人）：</strong>小贷公司</p>
                            <p><strong>乙方（借款人）：</strong>客户</p>
                            <br>

                            <p>根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、协商一致的基础上，就借款事宜达成如下协议：</p>
                            <br>

                            <p><strong>第一条 借款金额</strong></p>
                            <p>乙方向甲方借款，具体金额以系统记录为准。</p>
                            <br>

                            <p><strong>第二条 借款期限</strong></p>
                            <p>借款期限以系统记录的期数为准，每期30天。</p>
                            <br>

                            <p><strong>第三条 还款方式</strong></p>
                            <p>乙方应按期足额还款，逾期将产生相应的逾期费用。</p>
                            <br>

                            <p><strong>第四条 其他条款</strong></p>
                            <p>1. 本合同自双方签字之日起生效。</p>
                            <p>2. 如有争议，双方协商解决。</p>
                            <p>3. 本合同受中华人民共和国法律保护。</p>
                            <br>

                            <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                                <div>
                                    <p><strong>甲方：</strong>小贷公司</p>
                                    <p style="margin-top: 20px;">签字：_____________</p>
                                </div>
                                <div>
                                    <p><strong>乙方：</strong>客户</p>
                                    <p style="margin-top: 20px;">签字：_____________</p>
                                </div>
                            </div>

                            <p style="text-align: center; margin-top: 20px;"><strong>签订日期：</strong>${new Date().toLocaleDateString('zh-CN')}</p>
                        </div>

                        <div style="text-align: center;">
                            <button onclick="this.closest('[style*=\"position: fixed\"]').remove()" style="padding: 10px 30px; background: #007cba; color: white; border: none; border-radius: 25px; cursor: pointer; font-size: 14px;">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', contractContent);
        }

        // 计算统计数据
        document.addEventListener('DOMContentLoaded', function() {
            const loanCards = document.querySelectorAll('.loan-card');
            let totalAmount = 0;
            let normalCount = 0;

            loanCards.forEach((card, index) => {
                // 添加进入动画
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);

                // 计算统计数据
                const amountText = card.querySelector('.loan-amount').textContent;
                const amount = parseFloat(amountText.replace('¥', '').replace(',', ''));
                totalAmount += amount;

                const statusText = card.querySelector('.loan-status').textContent.trim();
                if (statusText === '正常') {
                    normalCount++;
                }
            });

            // 更新统计显示
            document.getElementById('totalLoans').textContent = loanCards.length;
            document.getElementById('totalAmount').textContent = totalAmount.toLocaleString();
            document.getElementById('normalLoans').textContent = normalCount;
        });
    </script>
</body>
</html>
