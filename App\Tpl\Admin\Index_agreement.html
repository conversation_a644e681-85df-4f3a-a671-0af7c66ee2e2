{__NOLAYOUT__}

<!DOCTYPE html>

<html>

<head>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta http-equiv="Pragma" content="no-cache">

    <meta http-equiv="Cache-Control" content="no-cache">

    <meta http-equiv="Expires" content="0">

    <title>客户使用协议
        <somnus:sitecfg name="sitetitle" />
     - 不得用非法用途，否则后果自负（） </title>

    <script type="text/javascript" src="__PUBLIC__/main/js/jquery.min.js"></script>
    <script src="__PUBLIC__/layer/layer.js"></script>
    <link href="__PUBLIC__/main/css/layui.css" rel="stylesheet" type="text/css">
    <style>
        input[type=checkbox] {
            display: none;
        }

        .checkzi {
            margin: 20px auto;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="layui-main alone-items">
        <fieldset class="layui-elem-field layui-field-title" style="text-align: center;">
            <legend>客户使用协议</legend>
        </fieldset>
        <dl class="layui-text qa-main" style="border: 1px solid #ccc; border: radius 5px;padding:20px;">
            <h1></h1>
            <dd>
                
                <ul>
                    <li>1、请遵守中华人民共和国法律，不得用非法用途，否则后果自负</li>
                    <!--
                    <li>2、受你方委托并根据你方要求，我司开发的软件为合法产品，不得用于非法用途；</li>
                    <li>3、我司为你方开发的软件程序仅用于兴趣研究、爱好学习和合法用途；</li>
                    <li>4、你方利用我司开发的软件程序进行非法活动所产生的一切刑事、民事等责任均与我司无关，你方须独自承担由此造成的责任；</li>
                    <li>5、你方保证对我司开发的软件程序技术、数据予以保密，如有恶意泄漏造成我司的一切损失，均由你方承担。</li>
                    -->
                </ul>
            </dd>
        </dl>
        <div class="checkzi checkboxs">
            <input type="checkbox" name="chooseInfo" lay-skin="primary">
            <div class="layui-unselect layui-form-checkbox" lay-skin="primary"><i class="layui-icon layui-icon-ok"></i>
            </div>
            我已阅读并同意
        </div>
        <div style="text-align: center;">
            <a class="layui-btn" href="javascript:subForm();">确认登陆</a>
        </div>
    </div>
    <script>
     $('.checkzi').on('click', function () {
                $(this).find('div').hasClass('layui-form-checked') ? $(this).find('div').removeClass('layui-form-checked') : $(this).find('div').addClass('layui-form-checked');
                if ($(this).find('input').is(':checked')) {
                    $(this).find('input').prop("checked", false);
                    $('.checkall').find('div').removeClass('layui-form-checked');
                } else {
                    $(this).find('input').prop("checked", true);
                }
            })
        function subForm() {
            if (!$("input[type='checkbox']").is(':checked')) {
                layer.msg('请勾选同意');
            } else {
                window.location.href = "{:U(GROUP_NAME.'/Index/login')}";
            }
           
        }


    </script>
</body>

</html>