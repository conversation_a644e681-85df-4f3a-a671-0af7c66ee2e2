<include file="Public:header" />

<div class="main-content">
    <div class="breadcrumbs" id="breadcrumbs">
        <ul class="breadcrumb">
            <li>
                <i class="icon-home home-icon"></i>
                <a href="{:U('Main/index')}">首页</a>
            </li>
            <li>
                <a href="{:U('Customer/index')}">客户管理</a>
            </li>
            <li class="active">编辑客户</li>
        </ul>
    </div>

    <div class="page-content">
        <div class="row">
            <div class="col-xs-12">
                <form class="form-horizontal" method="post" action="" enctype="multipart/form-data">
                    <!-- 隐藏的ID字段 - 修复参数错误问题 -->
                    <input type="hidden" name="id" value="{$info.id}">

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="customer_name">客户姓名 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="customer_name" name="customer_name" class="form-control" value="{$info.customer_name}" placeholder="请输入客户姓名" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="id_card">身份证号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="id_card" name="id_card" class="form-control" value="{$info.id_card}" placeholder="请输入身份证号" maxlength="18" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="bank_card">银行卡号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="bank_card" name="bank_card" class="form-control" value="{$info.bank_card}" placeholder="请输入银行卡号" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="phone">手机号 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="text" id="phone" name="phone" class="form-control" value="{$info.phone}" placeholder="请输入手机号" maxlength="11" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_amount">借款金额 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="number" id="loan_amount" name="loan_amount" class="form-control" value="{$info.loan_amount}" placeholder="请输入借款金额" step="0.01" min="0" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_periods">借款分期 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <select id="loan_periods" name="loan_periods" class="form-control" required>
                                <option value="">请选择分期数</option>
                                <option value="3" <if condition="$info['loan_periods'] eq 3">selected</if>>3期</option>
                                <option value="6" <if condition="$info['loan_periods'] eq 6">selected</if>>6期</option>
                                <option value="12" <if condition="$info['loan_periods'] eq 12">selected</if>>12期</option>
                                <option value="24" <if condition="$info['loan_periods'] eq 24">selected</if>>24期</option>
                                <option value="36" <if condition="$info['loan_periods'] eq 36">selected</if>>36期</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="loan_time">借款时间 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="datetime-local" id="loan_time" name="loan_time" class="form-control" value="{$info.loan_time}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="due_time">到期时间 <span class="red">*</span></label>
                        <div class="col-sm-9">
                            <input type="datetime-local" id="due_time" name="due_time" class="form-control" value="{$info.due_time}" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="overdue_interest">逾期利息率(%)</label>
                        <div class="col-sm-9">
                            <input type="number" id="overdue_interest" name="overdue_interest" class="form-control" value="{$info.overdue_interest}" placeholder="请输入逾期利息率" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="status">状态</label>
                        <div class="col-sm-9">
                            <select id="status" name="status" class="form-control">
                                <option value="1" <if condition="$info['status'] eq 1">selected</if>>正常</option>
                                <option value="0" <if condition="$info['status'] eq 0">selected</if>>已结清</option>
                                <option value="-1" <if condition="$info['status'] eq -1">selected</if>>逾期</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="contract_content">借款合同</label>
                        <div class="col-sm-9">
                            <textarea id="contract_content" name="contract_content" class="form-control" rows="6" placeholder="请输入借款合同内容">{$info.contract_content}</textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="contract_file">合同文件上传</label>
                        <div class="col-sm-9">
                            <input type="file" id="contract_file" name="contract_file" class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <span class="help-block">
                                <i class="icon-info-sign"></i>
                                支持格式：PDF、Word文档、图片，最大10MB
                                <if condition="$info.contract_file">
                                    <br><i class="icon-file"></i>
                                    当前文件：<a href="{:U('Customer/downloadContract', array('id' => $info.id))}" target="_blank">点击下载</a>
                                </if>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label no-padding-right" for="remarks">备注</label>
                        <div class="col-sm-9">
                            <textarea id="remarks" name="remarks" class="form-control" rows="3" placeholder="请输入备注信息">{$info.remarks}</textarea>
                        </div>
                    </div>

                    <div class="clearfix form-actions">
                        <div class="col-md-offset-3 col-md-9">
                            <button class="btn btn-info" type="submit">
                                <i class="icon-ok bigger-110"></i>
                                保存修改
                            </button>

                            &nbsp; &nbsp; &nbsp;
                            <button class="btn" type="reset">
                                <i class="icon-undo bigger-110"></i>
                                重置
                            </button>

                            &nbsp; &nbsp; &nbsp;
                            <a href="{:U('Customer/index')}" class="btn btn-warning">
                                <i class="icon-arrow-left bigger-110"></i>
                                返回
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<include file="Public:footer" />
