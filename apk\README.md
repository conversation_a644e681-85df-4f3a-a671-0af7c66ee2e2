# 📱 优易花APK文件目录

## 📁 目录说明
此目录用于存放优易花APP的APK安装包文件。

## 📋 文件命名规范
- **格式**：`youyihua_v{版本号}.apk`
- **示例**：
  - `youyihua_v1.0.0.apk` (当前版本)
  - `youyihua_v1.1.0.apk` (未来版本)
  - `youyihua_v2.0.0.apk` (主要版本)

## 🔗 下载链接对应关系
- **网页链接**：`https://dailuanshej.cn/apk/youyihua_v1.0.0.apk`
- **本地路径**：`源码/apk/youyihua_v1.0.0.apk`
- **脚本配置**：在 `download_apk.php` 中配置版本信息

## 📊 当前配置的版本信息
```php
'latest' => array(
    'version' => 'v1.0.0',
    'size' => '15.2 MB',
    'file' => 'youyihua_v1.0.0.apk',
    'path' => '/apk/youyihua_v1.0.0.apk',
    'md5' => 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6',
    'release_date' => '2024-01-15'
)
```

## 🚀 部署步骤

### 1. 编译APK
使用Android Studio编译 `android_frontend` 项目：
```bash
cd android_frontend
./gradlew assembleRelease
```

### 2. 复制APK文件
将编译好的APK文件复制到此目录：
```bash
# 从Android Studio输出目录复制
cp android_frontend/app/build/outputs/apk/release/app-release.apk ./youyihua_v1.0.0.apk
```

### 3. 验证文件
- 检查文件大小是否合理 (通常10-20MB)
- 验证APK可以正常安装
- 计算MD5校验码并更新配置

### 4. 更新配置
在 `download_apk.php` 中更新：
- 文件大小
- MD5校验码
- 发布日期
- 更新日志

## 🔒 安全注意事项
- 确保APK文件来源可靠
- 定期更新MD5校验码
- 保持文件权限为644 (可读不可执行)
- 定期备份历史版本

## 📈 版本管理
- 保留最近3个版本的APK文件
- 删除过旧版本以节省空间
- 使用语义化版本号 (major.minor.patch)

## 🛠️ 故障排除
如果下载出现问题：
1. 检查文件是否存在
2. 验证文件权限
3. 确认Web服务器配置
4. 查看下载日志 `../logs/download.log`

## 📞 联系信息
如有问题请联系技术支持：
- 网站：https://dailuanshej.cn
- 邮箱：<EMAIL>
