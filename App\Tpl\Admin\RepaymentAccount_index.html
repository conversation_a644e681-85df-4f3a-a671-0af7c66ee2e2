<div class="layui-table-tool">
    <div class="filter">
        <form action="{:U(GROUP_NAME.'/RepaymentAccount/index')}" method="post">
            <select name="account_type" class="inpMain">
                <option value="">全部类型</option>
                <option value="1" <if condition="$account_type eq 1">selected</if>>对公账户</option>
                <option value="2" <if condition="$account_type eq 2">selected</if>>对私账户</option>
            </select>
            <input name="keyword" type="text" class="inpMain" value="{$keyword}" size="20" placeholder="账户名称/银行名称/开户人"/>
            <input name="submit" class="btnGray layui-btn" type="submit" value="筛选" />
            <a href="{:U(GROUP_NAME.'/RepaymentAccount/add')}" class="layui-btn layui-btn-normal">添加还款账户</a>
        </form>
    </div>
</div>

<div id="list">
    <table width="100%" border="0" cellpadding="8" cellspacing="0" class="tableBasic">
        <tr>
            <th width="50" align="center">ID</th>
            <th width="80" align="center">账户类型</th>
            <th width="150">账户名称</th>
            <th width="120">银行名称</th>
            <th width="180">账户号码</th>
            <th width="100">开户人/单位</th>
            <th width="80" align="center">二维码</th>
            <th width="60" align="center">状态</th>
            <th width="60" align="center">排序</th>
            <th width="120" align="center">创建时间</th>
            <th align="center">操作</th>
        </tr>
        <volist name="list" id="vo">
            <tr>
                <td align="center">{$vo.id}</td>
                <td align="center">
                    <if condition="$vo.account_type eq 1">
                        <span style="color: #1890ff;">对公账户</span>
                    <else/>
                        <span style="color: #52c41a;">对私账户</span>
                    </if>
                </td>
                <td>{$vo.account_name}</td>
                <td>{$vo.bank_name}</td>
                <td>{$vo.account_number}</td>
                <td>{$vo.account_holder}</td>
                <td align="center">
                    <if condition="$vo.qr_code_image">
                        <a href="javascript:showQrCode('{$vo.qr_code_image}', '{$vo.account_name}');" style="color: #52c41a;">查看</a>
                    <else/>
                        <span style="color: #ccc;">无</span>
                    </if>
                </td>
                <td align="center">
                    <if condition="$vo.is_active eq 1">
                        <span style="color: #52c41a;">启用</span>
                    <else/>
                        <span style="color: #ff4d4f;">禁用</span>
                    </if>
                </td>
                <td align="center">{$vo.sort_order}</td>
                <td align="center">{$vo.created_time|date='Y-m-d H:i',###}</td>
                <td align="center">
                    <a href="{:U(GROUP_NAME.'/RepaymentAccount/edit',array('id'=>$vo['id']))}" class="layui-btn layui-btn-xs">编辑</a>
                    <a href="javascript:toggleStatus('{$vo.id}');" class="layui-btn layui-btn-xs <if condition='$vo.is_active eq 1'>layui-btn-warm<else/>layui-btn-normal</if>">
                        <if condition="$vo.is_active eq 1">禁用<else/>启用</if>
                    </a>
                    <a href="javascript:del('{$vo.id}','{:U(GROUP_NAME.'/RepaymentAccount/delete',array('id'=>$vo['id']))}');" class="layui-btn layui-btn-xs layui-btn-danger">删除</a>
                </td>
            </tr>
        </volist>
    </table>
</div>

<div class="clear"></div>
<div class="pager">
    {$page}
</div>

<script>
    function del(id, jumpurl) {
        layer.confirm(
            '确定要删除该还款账户吗？删除后将无法恢复！',
            function () {
                window.location.href = jumpurl;
            }
        );
    }

    function toggleStatus(id) {
        var url = "{:U(GROUP_NAME.'/RepaymentAccount/toggleStatus')}&id=" + id;
        $.get(url, function(res) {
            if (res.status == 1) {
                layer.msg(res.info, {icon: 1});
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                layer.msg(res.info, {icon: 2});
            }
        }, 'json');
    }

    function showQrCode(imagePath, accountName) {
        layer.open({
            type: 1,
            title: accountName + ' - 二维码',
            skin: 'layui-layer-lan',
            area: ['400px', '450px'],
            content: '<div style="text-align: center; padding: 20px;"><img src="' + imagePath + '" style="max-width: 100%; max-height: 350px;" alt="二维码"></div>',
            btn: ['关闭'],
            yes: function(index) {
                layer.close(index);
            }
        });
    }
</script>

<style>
    .tableBasic th {
        background: #f5f5f5;
        font-weight: bold;
    }
    .tableBasic td {
        border-bottom: 1px solid #e8e8e8;
    }
    .filter {
        margin-bottom: 15px;
    }
    .filter .inpMain {
        margin-right: 10px;
    }
</style>
