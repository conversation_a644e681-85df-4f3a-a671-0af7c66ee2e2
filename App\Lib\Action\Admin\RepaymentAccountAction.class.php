<?php
/**
 * 还款账户管理控制器
 */
class RepaymentAccountAction extends CommonAction {

    /**
     * 还款账户列表
     */
    public function index() {
        $RepaymentAccount = M('repayment_accounts');
        import('ORG.Util.Page');
        
        $keyword = I("keyword", '', 'trim');
        $account_type = I("account_type", '', 'intval');
        
        $this->keyword = $keyword;
        $this->account_type = $account_type;
        
        $where = array();
        if ($keyword) {
            $where['account_name|bank_name|account_holder'] = array('like', '%' . $keyword . '%');
        }
        if ($account_type) {
            $where['account_type'] = $account_type;
        }

        $count = $RepaymentAccount->where($where)->count();
        $Page = new Page($count, 20);
        $Page->setConfig('theme', '共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show = $Page->show();
        
        $list = $RepaymentAccount->where($where)->order('sort_order ASC, id DESC')->limit($Page->firstRow . ',' . $Page->listRows)->select();
        
        $this->title = '还款账户管理';
        $this->list = $list;
        $this->page = $show;
        $this->display();
    }

    /**
     * 添加还款账户
     */
    public function add() {
        if (IS_POST) {
            $data = I('post.');
            $RepaymentAccount = M('repayment_accounts');
            
            // 数据验证
            if (empty($data['account_name'])) {
                $this->error('请输入账户名称');
            }
            if (empty($data['bank_name'])) {
                $this->error('请输入银行名称');
            }
            if (empty($data['account_number'])) {
                $this->error('请输入账户号码');
            }
            if (empty($data['account_holder'])) {
                $this->error('请输入开户人/开户单位');
            }
            
            // 处理二维码上传
            if (!empty($_FILES['qr_code_image']['name'])) {
                import('ORG.Net.UploadFile');
                $upload = new UploadFile();
                $upload->maxSize = 2097152; // 2MB
                $upload->allowExts = array('jpg', 'jpeg', 'png', 'gif');
                $upload->savePath = './Public/uploads/qrcodes/';
                $upload->saveRule = 'time';
                $upload->autoSub = true;
                $upload->subType = 'date';
                
                if (!$upload->upload()) {
                    $this->error($upload->getErrorMsg());
                } else {
                    $info = $upload->getUploadFileInfo();
                    $data['qr_code_image'] = '/Public/uploads/qrcodes/' . $info[0]['savename'];
                }
            }
            
            $data['created_time'] = time();
            $data['updated_time'] = time();
            $data['admin_id'] = session('admin_id');
            
            if ($RepaymentAccount->add($data)) {
                $this->success('添加成功', U('RepaymentAccount/index'));
            } else {
                $this->error('添加失败');
            }
        } else {
            $this->title = '添加还款账户';
            $this->display();
        }
    }

    /**
     * 编辑还款账户
     */
    public function edit() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $RepaymentAccount = M('repayment_accounts');
        
        if (IS_POST) {
            $data = I('post.');
            
            // 数据验证
            if (empty($data['account_name'])) {
                $this->error('请输入账户名称');
            }
            if (empty($data['bank_name'])) {
                $this->error('请输入银行名称');
            }
            if (empty($data['account_number'])) {
                $this->error('请输入账户号码');
            }
            if (empty($data['account_holder'])) {
                $this->error('请输入开户人/开户单位');
            }
            
            // 处理二维码上传
            if (!empty($_FILES['qr_code_image']['name'])) {
                import('ORG.Net.UploadFile');
                $upload = new UploadFile();
                $upload->maxSize = 2097152; // 2MB
                $upload->allowExts = array('jpg', 'jpeg', 'png', 'gif');
                $upload->savePath = './Public/uploads/qrcodes/';
                $upload->saveRule = 'time';
                $upload->autoSub = true;
                $upload->subType = 'date';
                
                if (!$upload->upload()) {
                    $this->error($upload->getErrorMsg());
                } else {
                    $info = $upload->getUploadFileInfo();
                    $data['qr_code_image'] = '/Public/uploads/qrcodes/' . $info[0]['savename'];
                }
            }
            
            $data['updated_time'] = time();
            $data['admin_id'] = session('admin_id');
            
            if ($RepaymentAccount->where(array('id' => $id))->save($data)) {
                $this->success('修改成功', U('RepaymentAccount/index'));
            } else {
                $this->error('修改失败');
            }
        } else {
            $info = $RepaymentAccount->where(array('id' => $id))->find();
            if (!$info) {
                $this->error('记录不存在');
            }
            
            $this->title = '编辑还款账户';
            $this->info = $info;
            $this->display();
        }
    }

    /**
     * 删除还款账户
     */
    public function delete() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $RepaymentAccount = M('repayment_accounts');
        
        // 检查是否存在
        $info = $RepaymentAccount->where(array('id' => $id))->find();
        if (!$info) {
            $this->error('记录不存在');
        }
        
        // 删除二维码文件
        if ($info['qr_code_image'] && file_exists('.' . $info['qr_code_image'])) {
            unlink('.' . $info['qr_code_image']);
        }
        
        if ($RepaymentAccount->where(array('id' => $id))->delete()) {
            $this->success('删除成功');
        } else {
            $this->error('删除失败');
        }
    }

    /**
     * 切换启用状态
     */
    public function toggleStatus() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $RepaymentAccount = M('repayment_accounts');
        $info = $RepaymentAccount->where(array('id' => $id))->find();
        if (!$info) {
            $this->error('记录不存在');
        }
        
        $new_status = $info['is_active'] ? 0 : 1;
        $data = array(
            'is_active' => $new_status,
            'updated_time' => time(),
            'admin_id' => session('admin_id')
        );
        
        if ($RepaymentAccount->where(array('id' => $id))->save($data)) {
            $status_text = $new_status ? '启用' : '禁用';
            $this->success($status_text . '成功');
        } else {
            $this->error('操作失败');
        }
    }

    /**
     * 排序
     */
    public function sort() {
        if (IS_POST) {
            $ids = I('post.ids');
            $sorts = I('post.sorts');
            
            if (empty($ids) || empty($sorts)) {
                $this->error('参数错误');
            }
            
            $RepaymentAccount = M('repayment_accounts');
            
            foreach ($ids as $key => $id) {
                $sort = intval($sorts[$key]);
                $RepaymentAccount->where(array('id' => $id))->save(array(
                    'sort_order' => $sort,
                    'updated_time' => time(),
                    'admin_id' => session('admin_id')
                ));
            }
            
            $this->success('排序成功');
        }
    }

    /**
     * 上传二维码
     */
    public function uploadQrCode() {
        if (IS_POST) {
            import('ORG.Net.UploadFile');
            $upload = new UploadFile();
            $upload->maxSize = 2097152; // 2MB
            $upload->allowExts = array('jpg', 'jpeg', 'png', 'gif');
            $upload->savePath = './Public/uploads/qrcodes/';
            $upload->saveRule = 'time';
            $upload->autoSub = true;
            $upload->subType = 'date';
            
            if (!$upload->upload()) {
                $this->ajaxReturn(array('status' => 0, 'msg' => $upload->getErrorMsg()));
            } else {
                $info = $upload->getUploadFileInfo();
                $file_path = '/Public/uploads/qrcodes/' . $info[0]['savename'];
                $this->ajaxReturn(array('status' => 1, 'msg' => '上传成功', 'file_path' => $file_path));
            }
        }
    }
}
