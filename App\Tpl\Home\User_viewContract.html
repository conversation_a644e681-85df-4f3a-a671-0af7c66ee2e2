<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看合同内容</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #007cba;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .loan-info {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .contract-content {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .contract-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .contract-text {
            line-height: 1.8;
            color: #444;
            white-space: pre-wrap;
            font-size: 14px;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            margin: 0 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,186,0.4);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(108,117,125,0.4);
            color: white;
            text-decoration: none;
        }
        
        .empty-contract {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .info-row {
                flex-direction: column;
                gap: 5px;
            }
            
            .contract-content {
                padding: 20px;
            }
            
            .btn {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>📄 借款合同</h1>
            <p>查看您的借款合同详细内容</p>
        </div>
        
        <!-- 借款信息 -->
        <div class="loan-info">
            <h3 style="margin-bottom: 15px; color: #333;">📊 借款信息</h3>
            <div class="info-row">
                <span class="info-label">客户姓名</span>
                <span class="info-value">{$loan_info.customer_name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">借款金额</span>
                <span class="info-value">¥{$loan_info.loan_amount}</span>
            </div>
            <div class="info-row">
                <span class="info-label">借款期数</span>
                <span class="info-value">{$loan_info.loan_periods}期</span>
            </div>
            <div class="info-row">
                <span class="info-label">借款时间</span>
                <span class="info-value">{$loan_info.loan_time_format}</span>
            </div>
            <div class="info-row">
                <span class="info-label">到期时间</span>
                <span class="info-value">{$loan_info.due_time_format}</span>
            </div>
        </div>
        
        <!-- 合同内容 -->
        <div class="contract-content">
            <if condition="$loan_info.contract_content">
                <div class="contract-title">借款合同</div>
                <div class="contract-text">{$loan_info.contract_content}</div>
            <else/>
                <div class="empty-contract">
                    <div class="empty-icon">📋</div>
                    <h4>暂无合同内容</h4>
                    <p>该借款记录暂时没有合同内容</p>
                </div>
            </if>
        </div>
        
        <!-- 操作按钮 -->
        <div class="actions">
            <a href="javascript:history.back()" class="btn btn-secondary">
                ← 返回借款信息
            </a>
            <a href="{:U('User/loanInfo')}" class="btn btn-primary">
                📊 查看所有借款
            </a>
            <if condition="$loan_info.contract_file">
                <a href="{:U('User/downloadContract', array('id' => $loan_info['id']))}" class="btn btn-primary">
                    📎 下载合同文件
                </a>
            </if>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.header, .loan-info, .contract-content');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
