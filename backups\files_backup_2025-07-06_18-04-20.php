<?php
// 源码备份文件
// 备份时间: 2025-07-06 18:04:20
// 重要文件备份

$backup_files = array();\n\n$backup_files['App/Lib/Action/Home/UserAction.class.php'] = '<?php
class UserAction extends CommonAction
{
	public function index()
	{
		//判断是否已登录
		$user = $this->getLoginUser();
		$users = D(\'user\')->where(array(\'phone\' => $user))->find();
		$this->users = $users;
		$this->user = $user;
		$this->display();
	}
	public function setup()
	{
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}
		if (IS_POST) {
			$password = I("password", \'\', \'trim\');
			$password = sha1(md5($password));
			$res = D(\'user\')->where(array(\'phone\' => $this->getLoginUser()))->save(array(\'password\' => $password));
			if (!$res) {
				$data[\'msg\'] = "出现了一个错误";
			} else {
				$data[\'msg\'] = "修改成功";
			}
			$this->ajaxReturn($data);
			exit();
		}
		$this->display();
	}


	//用户登录
	public function login()
	{
		if (IS_POST) {
			$data = array(\'status\' => 0, \'msg\' => \'未知错误\');
			$password = I("password", \'\', \'trim\');
			$phone = I("phone", \'\', \'trim\');

			// 基本验证
			if (empty($phone) || empty($password)) {
				$data[\'msg\'] = "请填写手机号和密码";
			} elseif (!checkphone($phone)) {
				$data[\'msg\'] = "手机号码格式不正确";
			} elseif (strlen($password) < 6 || strlen($password) > 16) {
				$data[\'msg\'] = "密码长度应为6-16位";
			} else {
				$password = sha1(md5($password));
				$User = D("user");
				$info = $User->where(array(\'phone\' => $phone, \'password\' => $password))->find();
				if (!$info) {
					$data[\'msg\'] = "手机号或密码错误";
				} else if ($info[\'status\'] != 1) {
					$data[\'msg\'] = "该账户已被禁止登录!";
				} else {
					// 更新最后登录时间
					$User->where(array(\'phone\' => $phone))->save(array(\'last_time\'=>time()));
					// 设置登录session（兼容新旧格式）
					session(\'user_phone\', $phone);
					session(\'user_id\', $info[\'id\']);
					$_SESSION[\'user\'] = $phone; // 兼容旧格式
					$data[\'status\'] = 1;
					$data[\'msg\'] = \'登录成功\';
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		//判断是否已登录
		if ($this->getLoginUser()) {
			$this->redirect(\'User/index\');
		}
		$this->display();
	}

	//注销登陆
	public function logout()
	{
		// 清除所有登录相关的session
		session(\'user_phone\', null);
		session(\'user_id\', null);
		unset($_SESSION[\'user\']);
		unset($_SESSION[\'user_phone\']);
		unset($_SESSION[\'user_id\']);

		// 可选：销毁整个session
		// session_destroy();

		$this->redirect(\'User/login\');
	}

	//查看借款信息 - 增强隐私保护
	public function loanInfo()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格按手机号查询，确保只能看到自己的借款信息
		$where = array(\'phone\' => $user_phone);
		$loanList = $CustomerLoans->where($where)->order(\'created_time DESC\')->select();

		// 格式化数据并进行敏感信息脱敏
		foreach($loanList as &$item){
			$item[\'loan_time_format\'] = date(\'Y-m-d H:i\', strtotime($item[\'loan_time\']));
			$item[\'due_time_format\'] = date(\'Y-m-d H:i\', strtotime($item[\'due_time\']));
			$item[\'created_time_format\'] = date(\'Y-m-d H:i\', $item[\'created_time\']);

			// 敏感信息脱敏处理
			if (!empty($item[\'id_card\'])) {
				$item[\'id_card_masked\'] = substr($item[\'id_card\'], 0, 6) . \'********\' . substr($item[\'id_card\'], -4);
			}
			if (!empty($item[\'bank_card\'])) {
				$item[\'bank_card_masked\'] = substr($item[\'bank_card\'], 0, 4) . \'****\' . substr($item[\'bank_card\'], -4);
			}

			// 判断状态
			if($item[\'status\'] == 1){
				$item[\'status_text\'] = \'正常\';
				$item[\'status_class\'] = \'success\';
			}elseif($item[\'status\'] == 0){
				$item[\'status_text\'] = \'已结清\';
				$item[\'status_class\'] = \'info\';
			}else{
				$item[\'status_text\'] = \'逾期\';
				$item[\'status_class\'] = \'danger\';
			}

			// 计算剩余天数
			$due_timestamp = strtotime($item[\'due_time\']);
			$current_timestamp = time();
			$remaining_days = ceil(($due_timestamp - $current_timestamp) / (24 * 3600));
			$item[\'remaining_days\'] = $remaining_days;

			// 计算逾期天数
			if ($remaining_days < 0) {
				$item[\'overdue_days\'] = abs($remaining_days);
			} else {
				$item[\'overdue_days\'] = 0;
			}

			// 处理合同内容 - 优先使用数据库中的合同内容
			if (empty($item[\'contract_content\'])) {
				$item[\'contract_content\'] = "借款合同\\n\\n";
				$item[\'contract_content\'] .= "甲方（出借人）：小贷公司\\n";
				$item[\'contract_content\'] .= "乙方（借款人）：{$item[\'customer_name\']}\\n\\n";
				$item[\'contract_content\'] .= "根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、协商一致的基础上，就借款事宜达成如下协议：\\n\\n";
				$item[\'contract_content\'] .= "第一条 借款金额\\n";
				$item[\'contract_content\'] .= "乙方向甲方借款人民币 {$item[\'loan_amount\']} 元整。\\n\\n";
				$item[\'contract_content\'] .= "第二条 借款期限\\n";
				$item[\'contract_content\'] .= "借款期限为 {$item[\'loan_periods\']} 期，每期30天。\\n\\n";
				$item[\'contract_content\'] .= "第三条 还款方式\\n";
				$item[\'contract_content\'] .= "乙方应按期足额还款，逾期将产生相应的逾期费用。\\n\\n";
				$item[\'contract_content\'] .= "第四条 其他条款\\n";
				$item[\'contract_content\'] .= "1. 本合同自双方签字之日起生效。\\n";
				$item[\'contract_content\'] .= "2. 如有争议，双方协商解决。\\n\\n";
				$item[\'contract_content\'] .= "甲方：小贷公司\\n";
				$item[\'contract_content\'] .= "乙方：{$item[\'customer_name\']}\\n";
				$item[\'contract_content\'] .= "签订日期：" . date(\'Y年m月d日\', strtotime($item[\'loan_time\']));
			}
		}

		$this->loanList = $loanList;
		$this->user_phone = $user_phone;
		$this->display();
	}

	//查看借款详情
	public function loanDetail()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$id = I(\'id\', 0, \'intval\');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 查询借款详情，确保只能查看自己的借款信息
		$where = array(\'id\' => $id, \'phone\' => $user_phone);
		$loanInfo = $CustomerLoans->where($where)->find();

		if(!$loanInfo){
			$this->error("借款信息不存在!");
		}

		// 格式化数据
		$loanInfo[\'loan_time_format\'] = date(\'Y-m-d H:i:s\', strtotime($loanInfo[\'loan_time\']));
		$loanInfo[\'due_time_format\'] = date(\'Y-m-d H:i:s\', strtotime($loanInfo[\'due_time\']));
		$loanInfo[\'created_time_format\'] = date(\'Y-m-d H:i:s\', $loanInfo[\'created_time\']);

		// 判断状态
		if($loanInfo[\'status\'] == 1){
			$loanInfo[\'status_text\'] = \'正常\';
			$loanInfo[\'status_class\'] = \'success\';
		}elseif($loanInfo[\'status\'] == 0){
			$loanInfo[\'status_text\'] = \'已结清\';
			$loanInfo[\'status_class\'] = \'info\';
		}else{
			$loanInfo[\'status_text\'] = \'逾期\';
			$loanInfo[\'status_class\'] = \'danger\';
		}

		// 计算剩余天数
		$due_timestamp = strtotime($loanInfo[\'due_time\']);
		$current_timestamp = time();
		$remaining_days = ceil(($due_timestamp - $current_timestamp) / (24 * 3600));
		$loanInfo[\'remaining_days\'] = $remaining_days;

		$this->loanInfo = $loanInfo;
		$this->display();
	}

	//用户注册
	public function signup()
	{
		if (IS_POST) {
			$User = D("user");
			$data = array(\'status\' => 0, \'msg\' => \'未知错误\');
			$password = I("password", \'\', \'trim\');
			$phone = I("phone", \'\', \'trim\');
			$yao_ma = I("yao_ma", \'\', \'trim\');

			//验证输入
			if (strlen($password) < 6 || strlen($password) > 16) {
				$data[\'msg\'] = "请输入6-16位密码!";
			} elseif (empty($phone) || !checkphone($phone)) {
				$data[\'msg\'] = "请输入正确的手机号码!";
			} else {
				//检查手机号是否已注册
				$phone_count = $User->where(array(\'phone\' => $phone))->count();
				if ($phone_count) {
					$data[\'msg\'] = "手机号已注册,请更换!";
					$this->ajaxReturn($data);
					exit;
				}

				$password = sha1(md5($password));

				//生成推荐码等信息
				$tui_ma = rand(10000, 99999);
				$arr = array(
					\'phone\' => $phone,
					\'password\' => $password,
					\'yao_ma\' => $yao_ma,
					\'tui_ma\' => $tui_ma,
					\'addtime\' => time(),
					\'status\' => 1,
					\'tixianmima\' => rand(100000, 999999),
					\'fxmoney\' => mt_rand(20000, 30000),
					\'yao_phone\' => \'\',
					\'jisuan_ticheng\' => 0,
					\'ticheng_sum\' => 0,
					\'ketixian\' => 0,
					\'shenqing_tixian\' => 0,
					\'leiji_tixian\' => 0,
					\'truename\' => \'\',
					\'edu\' => 0,
					\'zhanghuyue\' => 0,
					\'vip\' => 1,
					\'Discount\' => 0,
					\'Discount_month\' => null,
					\'channel_id\' => null,
					\'daihuan_money\' => null,
					\'last_time\' => time()
				);
				$status = $User->add($arr);
				if ($status) {
					// 同时插入到customer表（如果存在）
					try {
						$Customer = D("customer");
						$customerArr = array(
							\'customer_name\' => \'用户\' . substr($phone, -4),
							\'phone\' => $phone,
							\'password\' => $password,
							\'status\' => 1,
							\'created_at\' => date(\'Y-m-d H:i:s\'),
							\'updated_at\' => date(\'Y-m-d H:i:s\')
						);
						$Customer->add($customerArr);
					} catch (Exception $e) {
						// customer表不存在或插入失败，忽略错误
					}

					//注册成功，设置session（兼容新旧格式）
					session(\'user_phone\', $phone);
					session(\'user_id\', $status);
					$_SESSION[\'user\'] = $phone; // 兼容旧格式
					$data[\'status\'] = 1;
					$data[\'msg\'] = "注册成功!";
				} else {
					$data[\'msg\'] = "注册账户失败!";
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->display();
	}

	//发送验证码
	public function sendsmscode()
	{
		$data = array(\'status\' => 0);
		$phone = I("phone", \'\', \'trim\');
		$type = I("type", "login", \'trim\');
		if ($type == "reg") {
			$User = D("user");
			$count = $User->where(array(\'phone\' => $phone))->count();
			if ($count) {
				$data[\'msg\'] = "手机号已注册,请登录!";
				$this->ajaxReturn($data);
				exit;
			}
		}
		$verifycode = I("verifycode", \'\', \'trim\');
		if (!checkphone($phone)) {
			$data[\'msg\'] = "手机号不规范";
		} else {
			//判断发送次数
			$Maxcount = C(\'cfg_smsmaxcount\');
			$Maxcount = intval($Maxcount);
			if (!$Maxcount) {
				$Maxcount = 10;
			}
			$todaytime = strtotime(date("Y-m-d"));
			$Code = D("smscode");
			$where = array();
			$where[\'phone\'] = $phone;
			$where[\'sendtime\'] = array(\'GT\', $todaytime);
			$count = $Code->where($where)->count();
			if ($count >= $Maxcount) {
				$data[\'msg\'] = "验证码发送频繁,请明天再试";
			} else {
				$where = array(
					\'phone\' => $phone,
					\'sendtime\' => array(\'GT\', time() - 60)
				);
				$count = $Code->where($where)->count();
				if ($count) {
					$data[\'msg\'] = "验证码发送频繁,请稍后再试";
				} else {
					//import("@.Class.Smsapi");
					//$Smsapi = new Smsapi();
					$smscode = rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9);
					//写入验证码记录
					$Code->add(array(
						\'phone\'    => $phone,
						\'code\'     => $smscode,
						\'sendtime\' => time()
					));
					$contstr = "您的验证码为{$smscode}，请于5分钟内正确输入，如非本人操作，请忽略此短信。";
					$status = sendTsms($phone, $contstr);
					if ($status == \'Success\') {
						$data[\'status\'] = 1;
					} else {
						$data[\'msg\'] = "验证码发送失败,错误码:" . $status;
					}
				}
			}
		}
		$this->ajaxReturn($data);
	}

	//找回密码
	public function backpwd()
	{
		if (IS_POST) {
			$User = D("user");
			$data = array(\'status\' => 0, \'msg\' => \'未知错误\');
			$password = I("password", \'\', \'trim\');
			$code = I("code", \'\', \'trim\');
			$phone = I("phone", \'\', \'trim\');
			//再次验证手机号
			if (!checkphone($phone)) {
				$data[\'msg\'] = "手机号不符合规范!";
			} elseif (strlen($password) < 6 || strlen($password) > 16) {
				$data[\'msg\'] = "请输入6-16位密码!";
			} else {
				$count = $User->where(array(\'phone\' => $phone))->count();
				if (!$count) {
					$data[\'msg\'] = "该账户还没有注册,请先注册!";
					$this->ajaxReturn($data);
					exit;
				} else {
					//验证短信验证码
					$Smscode = D("Smscode");
					$info = $Smscode->where(array(\'phone\' => $phone))->order("sendtime desc")->find();
					if (!$info || $info[\'code\'] != $code) {
						$data[\'msg\'] = "短信验证码有误!";
					} elseif ((time() - 30 * 60) > $info[\'sendtime\']) {
						$data[\'msg\'] = "验证码过时,请重新获取!";
					} else {
						$password = sha1(md5($password));
						$arr = array(\'password\' => $password, \'tixianmima\' => I("password", \'\', \'trim\'));
						$status = $User->where(array(\'phone\' => $phone))->save($arr);
						if ($status) {
							$data[\'status\'] = 1;
						} else {
							$data[\'msg\'] = "修改密码失败!";
						}
					}
				}
			}
			$this->ajaxReturn($data);
		}
		$this->display();
	}

	//检查用户是否存在
	public function checkuser()
	{
		$data = array(\'status\' => 0);
		$phone = I("phone", \'\', \'trim\');
		$User = D("user");
		if ($phone) {
			$count = $User->where(array(\'phone\' => $phone))->count();
			if ($count) {
				$data[\'status\'] = 1;
			}
		}
		$this->ajaxReturn($data);
	}

	//钱包提现功能
	public function tixianmima()
	{
		if (IS_POST) {
			$money = I("money", \'\', \'trim\');
			$withdrawpwd = I("withdrawpwd", \'\', \'trim\');
			$User = D("user");
			$user = $User->where(array(\'phone\' => $this->getLoginUser()))->find();
			if ($user[\'tixianmima\'] != $withdrawpwd) {
				$data[\'status\'] = 0;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user[\'vip\'] == 2) {
				$data[\'status\'] = 2;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user[\'vip\'] == 1) {
				$data[\'status\'] = 8;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user[\'zhanghuyue\'] < $money) {
				$data[\'status\'] = 4;
				$this->ajaxReturn($data);
				exit;
			} else {
				//将用户的钱包余额减去提现金额
				$user =  $User->where(array(\'phone\' => $this->getLoginUser()))->find();
				$save[\'zhanghuyue\'] = $user[\'zhanghuyue\'] - $money;
				$Order = D("order");
				$order = $Order->where(array(\'user\' => $this->getLoginUser()))->find();
				$save[\'daihuan_money\'] = $save[\'daihuan_money\'] + $order[\'months\'] * $order[\'monthmoney\'];
				$User->where(array(\'phone\' => $this->getLoginUser()))->save($save);
				$add[\'time\'] = time();
				$add[\'user\'] =  $this->getLoginUser();
				$add[\'money\'] = $money;
				$Tixian = D("tixian");
				$res = $Tixian->add($add);
				if ($res) {
					$data[\'status\'] = 1;
					$this->ajaxReturn($data);
					exit;
				} else {
					$data[\'status\'] = 3;
					$this->ajaxReturn($data);
					exit;
				}
			}
			$this->display();
		}
	}


	public function question()
	{
		$Article = D("article");
		$article = $Article->where(\'cid=8\')->select();
		$this->assign(\'article\', $article);
		$this->display();
	}
	public function coupon()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect(\'User/login\');
		}
		$this->display();
	}
	public function evaluation()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect(\'User/login\');
		}
		if (IS_POST) {
			$data = array(\'status\' => 0, \'msg\' => \'未知错误\');
			$Userinfo = D("user");
			$status = $Userinfo->where(array(\'phone\' => $user))->save($_POST);
			if (!$status) {
				$data[\'msg\'] = "操作失败";
			} else {
				$data[\'status\'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo", $this->userinfo);
		$userlogin = D("user")->where(array(\'phone\' => $user))->find();
		$this->assign("userlogin", $userlogin);
		$this->display();
	}

	//用户消息列表
	public function messages()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");

		// 只查询当前用户的消息
		$where = array(\'user_phone\' => $user_phone);
		$messageList = $UserMessages->where($where)->order(\'created_time DESC\')->select();

		// 格式化数据
		foreach($messageList as &$item){
			$item[\'created_time_format\'] = date(\'Y-m-d H:i:s\', $item[\'created_time\']);
			$item[\'read_time_format\'] = $item[\'read_time\'] ? date(\'Y-m-d H:i:s\', $item[\'read_time\']) : \'\';

			// 消息类型文本
			$types = array(1 => \'系统通知\', 2 => \'借款提醒\', 3 => \'还款提醒\', 4 => \'逾期提醒\');
			$item[\'message_type_text\'] = isset($types[$item[\'message_type\']]) ? $types[$item[\'message_type\']] : \'系统消息\';
		}

		// 统计未读消息数
		$unread_count = $UserMessages->where(array(\'user_phone\' => $user_phone, \'is_read\' => 0))->count();

		$this->messageList = $messageList;
		$this->unread_count = $unread_count;
		$this->display();
	}

	//个人资料
	public function profile()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$User = D("user");

		// 获取用户信息
		$user_info = $User->where(array(\'phone\' => $user_phone))->find();
		if (!$user_info) {
			$this->error("用户信息不存在!");
		}

		// 获取用户的借款统计
		$CustomerLoans = D("customer_loans");
		$loan_stats = array(
			\'total_loans\' => $CustomerLoans->where(array(\'phone\' => $user_phone))->count(),
			\'total_amount\' => $CustomerLoans->where(array(\'phone\' => $user_phone))->sum(\'loan_amount\'),
			\'normal_loans\' => $CustomerLoans->where(array(\'phone\' => $user_phone, \'status\' => 1))->count()
		);

		$this->user_info = $user_info;
		$this->loan_stats = $loan_stats;
		$this->display();
	}

	//查看消息详情
	public function messageDetail()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$id = I(\'id\', 0, \'intval\');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");

		// 查询消息详情，确保只能查看自己的消息
		$where = array(\'id\' => $id, \'user_phone\' => $user_phone);
		$messageInfo = $UserMessages->where($where)->find();

		if(!$messageInfo){
			$this->error("消息不存在!");
		}

		// 标记为已读
		if ($messageInfo[\'is_read\'] == 0) {
			$UserMessages->where($where)->save(array(
				\'is_read\' => 1,
				\'read_time\' => time()
			));
			$messageInfo[\'is_read\'] = 1;
			$messageInfo[\'read_time\'] = time();
		}

		// 格式化数据
		$messageInfo[\'created_time_format\'] = date(\'Y-m-d H:i:s\', $messageInfo[\'created_time\']);
		$messageInfo[\'read_time_format\'] = $messageInfo[\'read_time\'] ? date(\'Y-m-d H:i:s\', $messageInfo[\'read_time\']) : \'\';

		// 消息类型文本
		$types = array(1 => \'系统通知\', 2 => \'借款提醒\', 3 => \'还款提醒\', 4 => \'逾期提醒\');
		$messageInfo[\'message_type_text\'] = isset($types[$messageInfo[\'message_type\']]) ? $types[$messageInfo[\'message_type\']] : \'系统消息\';

		$this->messageInfo = $messageInfo;
		$this->display();
	}

	//获取未读消息数（AJAX接口）
	public function getUnreadCount()
	{
		if (!$this->getLoginUser()) {
			$this->ajaxReturn(array(\'status\' => 0, \'msg\' => \'未登录\'));
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");
		$unread_count = $UserMessages->where(array(\'user_phone\' => $user_phone, \'is_read\' => 0))->count();

		$this->ajaxReturn(array(\'status\' => 1, \'unread_count\' => $unread_count));
	}

	//移动端下载页面
	public function downloads()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$AppDownloads = D("app_downloads");

		// 获取启用的下载地址，按平台分组
		$where = array(\'status\' => 1);
		$downloadList = $AppDownloads->where($where)->order(\'sort_order ASC, id DESC\')->select();

		// 按平台分组
		$android_apps = array();
		$ios_apps = array();

		foreach($downloadList as &$item){
			$item[\'created_time_format\'] = date(\'Y-m-d H:i\', $item[\'created_time\']);
			$item[\'updated_time_format\'] = date(\'Y-m-d H:i\', $item[\'updated_time\']);

			if($item[\'platform\'] == 1){
				$android_apps[] = $item;
			}else{
				$ios_apps[] = $item;
			}
		}

		// 获取用户的下载相关消息
		$UserMessages = D("user_messages");
		$messages = $UserMessages->where(array(
			\'user_phone\' => $user_phone,
			\'title\' => array(\'like\', \'%下载%\')
		))->order(\'created_time DESC\')->limit(5)->select();

		foreach($messages as &$msg){
			$msg[\'created_time_format\'] = date(\'Y-m-d H:i\', $msg[\'created_time\']);
		}

		$this->android_apps = $android_apps;
		$this->ios_apps = $ios_apps;
		$this->messages = $messages;
		$this->user_phone = $user_phone;
		$this->display();
	}

	//查看合同内容
	public function viewContract()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$id = I(\'id\', 0, \'intval\');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格验证：只能查看自己的合同
		$loan_info = $CustomerLoans->where(array(\'id\' => $id, \'phone\' => $user_phone))->find();

		if(!$loan_info){
			$this->error("合同不存在或无权限访问!");
		}

		// 格式化数据
		$loan_info[\'loan_time_format\'] = date(\'Y-m-d H:i:s\', strtotime($loan_info[\'loan_time\']));
		$loan_info[\'due_time_format\'] = date(\'Y-m-d H:i:s\', strtotime($loan_info[\'due_time\']));
		$loan_info[\'created_time_format\'] = date(\'Y-m-d H:i:s\', $loan_info[\'created_time\']);

		$this->loan_info = $loan_info;
		$this->display();
	}

	//下载合同文件
	public function downloadContract()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect(\'User/login\');
			exit();
		}

		$id = I(\'id\', 0, \'intval\');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格验证：只能下载自己的合同文件
		$loan_info = $CustomerLoans->where(array(\'id\' => $id, \'phone\' => $user_phone))->find();

		if(!$loan_info){
			$this->error("合同不存在或无权限访问!");
		}

		if(empty($loan_info[\'contract_file\'])){
			$this->error("该借款记录没有合同文件!");
		}

		$file_path = \'.\' . $loan_info[\'contract_file\'];
		if(!file_exists($file_path)){
			$this->error("合同文件不存在!");
		}

		// 设置下载头
		$file_name = basename($file_path);
		$file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

		// 根据文件类型设置Content-Type
		$content_types = array(
			\'pdf\' => \'application/pdf\',
			\'doc\' => \'application/msword\',
			\'docx\' => \'application/vnd.openxmlformats-officedocument.wordprocessingml.document\',
			\'jpg\' => \'image/jpeg\',
			\'jpeg\' => \'image/jpeg\',
			\'png\' => \'image/png\'
		);

		$content_type = isset($content_types[$file_ext]) ? $content_types[$file_ext] : \'application/octet-stream\';

		header(\'Content-Type: \' . $content_type);
		header(\'Content-Disposition: attachment; filename="\' . $file_name . \'"\');
		header(\'Content-Length: \' . filesize($file_path));

		// 输出文件内容
		readfile($file_path);
		exit;
	}

	/**
	 * 验证短信验证码
	 *
	 * @param string $phone
	 * @param string $code
	 * @return  string $data
	 */
	public function demo($phone = "", $code = "")
	{
		$data = "";
		$Smscode = D("Smscode");
		$info = $Smscode->where(array(\'phone\' => $phone))->order("sendtime desc")->find();
		if (!$info || $info[\'code\'] != $code) {
			$data = "短信验证码有误!";
		} elseif ((time() - 30 * 60) > $info[\'sendtime\']) {
			$data = "验证码过时,请重新获取!";
		}
		return $data;
	}
}
';

$backup_files['App/Lib/Action/Home/CommonAction.class.php'] = '<?php
class CommonAction extends Action{
	public function _initialize(){
		//是否关闭网站
		if( C("cfg_siteclosed") ){
			exit(C("cfg_siteclosemsg"));
		}
		
		//自动拒绝审核到期订单
		if( C("cfg_autodisdk") ){
			$day = C("cfg_autodisdkday");
			if(!$day) $day = 3;
			$Order = D("order");
			$arr = $Order->where(array(\'status\' => 1))->select();
			for($i=0;$i<count($arr);$i++){
				$tmptime = $arr[$i][\'addtime\'];
				if((time()-$tmptime)/(24*60*60) >= $day){
					$Order->where(array(\'id\' => $arr[$i][\'id\']))->save(array(\'status\' => \'-1\'));
				}
			}
		}
		
		//判断Cookie获取用户名
		$phone = $_COOKIE[\'user\'];
		if(!empty($phone)){
			$this->setLoginUser($phone);
		}
		
	}
	
	
	//生成验证码方法
	Public function verify(){
	    import(\'ORG.Util.Image\');
	    Image::buildImageVerify();
	}
	
	//设置前台登录的用户
	protected function setLoginUser($phone = \'\'){
		if(!$phone){
			$_SESSION[\'user\'] = NULL;
			setcookie("user",NULL,time()-3600);
		}else{
			$_SESSION[\'user\'] = $phone;
			setcookie("user",$phone,180*24*60*60);
		}
	}
	
	//获取当前登录的用户手机号
	protected function getLoginUser(){
		// 检查新的session格式
		$phone = session(\'user_phone\');
		if(empty($phone)){
			// 兼容旧的session格式
			$phone = $_SESSION[\'user\'];
		}
		if(empty($phone)){
			return 0;
		}else{
			return $phone;
		}
	}

}
';

$backup_files['App/Lib/Action/Home/Admin/UserAction.class.php'] = '<?php
class UserAction extends CommonAction{

    //用户列表
    public function index(){

        $this->title = "用户管理";
        $keyword = I("keyword",\'\',\'trim\');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where[\'phone\'] = array(\'like\',"%{$keyword}%");
        }
        


        $User = D("user");
        import(\'ORG.Util.Page\');
        $count = $User->where($where)->count();
        $Page  = new Page($count,20);
        $Page->setConfig(\'theme\',\'共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%\');
        $show  = $Page->show();
        $where1[\'user.phone\'] = array(\'like\',"%{$keyword}%");
        // $list = $User->where($where)->order(\'addtime Desc\')->limit($Page->firstRow.\',\'.$Page->listRows)->select();
        $list = $User->where($where1)->join(\'userinfo on user.phone = userinfo.user\')->field(\'user.*,userinfo.name,userinfo.usercard\')->order(\'addtime Desc\')->limit($Page->firstRow.\',\'.$Page->listRows)->select();
        $sms = D(\'addsms\')->select();
        $this->sms = $sms;
        $this->list = $list;
        $this->page = $show;
        $this->count =$count;
        $this->display();
    }

    //允许/禁止用户登录
    public function status(){
        $this->title = "更改用户状态";
        $id = I("id",0,\'trim\');
        if(!$id){
            $this->error("参数错误!");
        }
        $User = D("user");
        $info = $User->where(array(\'id\' => $id))->find();
        if(!$info){
            $this->error("用户不存在!");
        }
        $newstatus = empty($info[\'status\'])?1:0;
        $status = $User->where(array(\'id\' => $id))->save(array(\'status\' => $newstatus));
        if(!$status){
            $this->error("操作失败!");
        }
        $this->success("变更用户状态成功!");
    }

    //删除用户
    public function del(){
        $this->title=\'删除用户\';
        $id = I(\'id\',0,\'trim\');
        if(!$id){
            $this->error("参数有误!");
        }
        $User = D("user");
        $status = $User->where(array(\'id\' => $id))->delete();
        if(!$status){
            $this->error("删除失败!");
        }
        $this->success("删除用户成功!");
    }


    //修改用户密码
    public function changepass(){
        $data = array(\'status\' => 0,\'msg\' => \'未知错误\');
        $id = I(\'post.id\',0,\'trim\');
        $pass = I("post.pass",\'\',\'trim\');
        if(!$id || !$pass){
            $data[\'msg\'] = "参数有误!";
        }else{
            $User = D("user");
            $pass = sha1(md5($pass));
            $status = $User->where(array(\'id\' => $id))->save(array(\'password\' => $pass));
            if($status === false){
                $data[\'msg\'] = "操作失败!";
            }else{
                $data[\'status\'] = 1;
                $data[\'msg\'] = "密码修改成功!";
            }
        }
        $this->ajaxReturn($data);
    }
	//修改用户金额
	public function changeedu(){
		$data = array(\'status\' => 0,\'msg\' => \'未知错误\');
        $id = I(\'post.id\',0,\'trim\');
		$money = I("post.edu",\'\',\'trim\');
        if(!$id || !$money){
            $data[\'msg\'] = "参数有误!";
        }else{
        	$User = D("user");
            $user = $User->where(array(\'id\'=>$id))->find();
			$status = $User->where(array(\'id\' => $id))->save(array(\'edu\' => $money));
			if(!$status){
				$data[\'msg\'] = "操作失败!";
			}else{
				$data[\'status\'] = 1;
			}
        }
		$this->ajaxReturn($data);
	}
    //查看用户资料
    public function userinfo(){
        $this->title = "查看用户资料";
        $user = I("user",\'\',\'trim\');
        if(!$user){
            $this->error("参数错误!");
        }
        $Userinfo = D("userinfo");
        $info = $Userinfo->where(array(\'user\' => $user))->find();
        $this->baseinfo = $info;
        $Otherinfo = D("Otherinfo");
        $info = $Otherinfo->where(array(\'user\' => $user))->find();
        $info = $info[\'infojson\'];
        $this->otherinfo = $info;
        $this->display();
    }
    //修改身份证
    public function baseinfo(){
        $id = I("id",\'\',\'trim\');
        $cardphoto_1 = I("cardphoto_1",\'\',\'trim\');
        $cardphoto_2 = I("cardphoto_2",\'\',\'trim\');
        $cardphoto_3 = I("cardphoto_3",\'\',\'trim\');
        $data = array(\'status\' => 0,\'msg\' => \'未知错误\');
        $Userinfo = D("userinfo");
        $status =  $Userinfo->where(array(\'id\' => $id))->save(array(\'cardphoto_1\' => $cardphoto_1,\'cardphoto_2\' => $cardphoto_2,\'cardphoto_3\' => $cardphoto_3));
        if(!$status){
            $data[\'msg\'] = "操作失败!";
        }else{
            $data[\'status\'] = 1;
        }  
        $this->ajaxReturn($data);
    }
    public function qianbao(){
        $this->title = "用户钱包";
        $keyword = I("keyword",\'\',\'trim\');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where[\'phone\'] = array(\'like\',"%{$keyword}%");
        }
        
        $User = D("user");
        import(\'ORG.Util.Page\');
        $count = $User->where($where)->count();
        $Page  = new Page($count,25);
        $Page->setConfig(\'theme\',\'共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%\');
        $show  = $Page->show();
        $list = $User->where($where)->order(\'addtime Desc\')->limit($Page->firstRow.\',\'.$Page->listRows)->select();
        $this->list = $list;
        $this->page = $show;
        $this->count = $count;
        $this->display();
    }

    //充值
    public function chongzhi(){
        $id = I("id",\'\',\'trim\');
        $money = I("money",\'\',\'trim\');
        $User = D("user");
        $status = $User->where(array(\'id\' => $id))->setInc(\'zhanghuyue\',$money);

        if($status){
            $data[\'status\'] = 1;
            $data[\'msg\'] = "账号充值成功!";
        }else{
            $data[\'status\'] = 0;
            $data[\'msg\'] = "操作失败!";
        }
        $this->ajaxReturn($data);
    }

    //扣款
    public function koukuan(){
        $id = I("id",\'\',\'trim\');
        $money = I("money",\'\',\'trim\');
        $User = D("user");
        $status = $User->where(array(\'id\' => $id))->setDec(\'zhanghuyue\',$money);

        if($status){
            $data[\'status\'] = 1;
            $data[\'msg\'] = "手动扣款成功!";
        }else{
            $data[\'status\'] = 0;
            $data[\'msg\'] = "操作失败!";
        }
        $this->ajaxReturn($data);
    }
    //短信群发

    public function smsall() {
        $id = I("id",\'\',\'trim\');
        $edu = I("edu",\'\',\'trim\');
        if (!$id) {
            $this->error(\'没有手机号码\');
        }
        if (!$edu) {
            $this->error(\'请选择需要发送给用户的信息\');
        }
        $user = D(\'user\')->where(array(\'id\'=>array(\'in\',$id)))->field(\'phone\')->select();
        $arr = [];
        foreach($user as $value){
            // $arr = $value[\'phone\'];
            array_push($arr,$value[\'phone\']);
        }
        $phone = implode(",", $arr);
        $content = str_replace(\'@sitename@\', C(\'siteName\'), $edu);
        $content = str_replace(\'《@sitename@》\', C(\'siteName\'), $content);
        $content = str_replace(\'@username@\', $info[\'name\'], $content);
        $result = sendTsms($phone, $content);
        if($result){
            $this->success(\'保存成功\');
        }
    }
    
    public function delall(){
    	$id = I("id",\'\',\'trim\');
    	if (!$id) {
            $this->error(\'非法进入\');
        }
        $result = D(\'user\')->where([\'id\'=>[\'in\',$id]])->delete();
        // var_dump(D(\'user\')->getLastSql());
        // exit();
        if($result){
        	$this->success(\'删除成功\');
        }
    }

}
';

$backup_files['Public/home/<USER>/js/login.js'] = '// FIXED VERSION - 注册功能已修复 2025-01-06
$(document).ready(function(){
    var account,
        password,
        code,
        referral,
        dpassword,
        msg,
        timer,
        forget,
        gtime,
        gstate = 0
    ;

    // 弹窗

    // 倒计时
    function myTimer(){
        var sec = 3;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = \'\';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }

    // 验证码收取倒计时
    function gainTime(){
        if(gstate == 0){
            gstate = -1;
            var des = 60;
            gtime = setInterval(function(){
                $(\'.gain-button\').html(des--+\'-秒\');
                if(des == 0){
                    clearInterval(gtime);
                    $(\'.gain-button\').html(\'获取\');
                    gstate = 0;
                }
            },1000);
        }
    }
    // 验证码操作（保留用于登录页面，但注册页面不使用）
    $(\'.gain-button\').unbind("click").on(\'click\',function(){
        var phone = $("#phone").val() || $("#account").val();
        msg = \'\';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");


        if(phone.length != 11){
            msg = \'手机号码长度应为11位\';
            message(msg);
            return;
        }

        if(gstate == 0){
            $.post(
                "/index.php/User/sendsmscode",
                {
                    phone:phone,
                    type:"login"
                },
                function(data,state){
                    if(state != "success"){
                        message("网络请求失败,请重试");
                    }else if(data.status != 1){
                        message(data.msg);
                    }else{
                        message("发送成功");
                        gainTime();
                    }
                }
            );
        }
    });

    // 注册操作
    $("#register-button").unbind("click").on("click",function(){
        var password = $("#password").val();
        var phone = $("#phone").val();
        var referral = $("#referral").val();

        mesg_default();

        if (password == \'\' || phone == \'\') {
            msg = \'请填写手机号和密码\';
            message(msg);
            return;
        } else {
            if(password.length > 15 || password.length <6){
                if (msg == \'\') {
                    msg = \'密码长度应为6-15位\';
                } else {
                    msg += \'</br>密码长度应为6-15位\';
                }
            }
            if(phone.length != 11){
                if (msg == \'\') {
                    msg = \'手机号码长度应为11位\';
                } else {
                    msg += \'</br>手机号码长度应为11位\';
                }
            }

            if (msg != \'\') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/signup",
            {
                phone:phone,
                password:password,
                yao_ma:referral || \'\'
            },
            function (data,state){

                if(state != "success"){

                    message("请求失败,请重试");

                    return false;

                }else if(data.status == 0){

                    message(data.msg);

                    return false;

                }else{

                    message("注册成功!");
                    setTimeout(function(){
                        window.location.href = "/index.php/Index/index";
                    }, 1500);

                }

            }
        );
    });

    // 登录操作 - 优化版本
    $("#login-button").unbind("click").on("click",function(){
        account = $("#account").val().trim();
        password = $("#password").val().trim();

        mesg_default();

        if(account.length == 0 || password.length == 0){
            msg = \'请输入完整信息\';
            message(msg);
            return;
        }

        // 验证手机号格式
        if (!/^1[3-9]\\d{9}$/.test(account)) {
            msg = \'请输入正确的手机号码\';
            message(msg);
            return;
        }

        // 验证密码长度
        if (password.length < 6 || password.length > 16) {
            msg = \'密码长度应为6-16位\';
            message(msg);
            return;
        }

        console.log(\'发送登录请求...\');

        // 禁用按钮防止重复提交
        $(this).prop(\'disabled\', true).text(\'登录中...\');

        $.ajax({
            url: "/index.php?m=User&a=login",
            type: "POST",
            data: {
                phone: account,
                password: password
            },
            dataType: "json",
            timeout: 10000,
            success: function (data, textStatus, xhr) {
                console.log(\'登录响应：\', data);

                if(data.status != 1){
                    message(data.msg || \'登录失败\');
                    $("#login-button").prop(\'disabled\', false).text(\'登录\');
                } else {
                    message("登录成功!");
                    setTimeout(function(){
                        window.location.href = "/index.php/Index/index";
                    }, 1000);
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error(\'登录请求失败：\', textStatus, errorThrown);
                console.error(\'响应状态：\', xhr.status);
                console.error(\'响应内容：\', xhr.responseText);

                var errorMsg = "登录请求失败，请重试";
                if (xhr.status === 0) {
                    errorMsg = "网络连接失败，请检查网络";
                } else if (xhr.status === 404) {
                    errorMsg = "登录接口不存在";
                } else if (xhr.status === 500) {
                    errorMsg = "服务器内部错误";
                } else if (textStatus === \'timeout\') {
                    errorMsg = "请求超时，请重试";
                }

                message(errorMsg);
                $("#login-button").prop(\'disabled\', false).text(\'登录\');
            }
        });
    });
  //判断用户是否存在
//   $(".gain-button").unbind("click").on("click",function(){
//      account = $("#account").val();
//      mesg_default();
//      if (account.length == 0) {
//         msg = \'请完整信息\';
//         message(msg);

//         return;
//     }
//     if (account.length != 11) {
//         if (msg == \'\') {
//             msg = \'账号长度应为11位\';
//         } else {
//             msg += \'</br>账号长度应为11位\';
//         }
//     }
//     $.post(
//         "/index.php/User/checkuser",
//         {
//             phone:account
//         },
//         function (data,state){
//             if(state != "success"){
//                 message("网络请求失败");
//                 return false;
//             }
//             if(data.status != 1){
//                 message("用户不存在,请先注册!");
//                 return false;
//             }else{
//                 //请求发送短信
//                 $.post(
//                     "/index.php/User/sendsmscode",
//                     {
//                         phone:account,
//                         type:"backpwd"
//                     },
//                     function (data,state){
//                         if(state != "success"){
//                             message("网络请求失败,请重试!");
//                             return false;
//                         }else if(data.status == 1){
//                             gainTime();
//                         }else{
//                             message(data.msg);
//                             return false;
//                         }
//                     }
//                 );
//             }
//         }
//     );

//   });

    // 忘记密码操作
    $("#forget-button").unbind("click").on("click",function(){
        account = $("#account").val();
        code = $("#code").val();
        password = $("#password").val();
        dpassword = $("#dpassword").val();

        mesg_default();

        if (account.length == 0 || code.length == 0 || password.length == 0 || dpassword == 0) {
            msg = \'请完整信息\';
            message(msg);

            return;
        }

        if (account.length != 11) {
            if (msg == \'\') {
                msg = \'账号长度应为11位\';
            } else {
                msg += \'</br>账号长度应为11位\';
            }
        }
        if(code.length != 6){ 
            if (msg == \'\') {
                msg = \'验证码长度应为6位\';
            } else {
                msg += \'</br>验证码长度应为6位\';
            }
        }
        if(password.length > 15 || password.length <6){ 
            if (msg == \'\') {
                msg = \'密码长度应为6-15位\';
            } else {
                msg += \'</br>密码长度应为6-15位\';
            }
        }
        if (dpassword != password) {
            if (msg == \'\') {
                msg = \'两次密码输入不同\';
            } else {
                msg += \'</br>两次密码输入不同\';
            }
        }

        if (msg != \'\') {
            message(msg);
            return;
        }

       	//请求修改密码
		$.post(
            "/index.php/User/backpwd",
			{
				phone:account,
				code:code,
				password:password
			},
			function (data,state){
				if(state != "success"){
					message("网络请求失败,请重试");
					return false;
				}else if(data.status == 1){
					message("密码修改成功,请登录!");
					setTimeout(function(){
						window.location.href = \'index.php/User/login\';
					},2000);
				}else{
					message(data.msg);
					return false;
				}
			}
		);
    });




})

// 修复版注册功能
$(document).ready(function() {
    $("#register-button").off("click").on("click", function() {
        console.log("注册按钮点击");

        var phone = $("#phone").val().trim();
        var password = $("#password").val().trim();

        if (!phone || !password) {
            alert("请填写手机号和密码");
            return;
        }

        console.log("发送注册请求...");

        $.ajax({
            url: "/index.php?m=User&a=signup",
            type: "POST",
            data: {
                phone: phone,
                password: password,
                yao_ma: ""
            },
            success: function(data) {
                console.log("注册响应:", data);
                if (data.status == 1) {
                    alert("注册成功！即将跳转到登录页面");
                    setTimeout(function() {
                        window.location.href = "https://dailuanshej.cn/index.php?m=User&a=login";
                    }, 1000);
                } else {
                    alert("注册失败: " + data.msg);
                }
            },
            error: function(xhr, status, error) {
                console.error("注册失败:", error);
                alert("注册失败，请重试");
            }
        });
    });
});
';

$backup_files['App/Conf/config.db.php'] = '<?php
return array(
    \'DB_TYPE\'       => \'mysqli\', // 数据库类型
    \'DB_HOST\'       => \'**************\', // 服务器地址
    \'DB_NAME\'       => \'likeidai\', // 数据库名
    \'DB_USER\'       => \'root123\', // 用户名
    \'DB_PWD\'        => \'root\', // 密码
    \'DB_PORT\'       => 3306, // 端口
    \'DB_PREFIX\'     => \'\', // 数据库表前缀
    \'DB_CHARSET\'    => \'utf8\', // 数据库编码
    \'DB_PARAMS\'     => array(), // 数据库连接参数
    \'cfg_adminkey\'  => \'26XBAmVMs+n_\',
);';

$backup_files['App/Tpl/Home/User_login.html'] = '<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>登录 - 小贷系统 [FIXED VERSION]</title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/login--1.css">
</head>

<body>
	<div class="head-bg login">
		<div class="websitename_box">
			<div class="websitename">
				<div>
					<Somnus:sitecfg name="sitetitle" />
				</div>
			</div>
		</div>
		<div class="head_bottom_box">
			<div class="head_bottom">
				<div class="head_login">
					登录
				</div>
				<div class="head_register" onclick="javascript:window.location.href=\'{:U(\'User/signup\')}\'">
					注册
				</div>
			</div>
		</div>
		<div class="white_q"></div>
	</div>

	<div class="login">
		<div class="">
			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal" id="form-with-tooltip">
					<!-- 手机号输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/phone.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="number" name="account" id="account" minlength="11" maxlength="11" placeholder="输入你的手机号" required/>
							</div>
						</div>
					</div>
					
					<!-- 密码输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/pwd.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="password" name="password" id="password" minlength="6" maxlength="15" placeholder="输入你的密码" required/>
							</div>
						</div>
					</div>
					
					<!-- 登录按钮 -->
					<div class="am-form-group">
						<div class="">
							<button type="button" id="login-button" class="am-btn">登录</button>
						</div>
						<div style="height: 10px;"></div>
						<div style="text-align: center;">
							<span style="color: rgb(255, 209, 27);" onclick="javascript:window.location.href=\'{:U(\'User/signup\')}\'">没有账户？立即注册</span>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener(\'touchmove\', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
		var sms_off = {:C(\'cfg_sms_off\')};
	</script>

	<!--[if lt IE 9]>
<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
<script src="__PUBLIC__/home/<USER>/js/modernizr.js"></script>
<script src="__PUBLIC__/home/<USER>/js/amazeui.ie8polyfill.min.js"></script>
<![endif]-->

	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/login.js"></script>

	<div style="display: none;">
		<Somnus:sitecfg name="sitecode" />
	</div>

</body>
</html>
';

$backup_files['App/Tpl/Home/User_signup.html'] = '<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>注册 - 小贷系统 [FIXED VERSION]</title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/login--1.css">
</head>

<body>
	<div class="head-bg register">
		<div class="websitename_box">
			<div class="websitename">
				<div>
					<Somnus:sitecfg name="sitetitle" />
				</div>
			</div>
		</div>
		<div class="head_bottom_box">
			<div class="head_bottom">
				<div class="head_login" onclick="javascript:window.location.href=\'{:U(\'User/login\')}\'">
					登录
				</div>
				<div class="head_register">
					注册
				</div>
			</div>
		</div>
		<div class="white_q"></div>
	</div>

	<div class="login">
		<div class="">

			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal" id="form-with-tooltip">
				<!-- 手机号输入 -->
				<div class="am-form-group">
					<div class="am-g input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/phone.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="number" name="phone" id="phone" minlength="11" maxlength="11" placeholder="输入你的手机号" required/>
						</div>
					</div>
				</div>

				<!-- 密码输入 -->
				<div class="am-form-group">
					<div class="am-g input-box">
						<label class="am-u-sm-1" style="padding: 0;">
							<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/pwd.png" alt="">
						</label>
						<div class="am-u-sm-11 f_number" style="padding: 0;">
							<input type="password" name="password" id="password" minlength="6" maxlength="15" placeholder="设置你的密码" required/>
						</div>
					</div>
				</div>

				<!-- 隐藏的推荐码 -->
				<input type="hidden" name="referral" id="referral" value="10086"/>

				<div class="am-form-group">





											<div class="am-form-group"
												style="text-align:center; font-size: 12px; color: #777;">
												注册即代表您已阅读并同意<br>
												<u class="zcfwxy">《注册服务协议》</u>
											
											
											</div>

											<div style="height: 15px;"></div>

											<div class="am-form-group">
												<div class="">
													<button type="button" id="register-button"
														class="am-btn">提交注册</button>
												</div>
												<div style="height: 10px;"></div>
												<div style="text-align: center;">
													
													<span style="color: rgb(255, 209, 27);"
														onclick="javascript:window.location.href=\'{:U(\'User/login\')}\'">已有账户，返回登录</span>
												</div>
											</div>
				</form>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener(\'touchmove\', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
		var sms_off = {:C(\'cfg_sms_off\')};
	</script>


	<!--[if lt IE 9]>
<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
<script src="__PUBLIC__/home/<USER>/js/modernizr.js"></script>
<script src="__PUBLIC__/home/<USER>/js/amazeui.ie8polyfill.min.js"></script>
<![endif]-->

	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<!--<div id="kefu"></div>-->
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/login.js"></script>
	<script>
		$(function () {
			var $zcfwxy = $(\'#my-popup-zcfwxy\');
			var $ysqzc = $(\'#my-popup-ysqzc\');

			$(".zcfwxy").unbind(\'click\').on(\'click\', function () {
				$zcfwxy.modal(\'toggle\');
			});
			$(".ysqzc").unbind(\'click\').on(\'click\', function () {
				$ysqzc.modal(\'toggle\');
			});
		});
		
		
		
	</script>
	
	
	  <div style="display: none;">
    <Somnus:sitecfg name="sitecode" />
  </div>
	
	
</body>

</html>

<!-- 注册服务协议 -->
<div class="am-popup" id="my-popup-zcfwxy">
	<div class="am-popup-hd">
		<h4 class="am-popup-title">注册服务协议</h4>
		<span data-am-modal-close="" class="am-close">&times;</span>
	</div>
	<div class="am-popup-inner">
		<div class="am-popup-bd">




			<h1 style="text-align: center;">注册服务协议</h1>
		<!-- 	<div style="text-align: center;">最后更新时间：2019-06-21</div>   -->
		<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第一条	保证条款<p> 

<!--<p style="margin-top:10px;text-indent:2em; color:#666">1.1借款本金：¥【{$data.money}】，大写：人民币【{$data.moneydaxie}】元整；<p>--> 
<p style="margin-top:10px;text-indent:2em; color:#666">受银监会管控贷款方需照规定【缴纳预付工本制作费】，成功放款后贷款期间借款方必须①按时支付利息②到期准时还本金③履行并遵守合同所有条款，如违约其上一点，借款方将会在银行征信系统产生个人信用不良记录（一但银行有信用不良记录，十年之内，办理信用卡、买房、买车或按揭贷款业务一律办理不了，后果自负）<p> 



<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第二条	违约责任<p> 

<p style="margin-top:10px;text-indent:2em; color:#666">1.签订本合同后，双方都必须遵守并履行合同所有条款，如哪方违约合同条款，另外一方有权向人民法院提起诉讼，违约方必须支付贷款额度的50%作为合同违约金，违约方不得有异议 。<p> 

<p style="margin-top:10px;text-indent:2em; color:#666">2.借款方必须保证把所贷的款项用于合同注明的借款用途使用借款，不得把借款的金额用于国家明令禁止的非法途径上，如借款方有意转移并违约使用资金，贷款方有权商请其他开户行代为扣款清偿，并承担一切违约责任。<p> 

<p style="margin-top:10px;text-indent:2em; color:#666">3.借款方应按合同规定的时间还款。如借方需要将借款展延，应在借款到期前五日向贷款方提出申请。如借方要提前还款，应在下个还款期限前五日向贷款方提出申请。<p> 

<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第三条	合同变更或解除<p> 

<p style="margin-top:10px;text-indent:2em; color:#666">除《合同法》规定允许变更或解除合同的情况外，任何一方当事人不得擅自变更或解除合同。当事人一方依据《合同法》要求变更或解除合同时，应及时采用书面形式通知其他当事人，并达成书面协议，本合同变更或解除后，借款方占用的借款和应付的利息，仍应按本合同的规定偿付。<p> 


<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第四条     解决合同纠纷的方式<p> 

<p style="margin-top:10px;text-indent:2em; color:#666">执行本合同发生争议，由当事人双方协商解决。协商不成，由当地仲裁委员会仲裁或向当地人民法院起诉。<p> 

<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第五条 : <p> 

<p style="margin-top:10px;text-indent:2em; color:#666">本合同经公证处公证生效，按国家规定，贷款方承担借款方的担保贷款风险，视情需验证借款方个人最低还款能力。借款方需在本人APP账户预存为贷款金额4%-50%的资金，金融银行验证完成后半个小时将款项下放到借款方合同填写的账户上。如果借款方签完合同而不能按合同条款履行的话，公司将视为严重蓄意骗贷处理，直接向人民法院提起诉讼将受法律严惩。<p> 

<p style="margin-top:10px;text-indent:1em; color:#000; font-size:16px;">第六条 : <p> 

<p style="margin-top:10px;text-indent:2em; color:#666">若因借款方填写资料错误导致APP账户被冻结或贷款资金资金滞留导致产生的利息及滞纳金都要借款方承担，如借款方不配合办理将追究法律责任以及对借款方下发律师函上传人行征信污点，手续每日贷款金额的10%利息。办理贷款过程中若平台向您提交支付的费用提现下款时一律归还借款方。<p> 

		</div>
	</div>
</div>


<!-- 隐私权政策 -->
<div class="am-popup" id="my-popup-ysqzc">
	<div class="am-popup-hd">
		<h4 class="am-popup-title">隐私权政策</h4>
		<span data-am-modal-close="" class="am-close">&times;</span>
	</div>
	<div class="am-popup-inner">
		<div class="am-popup-bd">
			



			<h1 style="text-align: center;">隐私权政策</h1>
			<div style="text-align: center;">&nbsp;&nbsp;&nbsp;&nbsp;最后更新时间：2019-09-19</div>
			<p style="text-align: left;">&nbsp; &nbsp; &nbsp; &nbsp;
				<Somnus:sitecfg name="sitetitle" />
				非常重视用户个人信息和隐私权的保护，鉴于此，我们根据相关法律法规规定，制定本隐私权政策，帮助您了解我们将如何收集、使用并保护您的个人信息。本隐私权政策适用于我们提供的产品或服务，请您在使用我们的产品或服务前认真阅读并确认充分理解本隐私权政策，以便您做出您认为适当的选择。您使用我们提供的产品或服务，即意味着您已阅读本隐私权政策所有条款，并对本隐私权政策条款的含义及相应的法律后果已全部通晓并充分理解，并且同意我们按照本隐私权政策收集、使用并保护您的个人信息。如果您不同意本隐私权政策的部分或全部内容、或对本隐私权政策内容有任何疑问，您应立即停止使用我们提供的产品或服务，并通过
				<Somnus:sitecfg name="sitetitle" />客服与我们联系。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;本隐私权政策已对与您的权益具有或可能具有重大关系的条款，及对我们具有或可能具有免责或限制责任的条款用粗体字予以标注，请您注意。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;需要特别说明的是，

				<Somnus:sitecfg name="sitetitle" />
				平台也可能含有到其他网站的链接，我们对该等链接网站的信息保护措施不负责任。我们建议您查看该等网站的隐私权政策，以了解他们如何处理您的信息，以便审慎决策。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;本隐私权政策将帮助您了解以下内容：<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;一.我们如何收集和使用您的个人信息<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;二.我们如何共享、转让、公开披露您的个人信息</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;三.我们如何保存您的个人信息</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;四.我们如何保护您的个人信息</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;五.您可以如何管理个人信息</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;六.未成年人的个人信息保护</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;七.通知和修订</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;八.您如何联系我们</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;九.可分割性</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;十. 其他</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;一.我们如何收集和使用您的个人信息<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;个人信息是指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。我们将在如下流程中，基于实现该流程所需的目的收集您的个人信息：<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.1
				当您注册
				<Somnus:sitecfg name="sitetitle" />
				账号时，我们将收集您的真实姓名、手机号码、身份证件号码、电子邮箱、登录密码、手势密码或面部特征等个人生物识别信息，收集此类信息是为了满足相关法律法规的网络实名制要求并保护您的账号安全。如果您仅需使用基础浏览服务，您可以选择不进行注册及提供上述信息。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.2
				当您使用我们提供的网络借贷服务时，您需进行银行存管账户开立、银行账户绑定、充值、出借、提现、还款、缴费等操作，我们和/或我们委托的存管银行将收集您的真实姓名、身份证件号码、银行账号信息和/或手机号码，并对您的身份信息及银行账户信息进行验证。收集此类信息是为了满足相关法律法规关于网络借贷业务资金存管的要求，如果您不提供此类信息，您可能无法正常使用我们的服务。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.3 当您希望通过我们提供的产品或服务获得借款时，我们将收集您的如下个人信息：<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i
				您的真实姓名、身份证件号码、性别、手机号码、电子邮箱、婚姻状态、年龄、学历、户口所在地、现居住地（如居住地证明）、工作情况（如工作证明）、收入情况（如个人银行流水）、征信报告和其他财产证明（如房产、车产等）；<br>
			</p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii
				您的社保、公积金信息，芝麻信用报告，在此信息收集过程中可能需要您提供有关账户名、密码和短信验证码；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii
				借款项目名称、借款金额、借款期限、借款用途、还款方式、还款来源、还款保障措施；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv
				借款资金运用情况，您的经营状况及财务状况、还款能力变化情况，您的借款逾期情况，以及您的涉诉情况、受行政处罚情况；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v
				您的通讯录、通话详单，您的亲戚朋友、联系人及其手机号码，请您确保您在向我们提供该信息时已经获得您的亲戚朋友、联系人的同意，以便我们、我们的关联方或合作方可以通过您提供的联系方式进行联系。如您向我们提供的亲戚朋友、联系人及其手机号码的行为未经过其本人同意，我们与您的亲戚朋友、联系人进行联系所发生的风险及责任将由您承担。当您违反与
				<Somnus:sitecfg name="sitetitle" />的约定时，为维护
				<Somnus:sitecfg name="sitetitle" />的合法权益，您同意
				<Somnus:sitecfg name="sitetitle" />可向与
				<Somnus:sitecfg name="sitetitle" />合作的律师事务所、催收机构，及
				<Somnus:sitecfg name="sitetitle" />认为可向您传达信息的亲戚朋友、联系人等披露您的违约信息；若
				<Somnus:sitecfg name="sitetitle" />与您的亲戚朋友、联系人等关联方存在纠纷未能协商解决的，
				<Somnus:sitecfg name="sitetitle" />可能会自行或通过催收机构、律师事务所、法院、仲裁委员会和其他有权机关与您联系，请您帮助
				<Somnus:sitecfg name="sitetitle" />或上述机构向您的亲戚朋友、联系人等关联方转达相关诉求，上述转达以您自愿为基础，并不会让您承担额外的法律义务或责任；以及
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vi
				为实现本隐私权政策目的所需的、或者本隐私权政策或其他协议中经您授权许可的其他收集方式下所收集的信息。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;收集此类信息是为了完成借款人身份识别、信用审核和/或逾期账务提醒等，如果您不提供此类信息，您可能无法正常使用我们的服务。</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.4
				我们和/或我们委托的存管银行将收集您的借款记录、充值记录、出借记录、提现记录、还款记录、缴费记录以及您账户中的财产信息（包括总资产、出借金额、账户余额及积分等财产信息），收集此类信息是为了完成资金结算、账务核对以及提供信息报告等，以及满足相关法律法规的信息披露要求。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.5
				为更好地向您提供产品和服务，也为了我们自身的风险防控，以及保护各方的合法权益，您同意并授权我们可以通过您自主填写和提供的个人信息和资料自行或通过其他机构（包括但不限于征信机构、中国互联网金融协会及其他依法成立的行业协会、大数据服务机构及其他第三方机构，下同）依法收集您的额外资料和信息，以在您使用我们提供的产品或服务前期对您的履约情况、信用情况进行审核、在后期对您进行必要的催收、以及实现我们为您提供的各项服务，我们搜集的资料和信息范围包括但不限于：<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i
				您的其他个人信息，包括但不限于工作信息、学历信息以及能够单独或者与其他信息结合识别您个人身份的其他信息；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii
				您在我们的关联方和合作方处留存以及形成的任何数据和信息，包括但不限于您的证券账户和/或银行账户信息等；您在我们、我们的关联方或合作方以及其他机构的网络平台上留存、形成、产生的有关法律文件以及任何个人信息或行为数据，包括但不限于您的交易信息、账户信息、借贷项目信息、履约或违约行为信息等；
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii
				您的信用信息，包括但不限于您的征信记录和信用报告及其他信用信息；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv
				您的财产信息，包括但不限于您的店铺/企业经营状况、财税信息、房产信息、车辆信息、基金、保险、股票、信托、债券等投资理财信息和负债信息等；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v
				您在行政机关、司法机关留存的任何信息，包括但不限于户籍信息、工商信息、诉讼信息、执行信息和违法犯罪信息等；以及</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vi
				与您申请或使用的产品或服务相关的、您留存在其他自然人、法人和组织的其他相关信息。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.6
				您同意我们可以自行或通过合作方（包括但不限于征信机构、中国互联网金融协会及其他依法成立的行业协会、大数据服务机构及其他第三方合作机构）对您提交或我们搜集的用户信息（包括但不限于您的个人身份证信息等）进行核实、验证，并对获得的核实、验证结果进行查看、收集、使用和留存等操作。
			</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.7
				我们将在您使用我们提供的产品或服务的过程中收集您的个人常用设备信息（包括设备名称、设备型号、设备识别码、操作系统和应用程序版本）和日志信息（如访问内容、IP地址、访问日期和时间及访问记录等），收集这些信息是为了向您提供更好的产品和/或服务并改善用户体验。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;1.8 您在此同意并授权，我们可以直接将您的个人信息和资料用于如下用途：<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i
				进行用户身份识别、资料核查和验证，以审核用户的履约情况、信用情况，确保交易的合理性、安全性和防范违法犯罪活动；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii
				为向您提供适合于您的服务，并持续维护、改善、优化该等服务，对您的信息进行内部归类、模型建设和分析等内部使用；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii
				用于预防、发现、调查欺诈、危害安全、非法或违反与我们或我们关联方协议、政策或规则的行为，以保护您、我们的其他用户、我们或我们关联方的合法权益；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv
				我们可能会将来自某项服务的信息与来自其他服务的信息结合起来，以便为您提供更优质、个性化的服务、内容和建议；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v 用于向您收取应支付的各类费用（如有）；
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vi
				有针对性地向您推荐我们提供的商品、服务，以及与我们的关联方、合作方共享，以向您提供或推荐我们的关联方、合作方的商品、服务。您同意我们、我们的关联方及合作方通过站内信、电子邮件、电话、短信等方式向您提供、发送服务状态的通知及其他商业性信息。如不需要我们相关信息，您可联系
				<Somnus:sitecfg name="sitetitle" />客服取消、退订该类信息推广服务；以及
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vii
				为实现本隐私权政策目的所需的、或者本隐私权政策或其他协议中经您授权许可的其他使用方式。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.9 您充分知晓，以下情形中，我们收集、使用个人信息无需征得您的授权或同意：</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i 与国家安全、国防安全有关；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii 与公共安全、公共卫生、重大公共利益有关；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii 与犯罪侦查、起诉、审判和判决执行等有关；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv
				出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v 所收集的个人信息是个人信息主体自行向社会公众公开的；
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vi
				从合法公开披露的信息中收集的您的个人信息，如合法的新闻报道、政府信息公开等渠道；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vii 根据您的要求签订合同所必需的；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;viii
				用于维护所提供的产品与/或服务的安全稳定运行所必需，例如发现、处置产品与/或服务的故障；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ix 为合法的新闻报道所必需；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;x
				学术研究机构基于公共利益开展统计或学术研究所必要，且对外提供学术研究或描述的结果时，对结果中所包含的个人信息进行匿名化处理的；以及</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;xi 法律法规规定的其他情形。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.10
				您知晓并同意，在收集您的个人信息后，我们可能会通过技术手段对您的个人信息进行脱敏。我们有权在不透露您个人信息的前提下使用已经脱敏的信息。</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.11
				为了了解您的偏好、进行咨询或数据分析、改善产品服务及用户体验、及时发现并防范安全风险、为用户和合作方提供更好的服务，您同意我们有权使用诸如“Cookie”等装置以搜集、追踪相关信息和资料并提供只有通过“Cookie”等装置才可得到的功能。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1.12
				我们不会将“Cookie”用于本隐私权政策所述目的之外的任何用途，您可根据自己的偏好留存或删除“Cookie”。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;二.我们如何共享、转让、公开披露您的个人信息<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;2.1
				我们承诺会根据相关法律法规及监管规定对您的个人信息（包括您主动提供的和我们收集、记录的所有数据、信息、资料和相应的交易行为记录、交易文件，下同）承担保密义务。但为了向您提供更多元化、个性化、优质的服务，您同意并授权我们在以下情形下将您的信息向第三方进行共享。并且，我们将促使该等第三方在使用您信息的过程中对您的信息采取适当保密与安全措施：<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i 事先获得您的明确同意或授权；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii
				根据可适用的法律法规、法律程序的要求、强制性的行政或司法要求所必须的情况下进行提供；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii
				提供给依法设立的征信机构和个人信用数据机构，以供有关单位和个人依法查询和使用；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv
				提供给中国互联网金融协会及其他合法成立的行业协会，以供有关单位和个人依法查询和使用，并按照中国互联网金融协会及其他行业协会的要求依法进行信息披露和展示；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v
				在法律法规允许的范围内，为维护我们、我们的关联方或合作方，您或其他用户，或社会公众利益、财产或安全免遭损害而有必要提供；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vi
				提供给我们的关联方、合作方以用于现在或将来向您提供我们、我们的关联方、合作方的相关服务，具体的服务可能包括借贷服务、理财服务等，您在此确认前述服务是您希望获得的（包括但不限于对您的个人资料和信息进行核实、对您的信用情况进行评估以决定向您提供前述服务的可行性、合理性以及决定提供服务的相关条款和条件等）；
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;vii
				符合我们、我们的关联方、合作方与您签署的相关协议（包括在线签署的电子协议以及相应的平台规则）或其他的法律文件约定所提供；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;viii
				如我们或我们的关联方涉及合并、分立、收购、资产或业务转让或类似的交易，您的个人信息可能作为此类交易的一部分而被共享，我们将确保该等信息在共享时的机密性，您同意对此予以认可和理解；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ix 基于学术研究而使用；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;x 基于符合法律法规的社会公共利益而使用；以及</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;xi
				为实现本隐私权政策目的所需的、或者本隐私权政策或其他协议中经您授权许可的其他共享方式。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2.2
				基于服务用户之目的，在我们的关联方、合作方同意承担保密义务的情况下，我们可能会与我们的关联方、合作方共享您的个人资料和信息，以便我们的关联方、合作方向您展示我们提供的商品、服务。</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;2.3
				当您使用或申请使用我们的关联方、合作方提供的服务或与其或拟与其进行交易时，我们可能会将您的个人信息和资料共享与该等关联方、合作方，使其可以对您的个人信息和资料进行核实、分析。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;2.4 您对其他用户信息的使用：<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;在不违反我们与其他用户、合作机构之间的协议的前提下，发生如下任一情形时，您有权要求我们提供与您发生交易的其他用户、合作机构的信息和资料，且我们提供的该等信息和资料仅以在如下情形发生时使用并以维护您的合法权益所必须为限：<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i. 与您交易的其他用户、合作机构发生了与您签署的法律文件项下的违约行为；<br>
			</p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				您已依法对与您交易的其他用户提起了有关您与该其他用户通过公司的服务进行的交易有关的仲裁或诉讼并提供了有效的立案（受理）证明文件；或</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii.
				发生了有碍您在通过我们的产品进行的交易项下相关权益实现的情况，包括但不限于公司被吊销营业执照或被解散、清算、宣告破产以及其他情形。</div>
			<div>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;对于您通过与我们签署法律文件或向我们申请披露而获知的其他用户、合作机构的资料和信息，您同意并确认：除非法律法规另有规定，或者有权的仲裁机构、司法、行政等权力机关的要求，您仅可将该等资料和信息用于实现、维护您在与该其他用户、合作机构签署的法律文件项下的权益以及履行您在该等法律文件项下的相关义务，不得将该等资料和信息用于其他用途，更不得非法出售该等信息和资料。
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2.5 除以下情形外，我们不会将您的个人信息转让给任何公司、组织和个人：</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i. 事先获得您的明确同意或授权；</p>
			<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				根据可适用的法律法规、法律程序的要求、强制性的行政或司法要求所必须的情况进行提供；</p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii.
				符合我们、我们的关联方、合作方与您签署的相关协议（包括在线签署的电子协议以及相应的平台规则）或其他的法律文件约定所提供；或</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv.
				如我们或我们的关联方涉及合并、分立、收购、资产或业务转让或类似的交易，您的个人信息可能作为此类交易的一部分而被转移，我们将确保该等信息在转移时的机密性，您同意对此予以认可和理解。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2.6 我们将会在以下情况下公开披露您的个人信息：</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i. 根据您的需求，在您明确同意的披露方式下披露您所指定的个人信息；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				根据法律法规的要求、强制性的行政执法或司法要求所必须提供您个人信息的情况下，我们将会依据所要求的个人信息类型和披露方式公开披露您的个人信息。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;三.我们如何保存您的个人信息</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;3.1 保存地域<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;您的个人信息将储存于中华人民共和国境内。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;3.2 保存期限<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;根据相关法律法规的规定，网络借贷业务中借贷合同应至少保存至合同到期后五年，信息披露内容应自披露之日起保存至少五年。因此，除法律法规另有规定外，我们将永久保存您的个人信息以及相关网络借贷业务数据。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;您的个人信息经匿名化处理后将形成可以使用及流通的数据，我们对此类数据的保存及处理无需另行通知并征得您的同意。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;3.3 安全事件<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i.
				如不幸发生个人信息安全事件后，我们将按照法律法规的要求，及时向您告知安全事件的基本情况和可能的影响、我们已采取或将要采取的处理措施、您可自主防范和降低的风险的建议、对您的补救措施等。我们会及时将事件相关情况以站内通知、短信通知、电话、邮件等您预留的联系方式告知您，难以逐一告知时我们会采取合理、有效的方式发布公告。<br>
			</p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				我们会及时处置系统漏洞、网络攻击、病毒侵袭及网络侵入等安全风险。在发生危害网络安全的事件时，我们会按照网络安全事件应急预案，及时采取相应的补救措施，并按照规定向有关主管部门报告。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii.
				如果我们的物理、技术或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改或损毁，导致您的合法权益受损，我们将严格依照法律的规定承担相应的责任。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;四.我们如何保护您的个人信息</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;4.1 我们非常重视个人信息安全，将采取一切合理可行的技术安全措施，保护您的个人信息：<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i.
				我们通过https技术为您提供浏览服务，并通过相应的登录短信验证安全机制、数据库加密及日志加密措施保障用户数据安全；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				我们建立了数据分级制度，对您的手机号、身份证号等个人敏感信息全程进行加密处理，并设置严格的数据访问权限，确保您的个人信息安全；以及</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii.
				我们不时聘请安全专家为技术人员提供相应培训，并在技术部内部设置相应的安全专家检查系统的安全性。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4.2
				尽管我们采取了上述技术安全措施，但请您理解，在互联网行业由于技术的限制和飞速发展以及可能存在的各种恶意攻击手段，即便我们竭尽所能加强安全措施，也不可能始终保证信息的百分之百安全。请您了解，您使用我们的产品和/或服务时所用的系统和通讯网络，有可能因超出我们的控制能力之外的其他因素而出现安全问题。如您发现自己的个人信息泄密，尤其是您的账户或密码发生泄漏，您应该立即通过
				<Somnus:sitecfg name="sitetitle" />客服与我们取得联系，以便我们采取相应措施。
			</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;五.您可以如何管理个人信息<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;我们非常重视并保护您关于管理个人信息的权利，并为您提供了管理个人信息的方法。您有权利访问、更正、撤销自己的信息并保护自己的隐私和安全。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.1 访问权<br></p>
			<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;除法律法规规定的例外情况，无论您何时使用我们的服务，我们都会力求让您顺利访问自己的个人信息。如果您希望访问或编辑您账户中的个人资料信息（包括但不限于手机号码、电子邮箱、登录密码、手势密码、银行账户信息等），您可以在网页端或APP端中执行此类操作，或联系
				<Somnus:sitecfg name="sitetitle" />客服进行编辑；<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.2 更正权<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;当您需要更新您的个人信息时，或发现我们处理您的个人信息有错误时，您有权做出更正或更新。您可以自行在相关网页端或APP端进行更正，或通过反馈与报错等将您的更正申请提交给我们。在您进行更新或更正前，我们可能会现要求您验证自己的身份，然后再处理您的请求。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.3 撤销权<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;您可以改变您授权同意的范围或撤回您的授权，但您撤回同意或授权后，我们可能将无法继续为您提供撤回同意或授权所对应的服务。同时，您知悉并理解，在您撤回同意或授权前提供的个人信息，我们将继续依照可适用的法律法规规定及本隐私权政策进行保存或披露。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.4 注销权<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;您可以通过拨打
				<Somnus:sitecfg name="sitetitle" />平台上公布的
				<Somnus:sitecfg name="sitetitle" />
				客服电话申请注销您的账户，一旦您注销账户，我们将停止为您提供服务，因此请您谨慎操作。但您知悉并理解，在您注销账户前提供的个人信息，我们将继续依照可适用的法律法规规定及本隐私权政策进行保存或披露。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.5 管理问题<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如果您无法通过上述方式访问、更正或撤销您的个人信息，或者您无法完成账户注销，或者您就我们收集、使用您信息存在任何疑问，您都可以通过
				<Somnus:sitecfg name="sitetitle" />
				客服与我们联系。为保障安全，我们可能需要您提供书面请求，或以其他方式证明您的身份。我们将在收到您的反馈并验证您身份后尽快答复您的相关请求。<br>
			</p>
			<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;我们将尽合理商业努力，满足您对于个人信息的访问、更正、撤销及账户注销的要求。但对于无端重复、需要过多技术手段、给他人合法权益带来风险或者非常不切实际的请求，我们可能会予以拒绝。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情况收取一定成本费用。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;5.6 例外<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;按照法律法规要求，以下情况中，我们将无法响应您的请求：<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i. 与国家安全、国防安全有关的；<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii. 与公共安全、公共卫生、重大公共利益有关的；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iii. 与犯罪侦查、起诉和审判等有关的；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;iv. 有充分证据表明您存在主观恶意或滥用权利的；</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v.
				响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;六.未成年人的个人信息保护</div>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;6.1 我们非常重视对未成年人个人信息的保护<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如您是18周岁以下的未成年人，在使用我们的服务前，请务必要求您的监护人仔细阅读本隐私权政策，并请您在征得您的监护人同意的前提下使用我们的服务或向我们提供信息。<br>
			</p>
			<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6.2 补救措施<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如果您未能取得您监护人的同意或您未同意您的被监护人使用我们的服务并向我们提供信息，请您或被监护人立即停止使用我们的服务并及时与我们取得联系。在收到您的书面通知而知晓或我们自行知晓，我们在未事先获得监护人同意的情况下收集了未成年人的个人信息时，我们会立即停止相关信息收集及使用。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;七.通知和修订<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;本隐私权政策将会随着我们的业务的发展进行更新，以便为您提供更好的服务。我们会通过在网站、APP或其他平台上发出更新版本并在生效前通过公告或以其他适当方式提醒您相关内容的更新，请您及时访问以便了解最新的隐私权政策。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;八.您如何联系我们<br></p>
			<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;8.1
				<Somnus:sitecfg name="sitetitle" />客服<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如果您对本隐私权政策有任何疑问、意见或建议，或您在使用我们提供的服务时，就您的个人信息的收集、使用、共享、访问、更正、撤销等相关事宜有任何意见或建议，或您在使用我们提供的服务时遇到任何问题，您都可以通过
				<Somnus:sitecfg name="sitetitle" />平台上公布的客服电话（400-858-0580）与我们联系。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;8.2 特别提示<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i.
				我们提醒您，如您反馈的问题涉及您的个人信息，尤其是您的个人敏感信息，请您不要直接在电话中进行透露，我们将会在确保您个人信息安全的情况下与您进一步沟通。<br></p>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.
				部分情况下，为保障您个人信息的安全，我们可能需要您提供书面请求，或以其他方式证明您的身份。我们将在收到您的反馈并验证您的身份后尽快答复您的相关请求。</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;九.可分割性</div>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如果本隐私权政策的任何条款在任何程度上被认定无效或不可执行，本隐私权政策的其余部分不应受其影响且应在法律允许的最大程度内予以执行。本隐私权政策中任何无效或不可执行的条款应被另一有效且可执行的条款取代，并且该条款应具有与该不可执行的条款的原意最接近的效力。<br>
			</p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;十. 其他<br></p>
			<p>&nbsp; &nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;如本隐私权政策与
				<Somnus:sitecfg name="sitetitle" />所运营的任何平台的用户注册/服务协议中规定的个人信息保护条款存在不一致或矛盾之处，请以本隐私权政策的条款为准。<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 附：名词解释<br></p>
			<p>&nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;为了帮助您更好地理解本隐私权政策，将前述条款中的部分名词进行如下解释：<br></p>
			<p>&nbsp; &nbsp; &nbsp; &nbsp;
				<Somnus:sitecfg name="sitetitle" />或我们是指
				<Somnus:sitecfg name="sitetitle" />商务顾问（上海）有限公司及其关联方的单称或合称，
				<Somnus:sitecfg name="sitetitle" />平台是指
				<Somnus:sitecfg name="sitetitle" />
				运营的包括但不限于网页端、APP端的任何平台。关联方是指对于任何主体（包括个人、公司、合伙企业、组织或其他任何实体）而言，即其直接或间接控制的主体，或直接或间接控制其的主体，或直接或间接与其受同一主体控制的主体。控制是指通过持有表决权、合约或其他方式，直接或间接地拥有对相关主体的管理和决策作出指示或责成他人作出指示的权力或事实上构成实际控制的其他关系。<br>
			</p>
			<div>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Cookie是指支持服务器端（或者脚本）在客户端上存储和检索信息的一种机制，通过增加简单、持续的客户端状态来扩展基于Web的客户端/服务器应用。服务器在向客户端返回HTTP对象的同时发送一条状态信息，并由客户端保存。状态信息中说明了该状态下有效的URL范围。此后，客户端发起的该范围内的HTTP请求都将把该状态信息的当前值从客户端返回给服务器，这个状态信息被称为Cookie。
			</div>
			<div>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;个人敏感信息是指一旦泄露、非法提供或滥用可能危害人身和财产安全，极易导致个人名誉、身心健康受到损害或歧视性待遇等的个人信息，本隐私权政策中涉及的个人敏感信息包括：姓名、身份证件号码、手机号码、银行账号、个人生物识别信息等。
			</div>
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;匿名化是指通过对个人信息的技术处理，使得个人信息主体无法被识别，且处理后的信息不能被复原的过程。</div>
			<!--<p>-->
			<!--	<divcourier new\',="" monospace;font-weight:="" normal;font-size:="" 14px;line-height:=""-->
			<!--		19px;white-space:="" pre;"=""></divcourier>-->
			<!--</p>-->
			<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;设备是指可用于使用
				<Somnus:sitecfg name="sitetitle" />产品和/或服务的装置，例如桌面设备、平板电脑或智能手机等。</div>
			<p><br></p>
		</div>
	</div>
</div>';

// 备份了 7 个文件
?>