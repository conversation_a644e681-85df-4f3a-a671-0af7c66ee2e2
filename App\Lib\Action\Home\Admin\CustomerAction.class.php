<?php
class CustomerAction extends CommonAction{

    //客户管理列表
    public function index(){
        $this->title = "客户管理";
        $keyword = I("keyword",'','trim');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where['customer_name|phone|id_card'] = array('like',"%{$keyword}%");
        }

        $Customer = D("customer_loans");
        import('ORG.Util.Page');
        $count = $Customer->where($where)->count();
        $Page  = new Page($count,20);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $list = $Customer->where($where)->order('created_time Desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        
        // 格式化数据
        foreach($list as &$item){
            $item['loan_time_format'] = date('Y-m-d H:i', strtotime($item['loan_time']));
            $item['due_time_format'] = date('Y-m-d H:i', strtotime($item['due_time']));
            $item['created_time_format'] = date('Y-m-d H:i', $item['created_time']);
            
            // 判断状态
            if($item['status'] == 1){
                $item['status_text'] = '正常';
                $item['status_class'] = 'success';
            }elseif($item['status'] == 0){
                $item['status_text'] = '已结清';
                $item['status_class'] = 'info';
            }else{
                $item['status_text'] = '逾期';
                $item['status_class'] = 'danger';
            }
        }
        
        $this->list = $list;
        $this->page = $show;
        $this->count = $count;
        $this->display();
    }

    //添加客户
    public function add(){
        $this->title = "添加客户";
        if(IS_POST){
            $data = array(
                'customer_name' => I('customer_name', '', 'trim'),
                'id_card' => I('id_card', '', 'trim'),
                'bank_card' => I('bank_card', '', 'trim'),
                'phone' => I('phone', '', 'trim'),
                'loan_amount' => I('loan_amount', 0, 'floatval'),
                'loan_periods' => I('loan_periods', 0, 'intval'),
                'loan_time' => I('loan_time', '', 'trim'),
                'due_time' => I('due_time', '', 'trim'),
                'overdue_interest' => I('overdue_interest', 0, 'floatval'),
                'contract_content' => I('contract_content', '', 'trim'),
                'status' => I('status', 1, 'intval'),
                'remarks' => I('remarks', '', 'trim'),
                'created_time' => time(),
                'updated_time' => time(),
                'admin_id' => session('admin_id') ? session('admin_id') : 1
            );

            // 验证必填字段
            if(empty($data['customer_name'])){
                $this->error("请输入客户姓名!");
            }
            if(empty($data['id_card'])){
                $this->error("请输入身份证号!");
            }
            if(empty($data['bank_card'])){
                $this->error("请输入银行卡号!");
            }
            if(empty($data['phone'])){
                $this->error("请输入手机号!");
            }
            if($data['loan_amount'] <= 0){
                $this->error("请输入正确的借款金额!");
            }
            if($data['loan_periods'] <= 0){
                $this->error("请输入正确的借款分期数!");
            }

            $Customer = D("customer_loans");
            $result = $Customer->add($data);
            if($result){
                $this->success("添加成功!", U('Customer/index'));
            }else{
                $this->error("添加失败!");
            }
        }
        $this->display();
    }

    //编辑客户
    public function edit(){
        $this->title = "编辑客户";

        // 调试信息 - 临时添加
        $debug_info = array(
            'GET_id' => isset($_GET['id']) ? $_GET['id'] : 'NULL',
            'POST_id' => isset($_POST['id']) ? $_POST['id'] : 'NULL',
            'REQUEST_id' => isset($_REQUEST['id']) ? $_REQUEST['id'] : 'NULL',
            'I_function_result' => I('id', 0, 'intval')
        );

        $id = I('id', 0, 'intval');
        if(!$id){
            // 显示调试信息
            $debug_msg = "参数错误! 调试信息: " . json_encode($debug_info);
            $this->error($debug_msg);
        }

        $Customer = D("customer_loans");
        $info = $Customer->where(array('id' => $id))->find();
        if(!$info){
            $this->error("客户不存在!");
        }

        if(IS_POST){
            // POST请求时，ID可能来自隐藏字段
            if (!$id) {
                $id = I('id', 0, 'intval');
                if (!$id) {
                    $this->error("保存失败：缺少客户ID参数!");
                }
            }

            // 获取并处理表单数据
            $loan_time = I('loan_time', '', 'trim');
            $due_time = I('due_time', '', 'trim');

            // 时间格式转换 - 修复空值问题
            if (!empty($loan_time)) {
                $loan_time = date('Y-m-d H:i:s', strtotime($loan_time));
            } else {
                $loan_time = $info['loan_time']; // 保持原值
            }
            if (!empty($due_time)) {
                $due_time = date('Y-m-d H:i:s', strtotime($due_time));
            } else {
                $due_time = $info['due_time']; // 保持原值
            }

            $data = array(
                'customer_name' => I('customer_name', '', 'trim'),
                'id_card' => I('id_card', '', 'trim'),
                'bank_card' => I('bank_card', '', 'trim'),
                'phone' => I('phone', '', 'trim'),
                'loan_amount' => I('loan_amount', 0, 'floatval'),
                'loan_periods' => I('loan_periods', 0, 'intval'),
                'loan_time' => $loan_time,
                'due_time' => $due_time,
                'overdue_interest' => I('overdue_interest', 0, 'floatval'),
                'contract_content' => I('contract_content', '', 'trim'),
                'status' => I('status', 1, 'intval'),
                'remarks' => I('remarks', '', 'trim'),
                'updated_time' => time()
            );

            // 处理合同文件上传
            if (!empty($_FILES['contract_file']['name'])) {
                $upload_result = $this->uploadContract();
                if ($upload_result['status']) {
                    $data['contract_file'] = $upload_result['file_path'];
                } else {
                    $this->error($upload_result['msg']);
                }
            }

            // 验证必填字段
            if (empty($data['customer_name'])) {
                $this->error("请输入客户姓名!");
            }
            if (empty($data['phone'])) {
                $this->error("请输入手机号!");
            }
            if ($data['loan_amount'] <= 0) {
                $this->error("请输入正确的借款金额!");
            }

            $result = $Customer->where(array('id' => $id))->save($data);
            if($result !== false){
                $this->success("修改成功!", U('Customer/index'));
            }else{
                $this->error("修改失败，请检查数据格式!");
            }
        }

        // 格式化时间用于表单显示 - 修复时间格式问题
        if (!empty($info['loan_time'])) {
            $info['loan_time'] = date('Y-m-d\TH:i', strtotime($info['loan_time']));
        }
        if (!empty($info['due_time'])) {
            $info['due_time'] = date('Y-m-d\TH:i', strtotime($info['due_time']));
        }
        
        $this->info = $info;
        $this->display();
    }

    //删除客户
    public function delete(){
        $id = I('id', 0, 'intval');
        if(!$id){
            $this->error("参数错误!");
        }

        $Customer = D("customer_loans");
        $result = $Customer->where(array('id' => $id))->delete();
        if($result){
            $this->success("删除成功!", U('Customer/index'));
        }else{
            $this->error("删除失败!");
        }
    }

    //查看详情
    public function view(){
        $this->title = "客户详情";
        $id = I('id', 0, 'intval');
        if(!$id){
            $this->error("参数错误!");
        }

        $Customer = D("customer_loans");
        $info = $Customer->where(array('id' => $id))->find();
        if(!$info){
            $this->error("客户不存在!");
        }

        // 格式化数据
        $info['loan_time_format'] = date('Y-m-d H:i:s', strtotime($info['loan_time']));
        $info['due_time_format'] = date('Y-m-d H:i:s', strtotime($info['due_time']));
        $info['created_time_format'] = date('Y-m-d H:i:s', $info['created_time']);
        $info['updated_time_format'] = date('Y-m-d H:i:s', $info['updated_time']);

        $this->info = $info;
        $this->display();
    }

    //更改状态
    public function changeStatus(){
        $id = I('id', 0, 'intval');
        $status = I('status', 1, 'intval');
        
        if(!$id){
            $this->error("参数错误!");
        }

        $Customer = D("customer_loans");
        $result = $Customer->where(array('id' => $id))->save(array(
            'status' => $status,
            'updated_time' => time()
        ));
        
        if($result !== false){
            $this->success("状态修改成功!", U('Customer/index'));
        }else{
            $this->error("状态修改失败!");
        }
    }

    // 合同文件上传
    private function uploadContract() {
        if (empty($_FILES['contract_file']['name'])) {
            return array('status' => false, 'msg' => '请选择要上传的文件');
        }

        $file = $_FILES['contract_file'];

        // 检查文件大小 (最大10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            return array('status' => false, 'msg' => '文件大小不能超过10MB');
        }

        // 检查文件类型
        $allowed_types = array('pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png');
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($file_ext, $allowed_types)) {
            return array('status' => false, 'msg' => '只支持PDF、Word文档和图片格式');
        }

        // 创建上传目录
        $upload_dir = './Public/uploads/contracts/' . date('Y/m/');
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // 生成文件名
        $file_name = date('YmdHis') . '_' . rand(1000, 9999) . '.' . $file_ext;
        $file_path = $upload_dir . $file_name;

        // 移动文件
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // 返回相对路径
            $relative_path = str_replace('./Public/', '/Public/', $file_path);
            return array('status' => true, 'file_path' => $relative_path);
        } else {
            return array('status' => false, 'msg' => '文件上传失败');
        }
    }

    // 下载合同文件
    public function downloadContract() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error("参数错误!");
        }

        $Customer = D("customer_loans");
        $info = $Customer->where(array('id' => $id))->find();

        if (!$info) {
            $this->error("客户不存在!");
        }

        if (empty($info['contract_file'])) {
            $this->error("该客户没有上传合同文件!");
        }

        $file_path = './Public' . $info['contract_file'];
        if (!file_exists($file_path)) {
            $this->error("合同文件不存在!");
        }

        // 设置下载头
        $file_name = basename($file_path);
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $file_name . '"');
        header('Content-Length: ' . filesize($file_path));

        // 输出文件内容
        readfile($file_path);
        exit;
    }
}
