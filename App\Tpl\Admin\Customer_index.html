<include file="Public:header" />

<div class="main-content">
    <div class="breadcrumbs" id="breadcrumbs">
        <ul class="breadcrumb">
            <li>
                <i class="icon-home home-icon"></i>
                <a href="{:U('Main/index')}">首页</a>
            </li>
            <li class="active">客户管理</li>
        </ul>
    </div>

    <div class="page-content">
        <div class="row">
            <div class="col-xs-12">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="dataTables_length">
                            <label>
                                <a href="{:U('Customer/add')}" class="btn btn-sm btn-success">
                                    <i class="icon-plus"></i> 添加客户
                                </a>
                            </label>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="dataTables_filter">
                            <form method="get">
                                <label>搜索:
                                    <input type="search" name="keyword" value="{$keyword}" placeholder="客户姓名/手机号/身份证号" class="form-control input-sm">
                                    <button type="submit" class="btn btn-sm btn-primary">搜索</button>
                                </label>
                            </form>
                        </div>
                    </div>
                </div>

                <table class="table table-striped table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>客户姓名</th>
                            <th>身份证号</th>
                            <th>银行卡号</th>
                            <th>手机号</th>
                            <th>借款金额</th>
                            <th>借款分期</th>
                            <th>借款时间</th>
                            <th>到期时间</th>
                            <th>逾期利息</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <volist name="list" id="vo">
                        <tr>
                            <td>{$vo.id}</td>
                            <td>{$vo.customer_name}</td>
                            <td>{$vo.id_card}</td>
                            <td>{$vo.bank_card}</td>
                            <td>{$vo.phone}</td>
                            <td>¥{$vo.loan_amount}</td>
                            <td>{$vo.loan_periods}期</td>
                            <td>{$vo.loan_time_format}</td>
                            <td>{$vo.due_time_format}</td>
                            <td>{$vo.overdue_interest}%</td>
                            <td>
                                <span class="label label-{$vo.status_class}">{$vo.status_text}</span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{:U('Customer/view', array('id'=>$vo['id']))}" class="btn btn-xs btn-info" title="查看详情">
                                        <i class="icon-eye-open"></i> 详情
                                    </a>
                                    <a href="{:U('Customer/edit', array('id'=>$vo['id']))}" class="btn btn-xs btn-warning" title="编辑">
                                        <i class="icon-edit"></i> 编辑
                                    </a>
                                    <if condition="$vo['status'] eq 1">
                                        <a href="{:U('Customer/changeStatus', array('id'=>$vo['id'], 'status'=>0))}" class="btn btn-xs btn-success" title="标记为已结清" onclick="return confirm('确定要标记为已结清吗？')">
                                            <i class="icon-ok"></i> 结清
                                        </a>
                                        <a href="{:U('Customer/changeStatus', array('id'=>$vo['id'], 'status'=>-1))}" class="btn btn-xs btn-danger" title="标记为逾期" onclick="return confirm('确定要标记为逾期吗？')">
                                            <i class="icon-warning-sign"></i> 逾期
                                        </a>
                                    <elseif condition="$vo['status'] eq 0"/>
                                        <a href="{:U('Customer/changeStatus', array('id'=>$vo['id'], 'status'=>1))}" class="btn btn-xs btn-primary" title="恢复正常" onclick="return confirm('确定要恢复为正常状态吗？')">
                                            <i class="icon-repeat"></i> 恢复
                                        </a>
                                    <else/>
                                        <a href="{:U('Customer/changeStatus', array('id'=>$vo['id'], 'status'=>1))}" class="btn btn-xs btn-primary" title="恢复正常" onclick="return confirm('确定要恢复为正常状态吗？')">
                                            <i class="icon-repeat"></i> 恢复
                                        </a>
                                    </if>
                                    <a href="{:U('Customer/delete', array('id'=>$vo['id']))}" class="btn btn-xs btn-danger" title="删除" onclick="return confirm('确定要删除这个客户吗？')">
                                        <i class="icon-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        </volist>
                    </tbody>
                </table>

                <div class="row">
                    <div class="col-sm-6">
                        <div class="dataTables_info">
                            共 {$count} 条记录
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="dataTables_paginate paging_bootstrap">
                            {$page}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<include file="Public:footer" />
