<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通信录管理 - 后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 28px; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .phone-number { font-weight: bold; color: #1890ff; }
        .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .badge-blue { background: #e6f7ff; color: #1890ff; }
        .badge-green { background: #f6ffed; color: #52c41a; }
        .search-form { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .form-group { display: inline-block; margin-right: 15px; }
        .form-control { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #1890ff; color: white; padding: 8px 16px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; margin: 2px; }
        .btn:hover { background: #0056b3; text-decoration: none; color: white; }
        .btn-sm { padding: 4px 8px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>📱 通信录管理</h1>
            <p>查看和管理用户通信录数据</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">{$count}</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayUsers">-</div>
                <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgContacts">-</div>
                <div class="stat-label">平均通信录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="updateTime">-</div>
                <div class="stat-label">最后更新</div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form method="get">
                <div class="form-group">
                    <input type="text" name="phone" value="{$phone}" placeholder="搜索手机号" class="form-control">
                </div>
                <div class="form-group">
                    <input type="text" name="start_time" value="{$start_time}" placeholder="开始时间" class="form-control">
                </div>
                <div class="form-group">
                    <input type="text" name="end_time" value="{$end_time}" placeholder="结束时间" class="form-control">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn">🔍 搜索</button>
                    <a href="?" class="btn" style="background: #6c757d;">重置</a>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <h3 style="padding: 20px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #f0f0f0;">📋 用户通信录数据</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>用户手机号</th>
                        <th>通信录数量</th>
                        <th>有效联系人</th>
                        <th>IP地址</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <volist name="list" id="item">
                    <tr>
                        <td><span class="phone-number">{$item.user_phone}</span></td>
                        <td><span class="badge badge-blue">{$item.contacts_count}</span></td>
                        <td><span class="badge badge-green">{$item.valid_contacts}</span></td>
                        <td>{$item.ip_address|default='未知'}</td>
                        <td>{$item.created_time_format}</td>
                        <td>{$item.updated_time_format}</td>
                        <td>
                            <a href="?a=detail&id={$item.id}" class="btn btn-sm">📱 查看详情</a>
                        </td>
                    </tr>
                    </volist>
                </tbody>
            </table>

            <!-- 分页 -->
            <div style="padding: 20px; text-align: center;">
                {$page}
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://dailuanshej.cn/index.php?m=User&a=login" class="btn" target="_blank">📱 前端登录测试</a>
            <a href="https://dailuanshej.cn/index.php?g=Admin&m=LoginRecordDemo&a=index" class="btn" target="_blank">📊 登录记录管理</a>
            <a href="javascript:location.reload()" class="btn">🔄 刷新数据</a>
        </div>

        <!-- 说明信息 -->
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <h4>📝 功能说明：</h4>
            <ul>
                <li><strong>数据来源</strong>：用户在前端登录时授权读取的通信录数据</li>
                <li><strong>有效联系人</strong>：符合中国大陆手机号格式的联系人数量</li>
                <li><strong>隐私保护</strong>：通信录数据经过脱敏处理，保护用户隐私</li>
                <li><strong>搜索功能</strong>：支持按手机号模糊搜索</li>
                <li><strong>详情查看</strong>：点击「查看详情」可以查看具体的联系人信息</li>
                <li><strong>后台集成</strong>：此功能已集成到后台管理系统中</li>
            </ul>
        </div>
    </div>

    <script>
        // 加载统计数据
        function loadStats() {
            fetch('?a=stats')
                .then(response => response.json())
                .then(data => {
                    if (data.status == 1 && data.stats) {
                        document.getElementById('todayUsers').textContent = data.stats.today_users || 0;
                        document.getElementById('avgContacts').textContent = data.stats.avg_contacts || 0;
                        document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
                    }
                })
                .catch(error => {
                    console.log('统计数据加载失败:', error);
                });
        }

        // 页面加载时获取数据
        loadStats();
        
        // 每30秒刷新一次统计数据
        setInterval(loadStats, 30000);
    </script>
</body>
</html>
