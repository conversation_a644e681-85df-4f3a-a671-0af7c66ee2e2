<?php
/**
 * 消息管理控制器
 * 解决客户隐私泄露和消息推送问题
 */
class MessageAction extends CommonAction {
    
    // 消息列表
    public function index() {
        $this->title = "消息管理";
        
        $keyword = I("keyword", '', 'trim');
        $message_type = I("message_type", 0, 'intval');
        $is_read = I("is_read", -1, 'intval');
        
        $this->keyword = $keyword;
        $this->message_type = $message_type;
        $this->is_read = $is_read;
        
        $where = array();
        if ($keyword) {
            $where['user_phone|title|content'] = array('like', "%{$keyword}%");
        }
        if ($message_type > 0) {
            $where['message_type'] = $message_type;
        }
        if ($is_read >= 0) {
            $where['is_read'] = $is_read;
        }
        
        $Message = D("user_messages");
        import('ORG.Util.Page');
        $count = $Message->where($where)->count();
        $Page = new Page($count, 20);
        $Page->setConfig('theme', '共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show = $Page->show();
        
        // 关联查询客户信息
        $list = $Message->field('m.*, c.customer_name')
                       ->alias('m')
                       ->join('LEFT JOIN customer_loans c ON m.user_phone = c.phone')
                       ->where($where)
                       ->order('m.created_time DESC')
                       ->limit($Page->firstRow . ',' . $Page->listRows)
                       ->select();
        
        // 格式化数据
        foreach ($list as &$item) {
            $item['created_time_format'] = date('Y-m-d H:i:s', $item['created_time']);
            $item['read_time_format'] = $item['read_time'] ? date('Y-m-d H:i:s', $item['read_time']) : '未读';
            
            // 消息类型文本
            $types = array(1 => '系统通知', 2 => '借款提醒', 3 => '还款提醒', 4 => '逾期提醒');
            $item['message_type_text'] = isset($types[$item['message_type']]) ? $types[$item['message_type']] : '未知';
        }
        
        $this->list = $list;
        $this->page = $show;
        $this->count = $count;
        $this->display();
    }
    
    // 发送消息
    public function send() {
        $this->title = "发送消息";
        
        if (IS_POST) {
            $user_phone = I('user_phone', '', 'trim');
            $template_id = I('template_id', 0, 'intval');
            $custom_title = I('custom_title', '', 'trim');
            $custom_content = I('custom_content', '', 'trim');
            $message_type = I('message_type', 1, 'intval');
            
            // 验证参数
            if (empty($user_phone)) {
                $this->error("请选择客户!");
            }
            
            $title = '';
            $content = '';
            
            if ($template_id > 0) {
                // 使用模板
                $Template = D("message_templates");
                $template = $Template->where(array('id' => $template_id, 'status' => 1))->find();
                if (!$template) {
                    $this->error("模板不存在或已禁用!");
                }
                
                // 获取客户信息用于变量替换
                $Customer = D("customer_loans");
                $customer = $Customer->where(array('phone' => $user_phone))->find();
                if (!$customer) {
                    $this->error("客户不存在!");
                }
                
                $title = $template['template_title'];
                $content = $template['template_content'];
                $message_type = $template['message_type'];
                
                // 银行卡号脱敏处理
                $bank_card_masked = $this->maskBankCard($customer['bank_card']);
                
                // 替换变量
                $variables = array(
                    '{customer_name}' => $customer['customer_name'],
                    '{loan_amount}' => $customer['loan_amount'],
                    '{loan_periods}' => $customer['loan_periods'],
                    '{due_time}' => date('Y-m-d', strtotime($customer['due_time'])),
                    '{overdue_interest}' => $customer['overdue_interest'],
                    '{bank_card_masked}' => $bank_card_masked
                );
                
                foreach ($variables as $var => $value) {
                    $title = str_replace($var, $value, $title);
                    $content = str_replace($var, $value, $content);
                }
                
            } else {
                // 自定义消息
                if (empty($custom_title) || empty($custom_content)) {
                    $this->error("请填写消息标题和内容!");
                }
                $title = $custom_title;
                $content = $custom_content;
            }
            
            // 插入消息
            $Message = D("user_messages");
            $data = array(
                'user_phone' => $user_phone,
                'title' => $title,
                'content' => $content,
                'message_type' => $message_type,
                'is_read' => 0,
                'admin_id' => session('admin_id') ? session('admin_id') : 1,
                'created_time' => time(),
                'read_time' => 0
            );
            
            $result = $Message->add($data);
            if ($result) {
                $this->success("消息发送成功!", U('Message/index'));
            } else {
                $this->error("消息发送失败!");
            }
        }
        
        // 获取客户列表
        $Customer = D("customer_loans");
        $customers = $Customer->field('DISTINCT phone, customer_name')->order('created_time DESC')->select();
        $this->customers = $customers;
        
        // 获取消息模板
        $Template = D("message_templates");
        $templates = $Template->where(array('status' => 1))->order('message_type ASC, id ASC')->select();
        $this->templates = $templates;
        
        $this->display();
    }
    
    // 消息详情
    public function view() {
        $this->title = "消息详情";
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error("参数错误!");
        }
        
        $Message = D("user_messages");
        $info = $Message->field('m.*, c.customer_name, c.id_card, c.bank_card')
                       ->alias('m')
                       ->join('LEFT JOIN customer_loans c ON m.user_phone = c.phone')
                       ->where(array('m.id' => $id))
                       ->find();
        
        if (!$info) {
            $this->error("消息不存在!");
        }
        
        // 格式化数据
        $info['created_time_format'] = date('Y-m-d H:i:s', $info['created_time']);
        $info['read_time_format'] = $info['read_time'] ? date('Y-m-d H:i:s', $info['read_time']) : '未读';
        
        // 银行卡号脱敏
        if ($info['bank_card']) {
            $info['bank_card_masked'] = $this->maskBankCard($info['bank_card']);
        }
        
        $this->info = $info;
        $this->display();
    }
    
    // 删除消息
    public function delete() {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error("参数错误!");
        }
        
        $Message = D("user_messages");
        $result = $Message->where(array('id' => $id))->delete();
        if ($result) {
            $this->success("删除成功!", U('Message/index'));
        } else {
            $this->error("删除失败!");
        }
    }
    
    // 批量删除
    public function batchDelete() {
        $ids = I('ids', '', 'trim');
        if (empty($ids)) {
            $this->error("请选择要删除的消息!");
        }
        
        $ids_array = explode(',', $ids);
        $ids_array = array_map('intval', $ids_array);
        $ids_array = array_filter($ids_array);
        
        if (empty($ids_array)) {
            $this->error("参数错误!");
        }
        
        $Message = D("user_messages");
        $where = array('id' => array('in', $ids_array));
        $result = $Message->where($where)->delete();
        
        if ($result) {
            $this->success("批量删除成功!", U('Message/index'));
        } else {
            $this->error("批量删除失败!");
        }
    }
    
    // 银行卡号脱敏处理
    private function maskBankCard($bank_card) {
        if (strlen($bank_card) <= 8) {
            return $bank_card;
        }
        return substr($bank_card, 0, 4) . str_repeat('*', strlen($bank_card) - 8) . substr($bank_card, -4);
    }
    
    // 获取消息统计
    public function statistics() {
        $this->title = "消息统计";
        
        $Message = D("user_messages");
        
        // 总消息数
        $total_count = $Message->count();
        
        // 未读消息数
        $unread_count = $Message->where(array('is_read' => 0))->count();
        
        // 各类型消息统计
        $type_stats = $Message->field('message_type, COUNT(*) as count')
                             ->group('message_type')
                             ->select();
        
        // 最近7天消息发送统计
        $week_stats = array();
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $start_time = strtotime($date);
            $end_time = $start_time + 86400 - 1;
            
            $count = $Message->where(array(
                'created_time' => array('between', array($start_time, $end_time))
            ))->count();
            
            $week_stats[] = array(
                'date' => $date,
                'count' => $count
            );
        }
        
        $this->total_count = $total_count;
        $this->unread_count = $unread_count;
        $this->type_stats = $type_stats;
        $this->week_stats = $week_stats;
        $this->display();
    }
}
?>
