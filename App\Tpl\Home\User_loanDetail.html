<!DOCTYPE html>
<html lang="en" class="no-js">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>借款详情 - 站长源码库（zzmaku.com） </title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<style>
		.detail-container {
			background: #fff;
			margin: 10px;
			border-radius: 8px;
			overflow: hidden;
			box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		}
		.detail-header {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			color: white;
			padding: 20px;
			text-align: center;
		}
		.loan-amount-big {
			font-size: 32px;
			font-weight: bold;
			margin: 10px 0;
		}
		.loan-status-big {
			display: inline-block;
			padding: 6px 12px;
			border-radius: 20px;
			background: rgba(255,255,255,0.2);
			font-size: 14px;
		}
		.detail-body {
			padding: 20px;
		}
		.detail-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 0;
			border-bottom: 1px solid #f0f0f0;
		}
		.detail-item:last-child {
			border-bottom: none;
		}
		.detail-label {
			color: #666;
			font-size: 14px;
		}
		.detail-value {
			color: #333;
			font-weight: 500;
			text-align: right;
			flex: 1;
			margin-left: 20px;
		}
		.contract-section {
			margin-top: 20px;
			padding: 15px;
			background: #f8f9fa;
			border-radius: 6px;
		}
		.contract-title {
			font-weight: bold;
			margin-bottom: 10px;
			color: #333;
		}
		.contract-content {
			line-height: 1.6;
			color: #666;
			font-size: 14px;
		}
		.remaining-highlight {
			color: #ff6b35;
			font-weight: bold;
			font-size: 16px;
		}
		.overdue-highlight {
			color: #dc3545;
			font-weight: bold;
			font-size: 16px;
		}
	</style>
</head>

<body>
	<div class="comm_top_nav" style="color: rgb(39, 37, 40);">
		<a href="javascript:history.back();" style="color: rgb(39, 37, 40);">
			<i class="am-icon-angle-left am-icon-fw"></i>
		</a>
		<b>借款详情</b>
	</div>

	<div style="margin-top: 50px;">
		<div class="detail-container">
			<div class="detail-header">
				<div>借款金额</div>
				<div class="loan-amount-big">¥{$loanInfo.loan_amount}</div>
				<div class="loan-status-big">{$loanInfo.status_text}</div>
			</div>

			<div class="detail-body">
				<div class="detail-item">
					<div class="detail-label">客户姓名</div>
					<div class="detail-value">{$loanInfo.customer_name}</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">身份证号</div>
					<div class="detail-value">{$loanInfo.id_card}</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">银行卡号</div>
					<div class="detail-value">{$loanInfo.bank_card}</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">手机号</div>
					<div class="detail-value">{$loanInfo.phone}</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">借款分期</div>
					<div class="detail-value">{$loanInfo.loan_periods}期</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">借款时间</div>
					<div class="detail-value">{$loanInfo.loan_time_format}</div>
				</div>

				<div class="detail-item">
					<div class="detail-label">到期时间</div>
					<div class="detail-value">{$loanInfo.due_time_format}</div>
				</div>

				<if condition="$loanInfo['status'] eq 1">
					<div class="detail-item">
						<div class="detail-label">剩余时间</div>
						<div class="detail-value">
							<if condition="$loanInfo['remaining_days'] gt 0">
								<span class="remaining-highlight">{$loanInfo.remaining_days}天</span>
							<elseif condition="$loanInfo['remaining_days'] eq 0"/>
								<span class="overdue-highlight">今日到期</span>
							<else/>
								<span class="overdue-highlight">已逾期{$loanInfo.remaining_days|abs}天</span>
							</if>
						</div>
					</div>
				</if>

				<if condition="$loanInfo['overdue_interest'] gt 0">
					<div class="detail-item">
						<div class="detail-label">逾期利息率</div>
						<div class="detail-value">{$loanInfo.overdue_interest}%</div>
					</div>
				</if>

				<div class="detail-item">
					<div class="detail-label">创建时间</div>
					<div class="detail-value">{$loanInfo.created_time_format}</div>
				</div>

				<if condition="$loanInfo['remarks']">
					<div class="detail-item">
						<div class="detail-label">备注信息</div>
						<div class="detail-value">{$loanInfo.remarks}</div>
					</div>
				</if>

				<if condition="$loanInfo['contract_content']">
					<div class="contract-section">
						<div class="contract-title">借款合同</div>
						<div class="contract-content">
							{$loanInfo.contract_content|nl2br}
						</div>
					</div>
				</if>
			</div>
		</div>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
	</script>

	<!--[if lt IE 9]>
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/modernizr.js"></script>
	<script src="__PUBLIC__/home/<USER>/js/amazeui.ie8polyfill.min.js"></script>
	<![endif]-->

	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>

	<div style="display: none;">
		<Somnus:sitecfg name="sitecode" />
	</div>

</body>

</html>
