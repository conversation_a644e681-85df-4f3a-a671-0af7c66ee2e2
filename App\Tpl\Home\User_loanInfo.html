<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>我的借款信息 - 优易花</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .loan-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .loan-amount {
            font-size: 24px;
            font-weight: bold;
            color: #007cba;
            margin-bottom: 15px;
        }
        
        .loan-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .info-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
        }
        
        .info-value {
            color: #333;
            margin-top: 5px;
        }

        /* 状态样式 */
        .status-success {
            color: #28a745 !important;
            font-weight: 700;
        }

        .status-info {
            color: #17a2b8 !important;
            font-weight: 700;
        }

        .status-danger {
            color: #dc3545 !important;
            font-weight: 700;
        }
        
        .contract-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            text-align: center;
        }
        
        .contract-btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .contract-btn:hover {
            background: #0056b3;
        }
        
        .nav-links {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
            padding: 0 10px;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 12px 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            text-align: center;
            font-size: 14px;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .nav-links a:hover, .nav-links a:active {
            background: rgba(255,255,255,0.3);
            transform: scale(0.98);
        }
        
        /* 合同弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            width: 90%;
            max-width: 600px;
            border-radius: 10px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: red;
        }
        
        .contract-content {
            line-height: 1.8;
            color: #333;
        }
        
        .contract-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #007cba;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 0;
            }

            .header {
                padding: 15px;
                margin-bottom: 15px;
            }

            .nav-links {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
                margin-bottom: 15px;
                padding: 0 5px;
            }

            .nav-links a {
                padding: 10px 6px;
                font-size: 13px;
                border-radius: 8px;
                min-height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .loan-card {
                padding: 15px;
                margin-bottom: 15px;
            }

            .loan-amount {
                font-size: 20px;
            }

            .loan-info {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .info-item {
                padding: 8px;
                font-size: 14px;
            }

            .contract-btn {
                padding: 10px 15px;
                font-size: 14px;
            }

            /* 移动端底部导航 */
            .bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 10px 0 20px;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
                z-index: 1000;
            }

            .nav-items {
                display: flex;
                justify-content: space-around;
                align-items: center;
            }

            .nav-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-decoration: none;
                color: #666;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .nav-item.active {
                color: #667eea;
            }

            .nav-item:hover, .nav-item:active {
                color: #667eea;
                text-decoration: none;
            }

            .nav-icon {
                font-size: 20px;
                margin-bottom: 4px;
            }

            /* 隐藏顶部导航，显示底部导航 */
            .nav-links {
                display: none;
            }

            /* 底部安全区域 */
            .bottom-safe-area {
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-links">
            <a href="{:U('Index/index')}">🏠 首页</a>
            <a href="{:U('User/loanInfo')}">💳 借款信息</a>
            <a href="{:U('User/profile')}">👤 个人中心</a>
            <a href="{:U('User/logout')}">🚪 退出</a>
        </div>
        
        <!-- 页面标题 -->
        <div class="header">
            <h1>💰 我的借款信息</h1>
            <p>查看您的借款记录和合同信息</p>
        </div>
        
        <!-- 借款列表 -->
        <if condition="$loanList">
            <volist name="loanList" id="vo">
                <div class="loan-card">
                    <div class="loan-amount">¥{$vo.loan_amount}</div>
                    
                    <div class="loan-info">
                        <div class="info-item">
                            <div class="info-label">📅 借款期数</div>
                            <div class="info-value">{$vo.loan_periods}期</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">⏰ 借款时间</div>
                            <div class="info-value">{$vo.loan_time_format}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">📆 到期时间</div>
                            <div class="info-value">{$vo.due_time_format}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">🆔 身份证号</div>
                            <div class="info-value">{$vo.id_card_masked}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">💳 银行卡号</div>
                            <div class="info-value">{$vo.bank_card_masked}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">📊 借款状态</div>
                            <div class="info-value status-{$vo.status_class}">
                                <if condition="$vo.status eq 1">
                                    ✅ {$vo.status_text}
                                <elseif condition="$vo.status eq 0"/>
                                    🎉 {$vo.status_text}
                                <else/>
                                    ⚠️ {$vo.status_text}
                                </if>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 强制显示合同部分 -->
                    <div class="contract-section">
                        <h4>📄 借款合同</h4>
                        <p>点击下方按钮查看您的借款合同详情</p>
                        <button class="contract-btn" onclick="showContract('{$vo.id}', '{$vo.customer_name}', '{$vo.loan_amount}', '{$vo.loan_periods}', `{$vo.contract_content}`)">
                            📎 查看借款合同
                        </button>
                        <if condition="$vo.contract_file">
                            <br><br>
                            <a href="{$vo.contract_file}" target="_blank" style="color: #28a745; text-decoration: none; background: #f0fff0; padding: 5px 10px; border-radius: 5px; font-size: 12px; margin-left: 10px;">
                                📎 下载合同文件
                            </a>
                        </if>
                    </div>
                </div>
            </volist>
        <else/>
            <div class="loan-card" style="text-align: center; padding: 40px;">
                <h3>📄 暂无借款记录</h3>
                <p>您还没有任何借款记录</p>
            </div>
        </if>

        <!-- 底部安全区域 -->
        <div class="bottom-safe-area"></div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="{:U('Index/index')}" class="nav-item">
                <span class="nav-icon">🏠</span>
                <span>首页</span>
            </a>
            <a href="{:U('User/loanInfo')}" class="nav-item active">
                <span class="nav-icon">💳</span>
                <span>借款信息</span>
            </a>
            <a href="{:U('User/profile')}" class="nav-item">
                <span class="nav-icon">👤</span>
                <span>个人中心</span>
            </a>
            <a href="{:U('User/logout')}" class="nav-item">
                <span class="nav-icon">🚪</span>
                <span>退出</span>
            </a>
        </div>
    </div>

    <!-- 合同弹窗 -->
    <div id="contractModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeContract()">&times;</span>
            <div id="contractContent"></div>
        </div>
    </div>
    
    <script>
        // 显示合同 - 使用真实的合同内容
        function showContract(loanId, customerName, loanAmount, loanPeriods, contractContent) {
            // 如果有真实的合同内容，使用真实内容，否则使用默认内容
            let content;
            if (contractContent && contractContent.trim() !== '') {
                content = `
                    <div class="contract-title">📄 借款合同</div>
                    <div class="contract-content" style="white-space: pre-wrap;">${contractContent}</div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeContract()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                `;
            } else {
                content = `
                    <div class="contract-title">📄 借款合同</div>

                    <div class="contract-content">
                        <p><strong>合同编号：</strong>${loanId}</p>
                        <p><strong>甲方（出借人）：</strong>优易花</p>
                        <p><strong>乙方（借款人）：</strong>${customerName}</p>
                        <br>

                        <p>根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、协商一致的基础上，就借款事宜达成如下协议：</p>
                        <br>

                        <p><strong>第一条 借款金额</strong></p>
                        <p>乙方向甲方借款人民币 <strong>${loanAmount}</strong> 元整。</p>
                        <br>

                        <p><strong>第二条 借款期限</strong></p>
                        <p>借款期限为 <strong>${loanPeriods}</strong> 期，每期30天。</p>
                        <br>

                        <p><strong>第三条 还款方式</strong></p>
                        <p>乙方应按期足额还款，逾期将产生相应的逾期费用。</p>
                        <br>

                        <p><strong>第四条 违约责任</strong></p>
                        <p>如乙方逾期还款，应承担相应的违约责任。</p>
                        <br>

                        <p><strong>第五条 其他条款</strong></p>
                        <p>1. 本合同自双方签字之日起生效。</p>
                        <p>2. 如有争议，双方协商解决。</p>
                        <p>3. 本合同受中华人民共和国法律保护。</p>
                        <br>

                        <div style="display: flex; justify-content: space-between; margin-top: 30px;">
                            <div>
                                <p><strong>甲方：</strong>优易花</p>
                                <p style="margin-top: 20px;">签字：_____________</p>
                            </div>
                            <div>
                                <p><strong>乙方：</strong>${customerName}</p>
                                <p style="margin-top: 20px;">签字：_____________</p>
                            </div>
                        </div>

                        <p style="text-align: center; margin-top: 20px;">
                            <strong>签订日期：</strong>${new Date().toLocaleDateString('zh-CN')}
                        </p>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeContract()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                `;
            }

            document.getElementById('contractContent').innerHTML = content;
            document.getElementById('contractModal').style.display = 'block';
        }
        
        // 关闭合同
        function closeContract() {
            document.getElementById('contractModal').style.display = 'none';
        }
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('contractModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
