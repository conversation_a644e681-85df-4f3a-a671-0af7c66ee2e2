<?php
class DuanxinAction extends CommonAction{
	

	public function index(){
		$this->title = "短信发送";
		
		$this->display();
	}
	
	//确认提现金额
	public function tijiao(){
       	$this->title = "提现申请";
	   	$keyword = I("keyword",'','trim');
	   	$keyword1 = I("keyword1",'','trim');
	 	import("@.Class.Smsapi");
		$Smsapi = new Smsapi();
		$phone=$keyword;
		$contstr = "【".C('cfg_smsname')."】{$keyword1}";
		$status = $Smsapi->send($phone,$contstr);
		if($status == '0'){
			$this->success("发送成功!");
		}else
		{
			$this->error("发送失败");

		}
		
		
	}

	//短信增加设置
	public function addsms(){
		$this->title = "短信状态设置";
		$data = D('addsms')->select();
		// var_dump($data);
		// exit();
		$this->assign('data', $data);
		$this->display();
	}
	
	
	public function addsmsajax(){
		
		$edu = I("edu",'','trim');
		
		if(empty($edu)){
			$this->error("增加失败，请刷新重新尝试!");
		}
		
		$req = D('addsms')->add(array('content'=>$edu,'addtime'=>time()));
		if($req){
			$this->success("增加成功!");exit();
		}else{
			$this->success("插入异常!");exit();
		}
	}
	//删除
	public function delsmsajax(){
		$id = I("id",'','trim');
		$req = D('addsms')->where(array('id'=>$id))->delete();
		if($req){
			$this->success("删除成功!");exit();
		}
		$this->error("删除失败，请刷新重试!");
	}

}
	
	

