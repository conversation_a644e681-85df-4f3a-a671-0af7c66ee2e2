<?php
/**
 * 后台通信录管理控制器
 */
class ContactsAction extends CommonAction
{
    // 通信录数据列表
    public function index()
    {
        $page = I('p', 1, 'intval');
        $pageSize = 20;
        
        // 搜索条件
        $where = array();
        $phone = I('phone', '', 'trim');
        if ($phone) {
            $where['user_phone'] = array('like', '%' . $phone . '%');
        }
        
        $startTime = I('start_time', '', 'trim');
        $endTime = I('end_time', '', 'trim');
        if ($startTime && $endTime) {
            $where['created_time'] = array('between', array(strtotime($startTime), strtotime($endTime . ' 23:59:59')));
        }
        
        // 获取数据
        $ContactsModel = M('user_contacts');
        $count = $ContactsModel->where($where)->count();
        $list = $ContactsModel->where($where)
                             ->order('updated_time DESC')
                             ->page($page, $pageSize)
                             ->select();
        
        // 处理数据
        foreach ($list as &$item) {
            $item['created_time_format'] = date('Y-m-d H:i:s', $item['created_time']);
            $item['updated_time_format'] = date('Y-m-d H:i:s', $item['updated_time']);
            
            // 解析通信录数据获取基本统计
            $contactsData = json_decode($item['contacts_data'], true);
            $item['valid_contacts'] = 0;
            if (is_array($contactsData)) {
                foreach ($contactsData as $contact) {
                    if (!empty($contact['phone']) && preg_match('/^1[3-9]\d{9}$/', preg_replace('/[^0-9]/', '', $contact['phone']))) {
                        $item['valid_contacts']++;
                    }
                }
            }
        }
        
        // 分页
        $Page = new \Think\Page($count, $pageSize);
        $show = $Page->show();
        
        $this->assign('list', $list);
        $this->assign('page', $show);
        $this->assign('count', $count);
        $this->assign('phone', $phone);
        $this->assign('start_time', $startTime);
        $this->assign('end_time', $endTime);
        $this->display();
    }
    
    // 查看详细通信录
    public function detail()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->error('参数错误');
        }
        
        $ContactsModel = M('user_contacts');
        $info = $ContactsModel->where(array('id' => $id))->find();
        if (!$info) {
            $this->error('数据不存在');
        }
        
        // 解析通信录数据
        $contactsData = json_decode($info['contacts_data'], true);
        $info['contacts_list'] = is_array($contactsData) ? $contactsData : array();
        $info['created_time_format'] = date('Y-m-d H:i:s', $info['created_time']);
        $info['updated_time_format'] = date('Y-m-d H:i:s', $info['updated_time']);
        
        // 获取统计信息
        $StatsModel = M('user_contacts_stats');
        $stats = $StatsModel->where(array('user_phone' => $info['user_phone']))->order('created_time DESC')->find();
        if ($stats) {
            $info['stats'] = json_decode($stats['stats_data'], true);
        }
        
        // 获取风控分析
        $RiskModel = M('contacts_risk_analysis');
        $risk = $RiskModel->where(array('user_phone' => $info['user_phone']))->find();
        if ($risk) {
            $info['risk_analysis'] = $risk;
            $info['risk_analysis']['analysis_result'] = json_decode($risk['analysis_result'], true);
            $info['risk_analysis']['analysis_factors'] = json_decode($risk['analysis_factors'], true);
        }
        
        $this->assign('info', $info);
        $this->display();
    }
    
    // 删除通信录数据
    public function delete()
    {
        $id = I('id', 0, 'intval');
        if (!$id) {
            $this->ajaxReturn(array('status' => 0, 'msg' => '参数错误'));
        }
        
        $ContactsModel = M('user_contacts');
        $info = $ContactsModel->where(array('id' => $id))->find();
        if (!$info) {
            $this->ajaxReturn(array('status' => 0, 'msg' => '数据不存在'));
        }
        
        // 删除相关数据
        $ContactsModel->where(array('id' => $id))->delete();
        M('user_contacts_stats')->where(array('user_phone' => $info['user_phone']))->delete();
        M('contacts_risk_analysis')->where(array('user_phone' => $info['user_phone']))->delete();
        M('contacts_network')->where(array('user_phone' => $info['user_phone']))->delete();
        
        $this->ajaxReturn(array('status' => 1, 'msg' => '删除成功'));
    }
    
    // 统计数据
    public function stats()
    {
        $ContactsModel = M('user_contacts');
        
        // 基本统计
        $totalUsers = $ContactsModel->count();
        $todayUsers = $ContactsModel->where(array('created_time' => array('egt', strtotime('today'))))->count();
        $weekUsers = $ContactsModel->where(array('created_time' => array('egt', strtotime('-7 days'))))->count();
        $monthUsers = $ContactsModel->where(array('created_time' => array('egt', strtotime('-30 days'))))->count();
        
        // 平均通信录数量
        $avgContacts = $ContactsModel->avg('contacts_count');
        
        // 最近7天数据
        $recentData = array();
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $count = $ContactsModel->where(array(
                'created_time' => array('between', array(strtotime($date), strtotime($date . ' 23:59:59')))
            ))->count();
            $recentData[] = array('date' => $date, 'count' => $count);
        }
        
        $stats = array(
            'total_users' => $totalUsers,
            'today_users' => $todayUsers,
            'week_users' => $weekUsers,
            'month_users' => $monthUsers,
            'avg_contacts' => round($avgContacts, 1),
            'recent_data' => $recentData
        );
        
        $this->assign('stats', $stats);
        $this->display();
    }
}
