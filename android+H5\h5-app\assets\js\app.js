/**
 * 优易花H5应用主要JavaScript文件
 */

// 全局变量
let currentPage = 'home';
let isLoggedIn = false;

/**
 * 页面导航函数
 */
function navigateTo(pageName) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(pageName + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
        currentPage = pageName;
        
        // 更新底部导航状态
        updateNavigation(pageName);
    }
}

/**
 * 更新底部导航状态
 */
function updateNavigation(activePage) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    // 根据页面名称激活对应导航项
    const pageNavMap = {
        'home': 0,
        'services': 1,
        'profile': 2
    };
    
    const activeIndex = pageNavMap[activePage];
    if (activeIndex !== undefined && navItems[activeIndex]) {
        navItems[activeIndex].classList.add('active');
    }
}

/**
 * 显示Toast提示
 */
function showToast(message, duration = 2000) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, duration);
    
    // 如果在APP中，也调用原生Toast
    if (typeof Android !== 'undefined') {
        Android.showToast(message);
    }
}

/**
 * 登录表单处理
 */
function initLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            
            // 简单验证
            if (!phone || !password) {
                showToast('请填写完整信息');
                return;
            }
            
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showToast('请输入正确的手机号码');
                return;
            }
            
            // 模拟登录请求
            showToast('正在登录...');
            
            // 这里应该调用实际的登录API
            setTimeout(() => {
                if (phone === '13800138000' && password === '123456') {
                    showToast('登录成功');
                    isLoggedIn = true;
                    navigateTo('profile');
                } else {
                    showToast('用户名或密码错误');
                }
            }, 1000);
        });
    }
}

/**
 * 检查登录状态
 */
function checkLoginStatus() {
    // 从本地存储检查登录状态
    const savedLoginStatus = localStorage.getItem('isLoggedIn');
    if (savedLoginStatus === 'true') {
        isLoggedIn = true;
    }
}

/**
 * 保存登录状态
 */
function saveLoginStatus() {
    localStorage.setItem('isLoggedIn', isLoggedIn.toString());
}

/**
 * 退出登录
 */
function logout() {
    isLoggedIn = false;
    localStorage.removeItem('isLoggedIn');
    showToast('已退出登录');
    navigateTo('home');
}

/**
 * 获取APP版本信息
 */
function getAppVersion() {
    if (typeof Android !== 'undefined') {
        return Android.getAppVersion();
    }
    return 'Web版本';
}

/**
 * 分享功能
 */
function shareApp() {
    const shareText = '推荐一个很好用的APP - 优易花智能服务平台';
    
    if (typeof Android !== 'undefined') {
        Android.shareText(shareText);
    } else {
        // 浏览器中的分享
        if (navigator.share) {
            navigator.share({
                title: '优易花',
                text: shareText,
                url: window.location.href
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareText).then(() => {
                showToast('分享内容已复制到剪贴板');
            });
        }
    }
}

/**
 * 网络状态检查
 */
function checkNetworkStatus() {
    if (!navigator.onLine) {
        showToast('网络连接异常，请检查网络设置');
        return false;
    }
    return true;
}

/**
 * 页面可见性变化处理
 */
function handleVisibilityChange() {
    if (document.hidden) {
        console.log('页面隐藏');
    } else {
        console.log('页面显示');
        // 页面重新显示时可以刷新数据
    }
}

/**
 * 错误处理
 */
function handleError(error) {
    console.error('应用错误:', error);
    showToast('操作失败，请稍后重试');
}

/**
 * 初始化应用
 */
function initApp() {
    console.log('初始化优易花H5应用');
    
    // 检查登录状态
    checkLoginStatus();
    
    // 初始化登录表单
    initLoginForm();
    
    // 监听网络状态变化
    window.addEventListener('online', () => {
        showToast('网络已连接');
    });
    
    window.addEventListener('offline', () => {
        showToast('网络已断开');
    });
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 全局错误处理
    window.addEventListener('error', (e) => {
        handleError(e.error);
    });
    
    // 阻止默认的下拉刷新
    document.addEventListener('touchmove', function(e) {
        if (e.touches.length > 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    // 阻止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(e) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            e.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
    
    console.log('应用初始化完成');
}

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    initApp();
});

/**
 * 导出全局函数（供HTML调用）
 */
window.navigateTo = navigateTo;
window.showToast = showToast;
window.logout = logout;
window.shareApp = shareApp;
window.getAppVersion = getAppVersion;
