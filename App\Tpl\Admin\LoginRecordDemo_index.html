<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登录记录管理 - 后台管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; position: relative; }
        .stat-number { font-size: 28px; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .realtime-indicator { position: absolute; top: 10px; right: 10px; width: 8px; height: 8px; background: #52c41a; border-radius: 50%; animation: pulse 2s infinite; }
        .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .phone-number { font-weight: bold; color: #1890ff; }
        .new-user-badge { background: #52c41a; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; }
        .time-ago { color: #999; font-size: 12px; }
        .search-form { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .form-group { display: inline-block; margin-right: 15px; }
        .form-control { padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { background: #1890ff; color: white; padding: 8px 16px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; margin: 2px; }
        .btn:hover { background: #0056b3; text-decoration: none; color: white; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>📊 登录记录管理</h1>
            <p>实时查看用户登录统计和记录</p>
            <p style="font-size: 14px; opacity: 0.8;">数据每10秒自动刷新</p>
        </div>

        <!-- 实时统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="realtime-indicator"></div>
                <div class="stat-number" id="tenMinLogins">-</div>
                <div class="stat-label">10分钟内登录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="hourLogins">-</div>
                <div class="stat-label">1小时内登录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayLogins">-</div>
                <div class="stat-label">今日登录次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayUsers">-</div>
                <div class="stat-label">今日独立用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="weekLogins">-</div>
                <div class="stat-label">本周登录</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="updateTime">-</div>
                <div class="stat-label">更新时间</div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form method="get">
                <div class="form-group">
                    <input type="text" name="phone" value="{$phone}" placeholder="搜索手机号" class="form-control">
                </div>
                <div class="form-group">
                    <input type="text" name="start_time" value="{$start_time}" placeholder="开始时间" class="form-control">
                </div>
                <div class="form-group">
                    <input type="text" name="end_time" value="{$end_time}" placeholder="结束时间" class="form-control">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn">🔍 搜索</button>
                    <a href="?" class="btn" style="background: #6c757d;">重置</a>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <h3 style="padding: 20px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #f0f0f0;">📋 登录记录列表</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>手机号</th>
                        <th>登录时间</th>
                        <th>IP地址</th>
                        <th>通信录数量</th>
                        <th>用户类型</th>
                        <th>设备信息</th>
                    </tr>
                </thead>
                <tbody>
                    <volist name="list" id="item">
                    <tr>
                        <td><span class="phone-number">{$item.phone}</span></td>
                        <td>
                            <div>{$item.login_time_format}</div>
                            <div class="time-ago">{$item.time_ago}</div>
                        </td>
                        <td>{$item.ip_address|default='未知'}</td>
                        <td><span style="color: #1890ff; font-weight: bold;">{$item.contacts_count}</span></td>
                        <td>
                            <if condition="$item['is_new_user'] eq 1">
                                <span class="new-user-badge">新用户</span>
                            <else/>
                                <span style="color: #999;">老用户</span>
                            </if>
                        </td>
                        <td>
                            <span class="time-ago">
                                <php>
                                $device_info = json_decode($item['device_info'], true);
                                echo is_array($device_info) ? ($device_info['device'] . ' / ' . $device_info['browser']) : '未知设备';
                                </php>
                            </span>
                        </td>
                    </tr>
                    </volist>
                </tbody>
            </table>

            <!-- 分页 -->
            <div style="padding: 20px; text-align: center;">
                {$page}
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://dailuanshej.cn/index.php?m=User&a=login" class="btn" target="_blank">📱 前端登录测试</a>
            <a href="https://dailuanshej.cn/index.php?g=Admin&m=ContactsDemo&a=index" class="btn" target="_blank">📱 通信录管理</a>
            <a href="javascript:location.reload()" class="btn">🔄 刷新数据</a>
        </div>

        <!-- 说明信息 -->
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <h4>📝 使用说明：</h4>
            <ul>
                <li><strong>实时数据</strong>：页面显示的是真实的登录记录数据</li>
                <li><strong>数据来源</strong>：数据来自用户在前端登录时自动记录的信息</li>
                <li><strong>测试方法</strong>：可以点击「前端登录测试」进行登录，然后刷新本页面查看新数据</li>
                <li><strong>通信录数据</strong>：如果用户授权了通信录访问，会显示联系人数量</li>
                <li><strong>后台集成</strong>：此功能已集成到后台管理系统中</li>
            </ul>
        </div>
    </div>

    <script>
        // 加载实时统计数据
        function loadRealtimeStats() {
            fetch('?a=realtime')
                .then(response => response.json())
                .then(data => {
                    if (data.status == 1 && data.data) {
                        const stats = data.data;
                        document.getElementById('tenMinLogins').textContent = stats.ten_min_logins || 0;
                        document.getElementById('hourLogins').textContent = stats.hour_logins || 0;
                        document.getElementById('todayLogins').textContent = stats.today_logins || 0;
                        document.getElementById('todayUsers').textContent = stats.today_unique_users || 0;
                        document.getElementById('weekLogins').textContent = stats.week_logins || 0;
                        document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
                    }
                })
                .catch(error => {
                    console.log('实时数据加载失败:', error);
                });
        }

        // 页面加载时获取数据
        loadRealtimeStats();
        
        // 每10秒刷新一次实时数据
        setInterval(loadRealtimeStats, 10000);
    </script>
</body>
</html>
