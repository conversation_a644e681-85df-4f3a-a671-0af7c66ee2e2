{__NOLAYOUT__}
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8"/>
<title>Admin Manager -<somnus:sitecfg name="sitetitle" /> - 0（0） </title>
<meta name="author" content="DeathGhost" />
<link rel="stylesheet" type="text/css" href="__PUBLIC__/admin/logint/css/style.css" tppabs="css/style.css" />
<style>
body{height:100%;background:#16a085;overflow:hidden;}
canvas{z-index:-1;position:absolute;}
.message
{
	position: fixed;
	margin: auto;
	top: 300px;
	bottom: 0;
	left: 0;
	right: 0;
	text-align: center;
	
	display: none;
	max-width: 60%;
	transform: scale(0);
	opacity: 0;
	z-index: 9999999;
}

.message p
{
	padding: 10px 25px;
	background: rgba(0, 0, 0, 0.75);
	color: #ffffff;
	border-radius: 5px;
}
.m-show
{
	animation: myfirst 0.6s;
	-webkit-animation: myfirst 0.6s; 
	animation-fill-mode:forwards;
	-webkit-animation-fill-mode:forwards; 
}

@keyframes myfirst
{
	
    50% {
		transform: scale(0);
		opacity: 0;
	}
    100% {
		transform: scale(1);
		opacity: 1;
	}
}

.m-hide
{
	animation: hide 0.4s;
	-webkit-animation: hide 0.4s; 
	animation-fill-mode:forwards;
	-webkit-animation-fill-mode:forwards; 
}

@keyframes hide
{
	
    0% {
		transform: scale(1);
		opacity: 1;
	}
    100% {
		transform: scale(0);
		opacity: 0;
	}
}
</style>
<script src="__PUBLIC__/admin/logint/js/jquery.js"></script>
<script src="__PUBLIC__/admin/logint/js/verificationNumbers.js" tppabs="js/verificationNumbers.js"></script>
<script src="__PUBLIC__/admin/logint/js/Particleground.js" tppabs="js/Particleground.js"></script>
	<script src="__PUBLIC__/layer/layer.js"></script>
 
<script>
$(document).ready(function() {
  //粒子背景特效
  $('body').particleground({
    dotColor: '#5cbdaa',
    lineColor: '#5cbdaa'
  });


});
</script>
</head>
<body>
<dl class="admin_login">
 <dt>
  <strong><somnus:sitecfg name="sitename" /></strong>
  <em>Management System</em>
 </dt>
 <form> 
 <dd class="user_icon">
  <input name="username" type="text" placeholder="账号" class="login_txtbx" id="username" />
 </dd>
 <dd class="pwd_icon">
  <input name="password"  type="password" placeholder="密码" class="login_txtbx" id="password" />
 </dd>
 </form>
 
 <!--
 <dd class="val_icon">
  <div class="checkcode">
    <input type="text" id="J_codetext" placeholder="验证码" maxlength="4" class="login_txtbx">
    <canvas class="J_codeimg" id="myCanvas" onclick="createCode()">对不起，您的浏览器不支持canvas，请下载最新版浏览器!</canvas>
  </div>
  <input type="button" value="验证码核验" class="ver_btn" onClick="validate();">
 </dd>-->
 <dd>
  <input type="button" id="submit" value="合法使用立即登陆" class="submit_btn"/>
 </dd>
 
</dl><div class="message">
			<p></p>
		</div>

<script>
    // 倒计时
    function myTimer(){
		var sec = 3;
		var timer;
            clearInterval(timer);
            timer = setInterval(function() { 
                console.log(sec--);
                if(sec == 1){
                    $(".message").addClass("m-hide");
                    $(".message").removeClass("m-show");
                }
                if (sec == 0) {
                    $(".message").hide();
                    $(".message").removeClass("m-hide");
                    clearInterval(timer);
                } 
            } , 1000);
    }

    // 弹窗内容
    function message(data){
        msg = $(".message p").html(data);
        $(".message").addClass("m-show");
        $(".message").show();
        
        myTimer();
        
    }

    // 初始化弹窗
    function mesg_default(){
        msg = '';
        $(".message").hide();
        $(".message").removeClass("m-show");
        $(".message").removeClass("m-hide");
    }
		$(document).ready(function () {
			$("#submit").click(function () {
				mesg_default();
				 username = $("#username").val();
				 password = $("#password").val();
				$.post(
					 "/index.php?g=Admin&m=Index&a=login",
					{ "username": username, "password": password },	
                    function (data,state){	
						console.log(data);
						if(state != "success"){
							message("请求失败,请稍后重试!");
						}else if(data.status != 1){
							message(data.msg);
						}else{
						    //   layer.confirm('本源码仅供学习及研究所用', {
							// 	btn: ['同意','拒绝'] //按钮
							// 	}, function(){
							// 	layer.msg('我同意', {icon: 1});
								window.location.href = "/index.php?g=Admin&m=Main&a=index";
								// }, function(){
								//  layer.msg('也可以这样', {
								// 	time: 20000, //20s后自动关闭
								//  	btn: ['明白了', '知道了']

								//  });
								// "index.php/Admin/Index/logout";
								// });
							
						}
						return false;
					},
					"json"
				);
			});
		});
	</script>

</body>
</html>
