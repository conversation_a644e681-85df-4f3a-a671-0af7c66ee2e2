<?php
class MainAction extends CommonAction{
	public function index(){
		$this->title = '管理中心';
		//判断install.php是否存在
        $install_status = 0;
        if(file_exists('./install')){
            $install_status = 1;
        }
        $this->install_status = $install_status;
        //获取4条登录记录
        $Admin_login = D("admin_login");
        $loginData = $Admin_login->order('logintime Desc')->where(array('username' => $this->getlogin() ))->limit(4)->select();
        $this->loginData = $loginData;

        //获取4条前台用户最新登录记录
        $User_login = D("user");
        $frontData = $User_login->order('last_time Desc')->limit(4)->select();
        $this->frontData = $frontData;

        //获取统计数据
        $userModel = D('user');
        //当天注册会员数
        $data['dayRegNum'] = $userModel->where(array('addtime' => array('EGT', strtotime(date('Y-m-d')))))->count();
        //总的注册会员数
        $data['sumRegNum'] = $userModel->count();
        //当天借款订单总数
        $loanorderModel = D('order');
        $data['dayLoanNum'] = $loanorderModel->where(array('addtime' => array('EGT', strtotime(date('Y-m-d')))))->count();
        //总的借款订单数
        $data['sumLoanNum'] = $loanorderModel->count();
        //当天放款订单数
        $voucherModel = D('voucher');
        $data['dayAgreeOrderNum'] = count($voucherModel->group('user')->where(array('addtime' => array('EGT', date('Y-m-d'))))->select());
        //总的放款订单数
        $data['sumAgreeOrderNum'] = count($voucherModel->group('user')->select());
        //当天借款金额
        $data['dayAgreeOrderMoney'] = $loanorderModel->where(array('addtime' => array('EGT', strtotime(date('Y-m-d')))))->sum('money') ==""?0:$loanorderModel->where(array('addtime' => array('EGT', strtotime(date('Y-m-d')))))->sum('money');
        //累计借款金额
        $data['sumAgreeOrderMoney'] = $loanorderModel->sum('money')==""?0:$loanorderModel->sum('money');

        $this->assign('data', $data);
		$this->display();
	}


	public function clearcache(){
		$this->title = '清除缓存';
		//清除缓存
		delDir(RUNTIME_PATH);
		$this->success('清理完成!',U(GROUP_NAME.'/Main/index'));
	}
    //新增注册会员接口
	public function vipnum(){
        $userModel = D('user');
        for ($x=6; $x>=0; $x--) {
            $y = $x-1;
            $yestoday = strtotime(date('Y-m-d', strtotime('- '.$x.' day')));
            if($y<0){
                $today = strtotime(date('Y-m-d', strtotime('+1 day')));
            }else{
                $today = strtotime(date('Y-m-d', strtotime('- '.$y.' day')));
            }
            $RegNum[] = $userModel->where(array('addtime' => array('between', array($yestoday,$today))))->count();
            $date[] = date('d', strtotime('- '.$x.' day'));
        }
        $data['vipnum'] = $RegNum;
        $data['date'] =$date;
        $data=json_encode($data);
        echo $data;
    }
    //系统订单数量
    public function orders(){
        $orderModel = D('order');
        $voucherModel = D('voucher');
        for ($x=6; $x>=0; $x--) {
            $y = $x-1;
            $yestoday = strtotime(date('Y-m-d', strtotime('- '.$x.' day')));
            if($y<0){
                $today = strtotime(date('Y-m-d', strtotime('+1 day')));
                $today1 = date('Y-m-d', strtotime('+1 day'));
            }else{
                $today = strtotime(date('Y-m-d', strtotime('- '.$y.' day')));
                $today1 = date('Y-m-d', strtotime('- '.$y.' day'));
            }

            //$today = strtotime(date('Y-m-d', strtotime('- '.$y.' day')));
            $yestoday1 = date('Y-m-d', strtotime('- '.$x.' day'));
            //$today1 = date('Y-m-d', strtotime('- '.$y.' day'));

            $orderNum[] = $orderModel->where(array('addtime' => array('between', array($yestoday,$today))))->count();
            $voucherNum[] = count($voucherModel->group('user')->where(array('addtime' => array('between', array($yestoday1,$today1))))->select());
            $date[] = date('d', strtotime('- '.$x.' day'));
        }
        $data['ordernum'] = $orderNum;
        $data['vouchernum'] = $voucherNum;
        $data['date'] =$date;
        $data=json_encode($data);
        echo $data;
    }

}