<?php
class BillsAction extends CommonAction{

    public function index(){
        $Order = M('order');
        import('ORG.Util.Page');
        $keyword = I("keyword",'','trim');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where['user'] = $keyword;
        }
        

      

        $count = $Order->where($where)->count();
        $Page  = new Page($count,25);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $list = $Order->where($where)->order('id desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $da = M('userinfo')->getField('user,name');
        $s['status'] = 0;
        $s['zfimg'] = array('neq','');

        $vouche = M('voucher')->where($s)->group('ordernum')->getField('ordernum,id');
        $this->title='还款列表';
        $this->data = $da;
        $this->list = $list;
        $this->page = $show;
        $this->vouche = $vouche;
        $this->display();
    }

    //还款详情列表
    public function vodetail(){
        $Order = M('voucher');
        import('ORG.Util.Page');
        $ordernum = I('get.ordernum');
        $where['ordernum'] = $ordernum;
        $count = $Order->where($where)->count();
        $Page  = new Page($count,25);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $list = $Order->where($where)->order('huantime asc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $da = M('userinfo')->getField('user,name');
        $this->data = $da;
        $this->list = $list;
        $this->page = $show;
        $this->time = date('Y-m-d');
        // var_dump($ordernum);
        $this->display();


    }

}
