<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>💬 在线客服 - 小贷系统</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 30px 20px 40px;
            color: white;
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .header-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 250px);
            padding: 25px 20px 100px;
            margin-top: -20px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 客服卡片 */
        .service-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border: 2px solid transparent;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .service-card:hover::before {
            opacity: 1;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            z-index: 1;
        }

        .service-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .service-desc {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .service-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            z-index: 1;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .service-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .service-btn:active {
            transform: scale(0.95);
        }

        /* 常见问题区域 */
        .faq-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .faq-list {
            background: #f8f9fa;
            border-radius: 16px;
            overflow: hidden;
        }

        .faq-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .faq-item:hover {
            background: #e9ecef;
            padding-left: 25px;
        }

        .faq-item .icon {
            font-size: 20px;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }

        .faq-item .text {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
        }

        .faq-item .arrow {
            color: #999;
            font-size: 14px;
        }

        /* 工作时间 */
        .work-time {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            border: 1px solid #e3f2fd;
        }

        .work-time-icon {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .work-time-title {
            font-size: 16px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 8px;
        }

        .work-time-text {
            font-size: 14px;
            color: #424242;
            line-height: 1.5;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            padding: 10px 0 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item-bottom {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 12px;
            transition: all 0.2s ease;
            padding: 5px 10px;
        }

        .nav-item-bottom.active {
            color: #667eea;
        }

        .nav-item-bottom:hover, .nav-item-bottom:active {
            color: #667eea;
            text-decoration: none;
        }

        .nav-icon-bottom {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* 底部安全区域 */
        .bottom-safe-area {
            height: 80px;
            background: transparent;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }

            .header {
                padding: 25px 15px 35px;
            }

            .main-content {
                padding: 20px 15px 80px;
            }

            .service-card {
                padding: 25px 20px;
            }

            .service-icon {
                width: 70px;
                height: 70px;
                font-size: 32px;
            }

            .header-title {
                font-size: 24px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="header-title">💬 在线客服</div>
            <div class="header-subtitle">我们随时为您提供帮助</div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 客服联系卡片 -->
            <div class="service-card">
                <div class="service-icon">💬</div>
                <div class="service-title">联系客服</div>
                <div class="service-desc">专业客服团队为您提供7×24小时在线服务，解答您的疑问</div>
                <a href="{:C('cfg_kefu_link')}" class="service-btn">立即咨询</a>
            </div>

            <!-- 工作时间 -->
            <div class="work-time">
                <div class="work-time-icon">🕐</div>
                <div class="work-time-title">服务时间</div>
                <div class="work-time-text">周一至周日 09:00-21:00<br>节假日正常服务</div>
            </div>

            <!-- 常见问题 -->
            <div class="faq-section">
                <h2 class="section-title">
                    <span>❓</span>
                    常见问题
                </h2>
                <div class="faq-list">
                    <a href="javascript:void(0)" class="faq-item" onclick="showFAQ('如何申请借款？', '您可以在首页点击申请借款按钮，填写相关信息即可提交申请。')">
                        <span class="icon">💰</span>
                        <span class="text">如何申请借款？</span>
                        <span class="arrow">›</span>
                    </a>
                    <a href="javascript:void(0)" class="faq-item" onclick="showFAQ('借款审核需要多长时间？', '通常情况下，我们会在1-3个工作日内完成审核，审核结果会通过短信或电话通知您。')">
                        <span class="icon">⏰</span>
                        <span class="text">借款审核需要多长时间？</span>
                        <span class="arrow">›</span>
                    </a>
                    <a href="javascript:void(0)" class="faq-item" onclick="showFAQ('如何还款？', '您可以通过我的钱包页面进行还款操作，支持银行卡转账等多种方式。')">
                        <span class="icon">💳</span>
                        <span class="text">如何还款？</span>
                        <span class="arrow">›</span>
                    </a>
                    <a href="javascript:void(0)" class="faq-item" onclick="showFAQ('忘记还款时间怎么办？', '系统会在还款日前3天通过短信提醒您，您也可以在我的借款页面查看详细信息。')">
                        <span class="icon">📅</span>
                        <span class="text">忘记还款时间怎么办？</span>
                        <span class="arrow">›</span>
                    </a>
                </div>
            </div>

            <!-- 底部安全区域 -->
            <div class="bottom-safe-area"></div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="{:U('Index/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">🏠</span>
                    <span>首页</span>
                </a>
                <a href="{:U('Qianbao/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">💰</span>
                    <span>钱包</span>
                </a>
                <a href="{:U('Help/index')}" class="nav-item-bottom active">
                    <span class="nav-icon-bottom">💬</span>
                    <span>客服</span>
                </a>
                <a href="{:U('User/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">👤</span>
                    <span>我的</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');
        });

        // 显示常见问题答案
        function showFAQ(question, answer) {
            alert(`问题：${question}\n\n答案：${answer}`);
        }

        // 添加触摸反馈
        document.querySelectorAll('.service-btn, .faq-item, .nav-item-bottom').forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            item.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    </script>
</body>
</html>