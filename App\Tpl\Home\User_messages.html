<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的消息 - 小贷系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007cba;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px 0;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            border: none;
        }

        .message-item {
            border: none;
            border-radius: 12px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .message-unread {
            border-left: 4px solid var(--primary-color);
            background: linear-gradient(135deg, #f8f9ff, #ffffff);
        }

        .message-read {
            border-left: 4px solid var(--secondary-color);
            opacity: 0.8;
        }

        .message-type-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 20px;
            font-weight: 500;
        }

        .message-type-1 { background: var(--info-color); color: white; }
        .message-type-2 { background: var(--success-color); color: white; }
        .message-type-3 { background: var(--warning-color); color: white; }
        .message-type-4 { background: var(--danger-color); color: white; }

        .message-time {
            color: var(--secondary-color);
            font-size: 0.85rem;
        }

        .message-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .message-content {
            color: var(--secondary-color);
            line-height: 1.5;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }

        .btn-modern {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,186,0.4);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--secondary-color);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .navbar-custom {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }
            
            .card {
                margin: 0 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{:U('Index/index')}">
                <i class="fas fa-coins"></i> 小贷系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('Index/index')}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{:U('User/loanInfo')}">
                            <i class="fas fa-credit-card"></i> 借款信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{:U('User/messages')}">
                            <i class="fas fa-envelope"></i> 我的消息
                            <if condition="$unread_count gt 0">
                                <span class="badge bg-danger">{$unread_count}</span>
                            </if>
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> 个人中心
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{:U('User/profile')}">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{:U('User/logout')}">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-container" style="margin-top: 80px;">
        <div class="container">
            <!-- 统计卡片 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">总消息数</h6>
                                <div class="stats-number">{$messageList|count}</div>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">未读消息</h6>
                                <div class="stats-number">{$unread_count}</div>
                            </div>
                            <div class="fs-1 opacity-50">
                                <i class="fas fa-bell"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 消息列表 -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-inbox"></i> 我的消息
                    </h4>
                </div>
                <div class="card-body">
                    <if condition="$messageList">
                        <volist name="messageList" id="vo">
                            <div class="message-item card {$vo.is_read ? 'message-read' : 'message-unread'}" 
                                 onclick="viewMessage({$vo.id})" style="cursor: pointer;">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="message-type-badge message-type-{$vo.message_type}">
                                                    {$vo.message_type_text}
                                                </span>
                                                <if condition="$vo.is_read eq 0">
                                                    <span class="badge bg-primary ms-2">新消息</span>
                                                </if>
                                            </div>
                                            <h6 class="message-title">{$vo.title}</h6>
                                            <p class="message-content mb-0">
                                                {$vo.content|substr=0,100}
                                                <if condition="strlen($vo.content) gt 100">...</if>
                                            </p>
                                        </div>
                                        <div class="text-end">
                                            <div class="message-time">
                                                <i class="fas fa-clock"></i>
                                                {$vo.created_time_format}
                                            </div>
                                            <if condition="$vo.is_read eq 0">
                                                <div class="mt-1">
                                                    <i class="fas fa-circle text-primary" style="font-size: 0.5rem;"></i>
                                                </div>
                                            </if>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </volist>
                    <else/>
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <h5>暂无消息</h5>
                            <p class="text-muted">您还没有收到任何消息</p>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 查看消息详情
        function viewMessage(messageId) {
            window.location.href = '{:U("User/messageDetail")}?id=' + messageId;
        }

        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为消息项添加进入动画
            const messageItems = document.querySelectorAll('.message-item');
            messageItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // 自动刷新未读消息数
        setInterval(function() {
            fetch('{:U("User/getUnreadCount")}')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        const badge = document.querySelector('.navbar .badge');
                        if (data.unread_count > 0) {
                            if (badge) {
                                badge.textContent = data.unread_count;
                            }
                        } else {
                            if (badge) {
                                badge.remove();
                            }
                        }
                    }
                })
                .catch(error => console.log('刷新未读消息数失败:', error));
        }, 30000); // 每30秒刷新一次
    </script>
</body>
</html>
