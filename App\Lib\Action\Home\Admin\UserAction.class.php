<?php
class UserAction extends CommonAction{

    //用户列表
    public function index(){

        $this->title = "用户管理";
        $keyword = I("keyword",'','trim');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where['phone'] = array('like',"%{$keyword}%");
        }
        


        $User = D("user");
        import('ORG.Util.Page');
        $count = $User->where($where)->count();
        $Page  = new Page($count,20);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $where1['user.phone'] = array('like',"%{$keyword}%");
        // $list = $User->where($where)->order('addtime Desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $list = $User->where($where1)->join('userinfo on user.phone = userinfo.user')->field('user.*,userinfo.name,userinfo.usercard')->order('addtime Desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $sms = D('addsms')->select();
        $this->sms = $sms;
        $this->list = $list;
        $this->page = $show;
        $this->count =$count;
        $this->display();
    }

    //允许/禁止用户登录
    public function status(){
        $this->title = "更改用户状态";
        $id = I("id",0,'trim');
        if(!$id){
            $this->error("参数错误!");
        }
        $User = D("user");
        $info = $User->where(array('id' => $id))->find();
        if(!$info){
            $this->error("用户不存在!");
        }
        $newstatus = empty($info['status'])?1:0;
        $status = $User->where(array('id' => $id))->save(array('status' => $newstatus));
        if(!$status){
            $this->error("操作失败!");
        }
        $this->success("变更用户状态成功!");
    }

    //删除用户
    public function del(){
        $this->title='删除用户';
        $id = I('id',0,'trim');
        if(!$id){
            $this->error("参数有误!");
        }
        $User = D("user");
        $status = $User->where(array('id' => $id))->delete();
        if(!$status){
            $this->error("删除失败!");
        }
        $this->success("删除用户成功!");
    }


    //修改用户密码
    public function changepass(){
        $data = array('status' => 0,'msg' => '未知错误');
        $id = I('post.id',0,'trim');
        $pass = I("post.pass",'','trim');
        if(!$id || !$pass){
            $data['msg'] = "参数有误!";
        }else{
            $User = D("user");
            $pass = sha1(md5($pass));
            $status = $User->where(array('id' => $id))->save(array('password' => $pass));
            if($status === false){
                $data['msg'] = "操作失败!";
            }else{
                $data['status'] = 1;
                $data['msg'] = "密码修改成功!";
            }
        }
        $this->ajaxReturn($data);
    }
	//修改用户金额
	public function changeedu(){
		$data = array('status' => 0,'msg' => '未知错误');
        $id = I('post.id',0,'trim');
		$money = I("post.edu",'','trim');
        if(!$id || !$money){
            $data['msg'] = "参数有误!";
        }else{
        	$User = D("user");
            $user = $User->where(array('id'=>$id))->find();
			$status = $User->where(array('id' => $id))->save(array('edu' => $money));
			if(!$status){
				$data['msg'] = "操作失败!";
			}else{
				$data['status'] = 1;
			}
        }
		$this->ajaxReturn($data);
	}
    //查看用户资料
    public function userinfo(){
        $this->title = "查看用户资料";
        $user = I("user",'','trim');
        if(!$user){
            $this->error("参数错误!");
        }
        $Userinfo = D("userinfo");
        $info = $Userinfo->where(array('user' => $user))->find();
        $this->baseinfo = $info;
        $Otherinfo = D("Otherinfo");
        $info = $Otherinfo->where(array('user' => $user))->find();
        $info = $info['infojson'];
        $this->otherinfo = $info;
        $this->display();
    }
    //修改身份证
    public function baseinfo(){
        $id = I("id",'','trim');
        $cardphoto_1 = I("cardphoto_1",'','trim');
        $cardphoto_2 = I("cardphoto_2",'','trim');
        $cardphoto_3 = I("cardphoto_3",'','trim');
        $data = array('status' => 0,'msg' => '未知错误');
        $Userinfo = D("userinfo");
        $status =  $Userinfo->where(array('id' => $id))->save(array('cardphoto_1' => $cardphoto_1,'cardphoto_2' => $cardphoto_2,'cardphoto_3' => $cardphoto_3));
        if(!$status){
            $data['msg'] = "操作失败!";
        }else{
            $data['status'] = 1;
        }  
        $this->ajaxReturn($data);
    }
    public function qianbao(){
        $this->title = "用户钱包";
        $keyword = I("keyword",'','trim');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where['phone'] = array('like',"%{$keyword}%");
        }
        
        $User = D("user");
        import('ORG.Util.Page');
        $count = $User->where($where)->count();
        $Page  = new Page($count,25);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $list = $User->where($where)->order('addtime Desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $this->list = $list;
        $this->page = $show;
        $this->count = $count;
        $this->display();
    }

    //充值
    public function chongzhi(){
        $id = I("id",'','trim');
        $money = I("money",'','trim');
        $User = D("user");
        $status = $User->where(array('id' => $id))->setInc('zhanghuyue',$money);

        if($status){
            $data['status'] = 1;
            $data['msg'] = "账号充值成功!";
        }else{
            $data['status'] = 0;
            $data['msg'] = "操作失败!";
        }
        $this->ajaxReturn($data);
    }

    //扣款
    public function koukuan(){
        $id = I("id",'','trim');
        $money = I("money",'','trim');
        $User = D("user");
        $status = $User->where(array('id' => $id))->setDec('zhanghuyue',$money);

        if($status){
            $data['status'] = 1;
            $data['msg'] = "手动扣款成功!";
        }else{
            $data['status'] = 0;
            $data['msg'] = "操作失败!";
        }
        $this->ajaxReturn($data);
    }
    //短信群发

    public function smsall() {
        $id = I("id",'','trim');
        $edu = I("edu",'','trim');
        if (!$id) {
            $this->error('没有手机号码');
        }
        if (!$edu) {
            $this->error('请选择需要发送给用户的信息');
        }
        $user = D('user')->where(array('id'=>array('in',$id)))->field('phone')->select();
        $arr = [];
        foreach($user as $value){
            // $arr = $value['phone'];
            array_push($arr,$value['phone']);
        }
        $phone = implode(",", $arr);
        $content = str_replace('@sitename@', C('siteName'), $edu);
        $content = str_replace('《@sitename@》', C('siteName'), $content);
        $content = str_replace('@username@', $info['name'], $content);
        $result = sendTsms($phone, $content);
        if($result){
            $this->success('保存成功');
        }
    }
    
    public function delall(){
    	$id = I("id",'','trim');
    	if (!$id) {
            $this->error('非法进入');
        }
        $result = D('user')->where(['id'=>['in',$id]])->delete();
        // var_dump(D('user')->getLastSql());
        // exit();
        if($result){
        	$this->success('删除成功');
        }
    }

}
