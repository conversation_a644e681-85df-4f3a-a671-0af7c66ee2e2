<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>👤 个人中心 - 小贷系统</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 30px 20px 40px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        /* 用户信息卡片 */
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .avatar {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            border: 3px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .user-phone {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
        }

        /* 余额卡片 */
        .balance-card {
            background: rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            z-index: 1;
        }

        .balance-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .balance-amount {
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .balance-unit {
            font-size: 18px;
            margin-left: 5px;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 280px);
            padding: 25px 20px 100px;
            margin-top: -20px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 功能菜单 */
        .menu-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .function-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .function-item {
            display: block;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .function-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .function-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .function-item:hover .function-card {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .function-item:hover .function-card::before {
            opacity: 1;
        }

        .function-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .function-text {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            position: relative;
            z-index: 1;
        }

        /* 更多功能区域 */
        .more-section {
            margin-bottom: 30px;
        }

        .more-list {
            background: #f8f9fa;
            border-radius: 16px;
            overflow: hidden;
        }

        .more-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .more-item:last-child {
            border-bottom: none;
        }

        .more-item:hover {
            background: #e9ecef;
            padding-left: 25px;
        }

        .more-item .icon {
            font-size: 20px;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }

        .more-item .text {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
        }

        .more-item .badge {
            background: #ff4757;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            margin-right: 10px;
        }

        .more-item .arrow {
            color: #999;
            font-size: 14px;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 414px;
            width: 100%;
            background: white;
            padding: 10px 0 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item-bottom {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 12px;
            transition: all 0.2s ease;
            padding: 5px 10px;
        }

        .nav-item-bottom.active {
            color: #667eea;
        }

        .nav-item-bottom:hover, .nav-item-bottom:active {
            color: #667eea;
            text-decoration: none;
        }

        .nav-icon-bottom {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* 底部安全区域 */
        .bottom-safe-area {
            height: 80px;
            background: transparent;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 25px 15px 35px;
            }
            
            .main-content {
                padding: 20px 15px 80px;
            }
            
            .function-grid {
                gap: 12px;
            }
            
            .function-card {
                padding: 16px;
            }
            
            .function-icon {
                font-size: 28px;
                margin-bottom: 10px;
            }
            
            .function-text {
                font-size: 13px;
            }

            .user-info {
                gap: 12px;
            }

            .avatar {
                width: 55px;
                height: 55px;
                font-size: 24px;
            }

            .user-name {
                font-size: 18px;
            }

            .balance-card {
                padding: 18px;
            }

            .balance-amount {
                font-size: 28px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* 点击反馈 */
        .function-item:active .function-card {
            transform: scale(0.95);
        }

        .more-item:active {
            transform: scale(0.98);
        }

        .nav-item-bottom:active {
            transform: scale(0.95);
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="user-info">
                <div class="avatar">👤</div>
                <div class="user-details">
                    <div class="user-name">
                        <if condition="$user eq 0">
                            <span onclick="javascript:window.location.href='{:U(\'User/login\')}'" style="cursor: pointer;">请登录</span>
                        <else />
                            欢迎回来！
                        </if>
                    </div>
                    <div class="user-phone">
                        <if condition="$user eq 0">
                            点击登录
                        <else />
                            {$user}
                        </if>
                    </div>
                </div>
            </div>

            <!-- 余额卡片 -->
            <div class="balance-card">
                <div class="balance-label">可提现金额</div>
                <div class="balance-amount">
                    <empty name="users.zhanghuyue">0<else />{$users.zhanghuyue}</empty>
                    <span class="balance-unit">元</span>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 功能菜单 -->
            <div class="menu-section">
                <h2 class="section-title">
                    <span>📱</span>
                    功能菜单
                </h2>
                <div class="function-grid">
                    <a href="{:U('Qianbao/index')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">💰</span>
                            <span class="function-text">我的钱包</span>
                        </div>
                    </a>
                    <a href="{:U('Order/lists')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">📋</span>
                            <span class="function-text">我的借款</span>
                        </div>
                    </a>
                    <a href="{:U('Order/bills')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">💳</span>
                            <span class="function-text">我的还款</span>
                        </div>
                    </a>
                    <a href="{:U('Info/index')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">👤</span>
                            <span class="function-text">我的资料</span>
                        </div>
                    </a>
                    <a href="{:U('User/evaluation')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">📊</span>
                            <span class="function-text">风险测评</span>
                        </div>
                    </a>
                    <a href="{:U('User/setup')}" class="function-item">
                        <div class="function-card">
                            <span class="function-icon">⚙️</span>
                            <span class="function-text">设置</span>
                        </div>
                    </a>
                </div>
            </div>

            <!-- 更多功能 -->
            <div class="more-section">
                <h2 class="section-title">
                    <span>📱</span>
                    更多功能
                </h2>
                <div class="more-list">
                    <a href="{:U('User/loanInfo')}" class="more-item">
                        <span class="icon">ℹ️</span>
                        <span class="text">查看借款信息</span>
                        <span class="badge">重要</span>
                        <span class="arrow">›</span>
                    </a>
                    <a href="{:U('User/question')}" class="more-item">
                        <span class="icon">❓</span>
                        <span class="text">常见问题</span>
                        <span class="arrow">›</span>
                    </a>
                </div>
            </div>

            <!-- 底部安全区域 -->
            <div class="bottom-safe-area"></div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="{:U('Index/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">🏠</span>
                    <span>首页</span>
                </a>
                <a href="{:U('Qianbao/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">💰</span>
                    <span>钱包</span>
                </a>
                <a href="{:U('Help/index')}" class="nav-item-bottom">
                    <span class="nav-icon-bottom">💬</span>
                    <span>客服</span>
                </a>
                <a href="{:U('User/index')}" class="nav-item-bottom active">
                    <span class="nav-icon-bottom">👤</span>
                    <span>我的</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');

            // 添加点击反馈
            const functionItems = document.querySelectorAll('.function-item, .more-item, .nav-item-bottom');
            functionItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                item.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        });
    </script>
</body>
</html>
