package com.youyihua.app;

import androidx.appcompat.app.AppCompatActivity;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.webkit.JavascriptInterface;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

/**
 * 优易花APP主活动
 * 这是一个轻量级的WebView容器，所有业务逻辑都在H5页面中实现
 */
public class MainActivity extends AppCompatActivity {
    
    private WebView webView;
    private static final String BASE_URL = "https://dailuanshej.cn/";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化WebView
        initWebView();
        
        // 加载H5应用
        loadH5App();
    }
    
    /**
     * 初始化WebView配置
     */
    private void initWebView() {
        webView = findViewById(R.id.webview);
        
        // WebView基础设置
        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);                    // 启用JavaScript
        settings.setDomStorageEnabled(true);                    // 启用DOM存储
        settings.setAllowFileAccess(true);                      // 允许文件访问
        settings.setAllowContentAccess(true);                   // 允许内容访问

        // 允许混合内容 (API 21+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        settings.setCacheMode(WebSettings.LOAD_DEFAULT);        // 缓存模式
        settings.setUseWideViewPort(true);                      // 支持viewport
        settings.setLoadWithOverviewMode(true);                 // 缩放至屏幕大小
        settings.setBuiltInZoomControls(false);                 // 禁用缩放控件
        settings.setSupportZoom(false);                         // 禁用缩放
        
        // 设置User-Agent（标识这是优易花APP）
        String userAgent = settings.getUserAgentString();
        settings.setUserAgentString(userAgent + " YouYiHuaApp/1.0");
        
        // 设置WebViewClient
        webView.setWebViewClient(new CustomWebViewClient());
        
        // 设置WebChromeClient
        webView.setWebChromeClient(new CustomWebChromeClient());
        
        // 添加JavaScript接口
        webView.addJavascriptInterface(new WebAppInterface(), "Android");
    }
    
    /**
     * 加载原站登录页面
     */
    private void loadH5App() {
        String fullUrl = BASE_URL + "index.php";
        android.util.Log.d("MainActivity", "正在加载原站登录页面: " + fullUrl);

        // 显示加载提示
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                android.widget.Toast.makeText(MainActivity.this, "正在加载优易花登录页面...", android.widget.Toast.LENGTH_SHORT).show();
            }
        });

        webView.loadUrl(fullUrl);
    }
    
    /**
     * 自定义WebViewClient
     */
    private class CustomWebViewClient extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            android.util.Log.d("MainActivity", "尝试加载URL: " + url);

            // 允许原站的所有页面在WebView中打开
            if (url.startsWith("https://dailuanshej.cn/")) {
                view.loadUrl(url);
                return true;
            }

            // 其他外部链接用系统浏览器打开
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(intent);
            return true;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            android.util.Log.d("MainActivity", "页面加载完成: " + url);

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    android.widget.Toast.makeText(MainActivity.this, "页面加载完成", android.widget.Toast.LENGTH_SHORT).show();
                }
            });
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            android.util.Log.e("MainActivity", "页面加载错误: " + description + " URL: " + failingUrl);

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    android.widget.Toast.makeText(MainActivity.this, "加载错误: " + description, android.widget.Toast.LENGTH_LONG).show();
                }
            });
        }
    }
    
    /**
     * 自定义WebChromeClient
     */
    private class CustomWebChromeClient extends WebChromeClient {
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            // 可以在这里显示加载进度
        }
    }
    
    /**
     * JavaScript接口类
     * 提供H5页面调用原生功能的接口
     */
    public class WebAppInterface {
        
        /**
         * 显示Toast消息
         */
        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    Toast.makeText(MainActivity.this, message, Toast.LENGTH_SHORT).show();
                }
            });
        }
        
        /**
         * 获取APP版本信息
         */
        @JavascriptInterface
        public String getAppVersion() {
            try {
                return getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            } catch (Exception e) {
                return "1.0.0";
            }
        }
        
        /**
         * 退出APP
         */
        @JavascriptInterface
        public void exitApp() {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    finish();
                }
            });
        }
        
        /**
         * 分享功能
         */
        @JavascriptInterface
        public void shareText(String text) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    Intent shareIntent = new Intent(Intent.ACTION_SEND);
                    shareIntent.setType("text/plain");
                    shareIntent.putExtra(Intent.EXTRA_TEXT, text);
                    startActivity(Intent.createChooser(shareIntent, "分享到"));
                }
            });
        }
    }
    
    /**
     * 处理返回键
     */
    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
    
    /**
     * 页面暂停时暂停WebView
     */
    @Override
    protected void onPause() {
        super.onPause();
        if (webView != null) {
            webView.onPause();
        }
    }
    
    /**
     * 页面恢复时恢复WebView
     */
    @Override
    protected void onResume() {
        super.onResume();
        if (webView != null) {
            webView.onResume();
        }
    }
    
    /**
     * 销毁WebView
     */
    @Override
    protected void onDestroy() {
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
