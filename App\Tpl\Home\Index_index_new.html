<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>💰 小贷系统 - 专业借贷服务平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .container {
            max-width: 414px;
            margin: 0 auto;
            padding: 0;
            min-height: 100vh;
            background: rgba(255,255,255,0.05);
            backdrop-filter: blur(10px);
            position: relative;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }

        .status-time {
            font-weight: 600;
        }

        .status-icons {
            display: flex;
            gap: 5px;
        }

        /* 头部区域 */
        .header {
            padding: 30px 20px 40px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }

        .logo {
            font-size: 48px;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
            letter-spacing: 1px;
        }

        /* 主要内容区域 */
        .main-content {
            background: white;
            border-radius: 25px 25px 0 0;
            min-height: calc(100vh - 200px);
            padding: 25px 20px 100px;
            margin-top: -20px;
            position: relative;
            box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
        }

        /* 功能菜单 */
        .menu-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .menu-item {
            display: block;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .menu-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .menu-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .menu-item:hover .menu-card {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .menu-item:hover .menu-card::before {
            opacity: 1;
        }

        .menu-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .menu-text {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            position: relative;
            z-index: 1;
        }

        /* 快速导航 */
        .quick-nav {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .quick-nav .section-title {
            color: white;
            margin-bottom: 15px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .nav-item {
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 8px;
            display: block;
        }

        .nav-item div {
            font-size: 13px;
            font-weight: 500;
        }

        /* 特色功能卡片 */
        .features {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }

        .feature-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.6;
        }

        /* 底部安全区域 */
        .bottom-safe-area {
            height: 20px;
            background: transparent;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 25px 15px 35px;
            }
            
            .app-title {
                font-size: 24px;
            }
            
            .main-content {
                padding: 20px 15px 80px;
            }
            
            .menu-grid {
                gap: 12px;
            }
            
            .menu-card {
                padding: 16px;
            }
            
            .menu-icon {
                font-size: 28px;
                margin-bottom: 10px;
            }
            
            .menu-text {
                font-size: 13px;
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            animation: fadeIn 0.6s ease forwards;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        /* 点击反馈 */
        .menu-item:active .menu-card {
            transform: scale(0.95);
        }

        .nav-item:active {
            transform: scale(0.95);
        }
    </style>
</head>

<body>
    <div class="container loading">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-time">
                <script>document.write(new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'}))</script>
            </div>
            <div class="status-icons">
                <span>📶</span>
                <span>🔋</span>
            </div>
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">💰</div>
            <h1 class="app-title">小贷系统</h1>
            <p class="app-subtitle">专业、安全、便捷的借贷服务平台</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 功能菜单 -->
            <div class="menu-section">
                <h2 class="section-title">
                    <span>📱</span>
                    功能菜单
                </h2>
                <div class="menu-grid">
                    <!-- 我的借款 -->
                    <a href="/index.php?m=Order&a=lists" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">💰</span>
                            <div class="menu-text">我的借款</div>
                        </div>
                    </a>

                    <!-- 我的还款 -->
                    <a href="/index.php?m=Order&a=bills" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">💳</span>
                            <div class="menu-text">我的还款</div>
                        </div>
                    </a>

                    <!-- 我的钱包 -->
                    <a href="/index.php?m=Qianbao&a=index" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">👛</span>
                            <div class="menu-text">我的钱包</div>
                        </div>
                    </a>

                    <!-- 我的资料 -->
                    <a href="/index.php?m=Info&a=index" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">👤</span>
                            <div class="menu-text">我的资料</div>
                        </div>
                    </a>

                    <!-- 风险测评 -->
                    <a href="/index.php?m=User&a=evaluation" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">📊</span>
                            <div class="menu-text">风险测评</div>
                        </div>
                    </a>

                    <!-- 常见问题 -->
                    <a href="/index.php?m=User&a=question" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">❓</span>
                            <div class="menu-text">常见问题</div>
                        </div>
                    </a>

                    <!-- 设置 -->
                    <a href="/index.php?m=User&a=setup" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">⚙️</span>
                            <div class="menu-text">设置</div>
                        </div>
                    </a>

                    <!-- 个人中心 -->
                    <a href="/index.php?m=User&a=index" class="menu-item">
                        <div class="menu-card">
                            <span class="menu-icon">🏠</span>
                            <div class="menu-text">个人中心</div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- 快速导航 -->
            <div class="quick-nav">
                <h2 class="section-title">
                    <span>🚀</span>
                    快速导航
                </h2>
                <div class="nav-grid">
                    <a href="/index.php?m=Index&a=index" class="nav-item">
                        <span class="nav-icon">🏠</span>
                        <div>首页</div>
                    </a>
                    <a href="/index.php?m=User&a=loanInfo" class="nav-item">
                        <span class="nav-icon">💳</span>
                        <div>借款信息</div>
                    </a>
                    <a href="/index.php?m=User&a=profile" class="nav-item">
                        <span class="nav-icon">👤</span>
                        <div>个人中心</div>
                    </a>
                    <a href="/index.php?m=User&a=logout" class="nav-item">
                        <span class="nav-icon">📤</span>
                        <div>退出</div>
                    </a>
                </div>
            </div>

            <!-- 特色功能 -->
            <div class="features">
                <div class="feature-card">
                    <span class="feature-icon">🔒</span>
                    <div class="feature-title">安全可靠</div>
                    <div class="feature-desc">采用银行级安全标准，多重加密保护，确保您的资金和信息安全</div>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <div class="feature-title">便捷服务</div>
                    <div class="feature-desc">智能处理系统，便捷服务流程，高效业务办理，满足您的各种需求</div>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">💎</span>
                    <div class="feature-title">灵活还款</div>
                    <div class="feature-desc">多种还款方式，灵活的还款期限，让您轻松管理财务，无压力还款</div>
                </div>
            </div>

            <!-- 底部安全区域 -->
            <div class="bottom-safe-area"></div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.classList.add('loading');

            // 添加点击反馈
            const menuItems = document.querySelectorAll('.menu-item, .nav-item');
            menuItems.forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                item.addEventListener('touchend', function() {
                    this.style.transform = '';
                });
            });
        });
    </script>
</body>
</html>
