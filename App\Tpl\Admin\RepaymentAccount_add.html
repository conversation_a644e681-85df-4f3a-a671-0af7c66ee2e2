<div class="layui-card">
    <div class="layui-card-header">
        <h3>添加还款账户</h3>
    </div>
    <div class="layui-card-body">
        <form action="{:U(GROUP_NAME.'/RepaymentAccount/add')}" method="post" enctype="multipart/form-data" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">账户类型</label>
                <div class="layui-input-block">
                    <input type="radio" name="account_type" value="1" title="对公账户" checked>
                    <input type="radio" name="account_type" value="2" title="对私账户">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">账户名称</label>
                <div class="layui-input-block">
                    <input type="text" name="account_name" required lay-verify="required" placeholder="请输入账户名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">银行名称</label>
                <div class="layui-input-block">
                    <input type="text" name="bank_name" required lay-verify="required" placeholder="请输入银行名称" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">账户号码</label>
                <div class="layui-input-block">
                    <input type="text" name="account_number" required lay-verify="required" placeholder="请输入账户号码" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">开户人/单位</label>
                <div class="layui-input-block">
                    <input type="text" name="account_holder" required lay-verify="required" placeholder="请输入开户人或开户单位" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">二维码类型</label>
                <div class="layui-input-block">
                    <select name="qr_code_type" class="layui-select">
                        <option value="">请选择二维码类型</option>
                        <option value="wechat">微信支付</option>
                        <option value="alipay">支付宝</option>
                        <option value="bank">银行转账</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">上传二维码</label>
                <div class="layui-input-block">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn" id="uploadBtn">选择图片</button>
                        <input type="file" name="qr_code_image" accept="image/*" style="display: none;" id="qrCodeFile">
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="qrPreview" style="width: 200px; height: 200px; margin-top: 10px; display: none;">
                            <p id="qrText" style="margin-top: 10px; color: #999;">支持jpg、jpeg、png、gif格式，大小不超过2MB</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">排序顺序</label>
                <div class="layui-input-block">
                    <input type="number" name="sort_order" value="0" placeholder="数字越小排序越靠前" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">是否启用</label>
                <div class="layui-input-block">
                    <input type="radio" name="is_active" value="1" title="启用" checked>
                    <input type="radio" name="is_active" value="0" title="禁用">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">账户描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入账户描述信息" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="formSubmit">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    <a href="{:U(GROUP_NAME.'/RepaymentAccount/index')}" class="layui-btn layui-btn-normal">返回列表</a>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
layui.use(['form', 'upload'], function(){
    var form = layui.form;
    var upload = layui.upload;

    // 文件选择
    document.getElementById('uploadBtn').onclick = function() {
        document.getElementById('qrCodeFile').click();
    };

    // 文件预览
    document.getElementById('qrCodeFile').onchange = function(e) {
        var file = e.target.files[0];
        if (file) {
            // 检查文件类型
            if (!file.type.match('image.*')) {
                layer.msg('请选择图片文件！', {icon: 2});
                return;
            }
            
            // 检查文件大小
            if (file.size > 2 * 1024 * 1024) {
                layer.msg('图片大小不能超过2MB！', {icon: 2});
                return;
            }

            var reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('qrPreview').src = e.target.result;
                document.getElementById('qrPreview').style.display = 'block';
                document.getElementById('qrText').innerHTML = '已选择：' + file.name;
            };
            reader.readAsDataURL(file);
        }
    };

    // 表单提交
    form.on('submit(formSubmit)', function(data) {
        // 这里可以添加额外的验证逻辑
        return true;
    });
});
</script>

<style>
    .layui-card {
        margin: 20px;
    }
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
    .layui-upload-img {
        border: 1px solid #e6e6e6;
        border-radius: 4px;
    }
</style>
