<?php
class DaikuanAction extends CommonAction{


    //借款列表
    public function index(){
        $this->title = "借款列表";
        $keyword = I("keyword",'','trim');
        $this->keyword = $keyword;
        $where = array();
        if($keyword){
            $where['user'] = $keyword;
        }

        


        $Order = D("order");
        import('ORG.Util.Page');
        $count = $Order->where($where)->count();
        $Page  = new Page($count,20);
        $Page->setConfig('theme','共%totalRow%条记录 | 第 %nowPage% / %totalPage% 页 %upPage%  %linkPage%  %downPage%');
        $show  = $Page->show();
        $list = $Order->where($where)->order('addtime Desc')->limit($Page->firstRow.','.$Page->listRows)->select();
        $configModel = D('config');
        $config = $configModel->where(array('id'=>1))->find();

        $userinfoModel = D('userinfo');
        $info = $userinfoModel->select();
		// var_dump($list);
        $userModel = D('user');
        $user = $userModel->select();

        $this->assign('config', $config);
        $this->assign('info', $info);
        $this->assign('user', $user);
        $this->list = $list;
        $this->page = $show;
        $this->display();
    }

    //修改订单状态
    public function changestatus(){
        $id = I("id",0,'trim');
        $status = I("status",'','trim');
        $Order = D("order");
        $tel = $Order->where(array('id' => $id))->find();
        //$month = I("months",'','trim');
        //var_dump($tel['user']);exit();

        $data = array('status' => 0,'msg' => '未知错误');
        if(!$id || $status == ''){
            $data['msg'] = "参数错误!";
        }else{
            $Order = D("order");
            $count = $Order->where(array('id' => $id))->count();
            if(!$count){
                $data['msg'] = "订单不存在!";
            }else{
                $tixian = I("tixian",'','trim');
                $miaosu=I('name');
                $status1 = $Order->where(array('id' => $id))->save(array('status' => $status));
                $Order->where(array('id' => $id))->save(array('name' => $miaosu));
                $tixian = $Order->where(array('id' => $id))->save(array('tixian' => $tixian));

                if(!$status1){
                    $data['msg'] = "操作失败!";
                }else{

                    //审核通过发送短信内容
                    if($status == 3){
                        $User = D("user");
                        $zhuanghu = $User->where(array('phone' => $tel['user']))->find();
                        $save['zhanghuyue'] = $zhuanghu['zhanghuyue'] + $tel['money'];
                        $res = $User->where(array('id' => $zhuanghu['id']))->save($save);
                        $content = "恭喜您的订单".$tel['ordernum']."审.核已通.过，请登入APP查看详情联系客户专员";
                    }
                    //银行卡异常发送短信内容
                    if($status == 9){
                        $content = "您的资料填写有误，请进入APP核对，详情咨询您的客户专员";
                    }
                    //冻结发送短信内容
                    if($status == 6){
                        $content = "您的订单提现额度已临时冻结，详情咨询您的客户专员";
                    }
                    //退款审核中发送短信内容
                    if($status == 12){
                        $content = "您的退款订单已提交审.核，请登入APP查看最新进度详情";
                    }
                    $status_array = array( "3","9","6","12");
                    if(in_array($status, $status_array)){
                        // $content = '【'.C('cfg_sms_name').'】'. $content ;
                        // $smsapi = C('cfg_SMS_API'); //短信网关
                        // $user = C('cfg_SMS_USER'); //短信平台帐号
                        // $pass = C('cfg_sms_pass'); //短信平台密码
                        // $uid = C('cfg_sms_uid');
                        // $sendurl = $smsapi."&userid=".$uid."&account=".$user."&password=".$pass."&mobile=".$tel['user']."&content=".$content."&sendTime=&taskName=&checkcontent=1&mobilenumber=1&countnumber=1&telephonenumber=0";
						$status = sendTsms($tel['user'], $content);
                        // $result =file_get_contents($sendurl) ;
                    }
                    $data['status'] = 1;
                }
            }
        }
        $this->ajaxReturn($data);
    }

    //删除订单
    public function del(){
        $this->title='删除订单';
        $id = I('id',0,'trim');
        if(!$id){
            $this->error("参数有误!");
        }
        $Order = D("order");
        $status = $Order->where(array('id' => $id))->delete();
        if(!$status){
            $this->error("删除失败!");
        }
        $this->success("删除订单成功!");
    }
    
    public function delall(){
    	$id = I("id",'','trim');
    	if (!$id) {
            $this->error('非法进入');
        }
        $result = D('order')->where(['id'=>['in',$id]])->delete();
        // var_dump(D('user')->getLastSql());
        // exit();
        if($result){
        	$this->success('删除成功');
        }
    }
    
    

    public function hetong(){
        $id = I('id',0,'trim');
        $Order = D("order");
        $status = $Order->where(array('id' => $id))->find();
        //通过订单的电话号码查出用户的资料信息
        $User = D("userinfo");
        $user = $User ->where(array('user'=>$status['user']))->find();
        $this->user = $user;
        $this->data = $status;
        $this->display();
    }

    /**
     * 修改预设订单状态
     */
    public function savedbtstrs(){
        $strs = I('strs');
        if(!$strs){
            $this->error('请输入预设订单状态');
        }
        $configModel = D('config');
        $r = $configModel->where(array('id' => 1))->save(array('status' => $strs));
        if (!$r) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }

    /**
     * 修改预设订单状态描述
     */
    public function savestrs(){
        $strs = I('strs');
        if(!$strs){
            $this->error('请输入预设订单状态描述');
        }
        $configModel = D('config');
        $r = $configModel->where(array('id' => 1))->save(array('des' => $strs));
        if (!$r) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }


    /**
     * 修改预设保险费率
     */
    public function savelix(){
        $strs = I('strs');
        if(!$strs){
            $this->error('请输入预设保险');
        }
        $configModel = D('config');
        $r = $configModel->where(array('id' => 1))->save(array('insurance' => $strs));
        if (!$r) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }

    /**
     * 修改预设保险费率
     */
    public function saveypass(){
        $strs = I('strs');
        if(!$strs){
            $this->error('请输入提现密码');
        }
        $configModel = D('config');
        $r = $configModel->where(array('id' => 1))->save(array('pass' => $strs));
        if (!$r) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }
    /**
     *修改银行卡信息
     */
    public function evbank(){
        $uid = I('oid');
        if ($this->isPost()) {
            $bankNum = I('banknum');
            $bankName = I('bankname');
            if (!$bankNum) {
                $this->error('请输入银行卡号');
            }

            $loanorderModel = D('order');
            $data = array('bank' => $bankName, 'banknum' => $bankNum);
            $res = $loanorderModel->where(array('uid' => $uid))->save($data);

            $infoModel = D('userinfo');
            $data1 = array('bankname' => $bankName, 'bankcard' => $bankNum);
            $info = $infoModel ->where(array('user'=>$uid))->save($data1);

            if (!$res) {
                $this->error('信息保存失败');
            }
            $this->success('保存成功');
        }
    }

    /**
     *修改订单状态
     */

    public function savexgo(){
        if ($this->isPost()) {
            $color = I('color');
            $dbt = I('dbt');
            $dstatus=I('dstatus');
            $xsm = I('xsm');
            $content = I('sms');
            $id = I('id');
            $xbzmark = I('xbzmark');
            if(!$color){
                $color = "E53333";
            }
            if(!$dbt){
                $this->error('请输入订单状态！');
            }
            if (!$xsm) {
                $this->error('请输入订单说明！');
            }
            $loanorderModel = D('order');
            $name = $loanorderModel->where(array('id'=>$id))->find();
            $data = array('color' => $color, 'pending' => $dbt ,'status'=> $dstatus,'error'=>$xsm , 'pending_time'=>time());
            $res = $loanorderModel->where(array('id' => $id))->save($data);
            if (!$res) {
                $this->error('信息保存失败');
            }
            //echo $dbt;exit();
            if($dstatus=="3"){
                //通过uid去user表查找对应的用户
                $User = D("user");
                $zhuanghu = $User->where(array('phone' => $name['user']))->find();
                $save['zhanghuyue'] = $zhuanghu['zhanghuyue'] + $name['money'];
                $User->where(array('id' => $zhuanghu['id']))->save($save);
            }
            if($dstatus=="21"){
                //通过uid去user表查找对应的用户
                $d = M('voucher')->where(array('ordernum' => $name['ordernum']))->count();
                if($d){
                    $this->error('分期还款已生成,请不要重复提交生成分期还款订单！');
                }
                $time = date('Y-m-d H:i:s');
                $voucher = [
                    'user'=>$name['user'],
                    'ordernum'=>$name['ordernum'],
                    'money'=>$name['money'],
                    'months'=>$name['months'],
                    //	'ofnumber'=>1,//当前期数
                    'monthmoney'=>$name['monthmoney'],
                    'addtime'=>$time,
                    //'huantime'=>date('Y-m-d H:i:s',strtotime('+1month')),
                ];
                for ($i = 1; $i <= $name['months']; $i++) {
                    $voucher['ofnumber'] = $i;
                    $voucher['huantime'] = date('Y-m-d',strtotime("$time +$i month"));
                    $dataList[] = $voucher;
                }
                M('voucher')->addALL($dataList);
                $loanorderModel->where(array('id' => $id))->save(array('status'=>50));   //标记为已生成账单
            }

            if($content){

                $User = D('userinfo');
                $info = $User ->where(array('user'=>$name['user'])) -> find();

                //$content = '【' . C('sms_name') . '】' . $content;
                $content = str_replace('@sitename@', C('siteName'), $content);
                $content = str_replace('《@sitename@》', C('siteName'), $content);
                $content = str_replace('@username@', $info['name'], $content);
                $status = sendTsms($info['user'], $content);
                // exit();
                // $content = '【'.C('cfg_sms_name').'】'. $content ;
                // $smsapi = C('cfg_SMS_API'); //短信网关
                // $user = C('cfg_SMS_USER'); //短信平台帐号
                // $pass = C('cfg_sms_pass'); //短信平台密码
                // $uid = C('cfg_sms_uid');
                // $sendurl = $smsapi."&userid=".$uid."&account=".$user."&password=".$pass."&mobile=".$info['user']."&content=".$content."&sendTime=&taskName=&checkcontent=1&mobilenumber=1&countnumber=1&telephonenumber=0";

                // $result =file_get_contents($sendurl) ;

            }
            // exit();
            $this->success('保存成功');

        }
    }
    /**
     * 修改预设保险费率
     */
    public function savevic(){
        $vicsv = I('vicsv');
        $oid = I('oid');
        $loanorderModel = D('order');
        $data = array('shuoming' => $vicsv);
        $res = $loanorderModel->where(array('user' => $oid))->save($data);
        if (!$res) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }
    /**
     * 修改借款月份
     */
    public function savemonth(){
        $month = I('month');
        $id = I('id');
        $loanorderModel = D('loanorder');
        $data = array('time' => $month);
        $res = $loanorderModel->where(array('id' => $id))->save($data);
        if (!$res) {
            $this->error('修改失败');
        }
        $this->success('修改成功');
    }

    //转账截图

    public function viewhdo(){
        $id = I('id');
        if (!$id) {
            $this->redirect('Daikuan/index');
        }
        $configModel = D('config');
        $config = $configModel->where(array('id'=>1))->find();
        $this->assign('config', $config);


        $loanorderModel = D('order');
        $order = $loanorderModel->where(array('id'=>$id))->find();
        $this->assign('order', $order);

        $userinfoModel = D('userinfo');
        $info = $userinfoModel->where(array('user'=>$order['user']))->find();
        $this->assign('info', $info);


        $this->display();
    }

    //转账截图

    public function viewhd(){
        $id = I('id');
        if (!$id) {
            $this->redirect('Daikuan/index');
        }
        $loanorderModel = D('order');
        $order = $loanorderModel->where(array('id'=>$id))->find();
        $this->assign('order', $order);

        $userinfoModel = D('userinfo');
        $info = $userinfoModel->where(array('user'=>$order['user']))->find();
        $this->assign('info', $info);


        $this->display();
    }

    //修改支付状态
    public function changepaystatus(){
        $id = I("id",0,'trim');
        $status = I("status",'','trim');
        $type = I("type",'','trim');
        $ordernum = I("ordernum",'','trim');
        $data = array('status' => 0,'msg' => '未知错误');
        if(!$id || $status == ''){
            $data['msg'] = "参数错误!";
        }else{
            $count = M('voucher')->where(array('id' => $id))->find();
            if(!$count){
                $data['msg'] = "还款订单不存在!";
            }else{
                if($type==2){
                    $res = M('order')->where(array('ordernum' => $ordernum))->save(array('status' => 16));
                }
                $res = M('voucher')->where(array('id' => $id))->save(array('status' => $status));
                if(!$res){
                    $data['msg'] = "操作失败!";
                }else{
                    $data['status'] = 1;
                    $data['msg'] = "设置成功!";
                }
            }
        }
        $this->ajaxReturn($data);
    }

    //修该日期
    public function savedate(){
        $id = I("id",0,'trim');
        $huantime = I("huantime",'','trim');

        $data = array('status' => 0,'msg' => '未知错误');
        if(!$id || !$huantime){
            $data['msg'] = "参数错误!";
        }else{
            $voucher = D("voucher");
            $count = $voucher->where(array('id' => $id,'huantime'=>$huantime))->count();
            if($count){
                $data['msg'] = "还款日期不能重复!";
            }else{
                $status = $voucher->where(array('id' => $id))->save(array('huantime' => $huantime));
                if(!$status){
                    $data['msg'] = "操作失败!";
                }else{
                    $data['status'] = 1;
                }
            }
        }
        $this->ajaxReturn($data);
    }

    public function editmoney() {
        $data = array('status' => 0,'msg' => '未知错误');
        $id = I('id');
        $money = I('money');
        $feilv = C('cfg_fuwufei');
        $feilv = explode(',',$feilv);
        $Order = D('order');
        // $feilv=str_split($feilv);
        if($money == '' || $id == '' || !$money || !$id){
            $data['msg'] = '禁止为空';
            $data['status'] = 0;
        }else{
            $order = $Order->where(array('id'=>$id))->find();
            $fuwufei = $feilv[($order['months']-1)] * $money / 100;
            $huankuan = ceil((float)($money/$order['months']));
            $monthmoney = ceil($huankuan+$fuwufei);
            $res = $Order->where(array('id'=>$id))->save(array('money'=>$money,'monthmoney'=>$monthmoney));
            if(!$res){
                $data['msg'] = '修改失败';
                $data['status'] = 0;
                exit();
            }else{
                $data['msg'] = '修改成功';
                $data['status'] = 1;
            }
        }
        
        $this->ajaxReturn($data);
        exit();

    }

    public function editmonths() {
        $data = array('status' => 0,'msg' => '未知错误');
        $id = I('id');
        $months = I('months');
        $feilv = C('cfg_fuwufei');
        $feilv = explode(',',$feilv);
        $Order = D('order');
        // $feilv=str_split($feilv);
        if($months == '' || $id == '' || !$months || !$id){
            $data['msg'] = '禁止为空';
            $data['status'] = 0;
        }else{
            $order = $Order->where(array('id'=>$id))->find();
            $fuwufei = $feilv[($months-1)] * $order['money'] / 100;
            $huankuan = ceil((float)($order['money']/$months));
            $monthmoney = ceil($huankuan+$fuwufei);
            $res = $Order->where(array('id'=>$id))->save(array('months'=>$months,'monthmoney'=>$monthmoney));
            if(!$res){
                $data['msg'] = '修改失败';
                $data['status'] = 0;
            }else{
                $data['msg'] = '修改成功';
                $data['status'] = 1;
            }
        }
        
        $this->ajaxReturn($data);
    }
    // 合同
    public function contract() {
    	$Contract = D('contract');
    	if ($this->isPost()) {
			$arr = I('content');
			$result = $Contract->where(array('id'=>'1'))->save(array('contract'=>$arr));
			if (!$result) {
				$this->error('保存失败!');
			}
			$this->success('保存成功!');
		}else{
			$data = $Contract->where(array('id'=>1))->find();
			if($data){
				$this->assign('data', $data['contract']);
			}
			$this->display();
		}
		
    }

}
