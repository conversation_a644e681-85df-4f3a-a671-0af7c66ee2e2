<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #007cba;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .message-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .message-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007cba;
        }
        
        .message-meta {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .meta-item:last-child {
            margin-bottom: 0;
        }
        
        .meta-label {
            font-weight: bold;
            color: #666;
        }
        
        .meta-value {
            color: #333;
        }
        
        .message-content {
            line-height: 1.8;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            white-space: pre-wrap;
        }
        
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 15px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.3);
            text-decoration: none;
        }
        
        .actions {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007cba;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            text-decoration: none;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-read {
            background: #d4edda;
            color: #155724;
        }
        
        .status-unread {
            background: #cce7ff;
            color: #004085;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-links a {
                margin: 5px 0;
            }
            
            .meta-item {
                flex-direction: column;
                gap: 5px;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .btn {
                margin: 5px 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航 -->
        <div class="nav-links">
            <a href="{:U('Index/index')}">🏠 首页</a>
            <a href="{:U('User/loanInfo')}">💳 借款信息</a>
            <a href="{:U('User/messages')}">📧 我的消息</a>
            <a href="{:U('User/profile')}">👤 个人资料</a>
            <a href="{:U('User/logout')}">🚪 退出</a>
        </div>
        
        <!-- 页面标题 -->
        <div class="header">
            <h1>📧 消息详情</h1>
            <p>查看消息的详细内容</p>
        </div>
        
        <!-- 消息详情 -->
        <div class="message-card">
            <div class="message-title">
                📄 {$messageInfo.title}
            </div>
            
            <div class="message-meta">
                <div class="meta-item">
                    <span class="meta-label">📅 发送时间</span>
                    <span class="meta-value">{$messageInfo.created_time_format}</span>
                </div>
                
                <div class="meta-item">
                    <span class="meta-label">📊 消息类型</span>
                    <span class="meta-value">
                        <if condition="$messageInfo.message_type eq 1">
                            📢 系统通知
                        <elseif condition="$messageInfo.message_type eq 2"/>
                            💰 借款相关
                        <elseif condition="$messageInfo.message_type eq 3"/>
                            ⚠️ 重要提醒
                        <else/>
                            📧 普通消息
                        </if>
                    </span>
                </div>
                
                <div class="meta-item">
                    <span class="meta-label">👁️ 阅读状态</span>
                    <span class="meta-value">
                        <if condition="$messageInfo.is_read eq 1">
                            <span class="status-badge status-read">✅ 已读</span>
                        <else/>
                            <span class="status-badge status-unread">🔵 未读</span>
                        </if>
                    </span>
                </div>
                
                <if condition="$messageInfo.read_time_format">
                    <div class="meta-item">
                        <span class="meta-label">👀 阅读时间</span>
                        <span class="meta-value">{$messageInfo.read_time_format}</span>
                    </div>
                </if>
            </div>
            
            <div class="message-content">
                {$messageInfo.content}
            </div>
            
            <div class="actions">
                <a href="javascript:history.back()" class="btn btn-secondary">
                    ← 返回消息列表
                </a>
                <a href="{:U('User/messages')}" class="btn btn-primary">
                    📧 查看所有消息
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.message-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
