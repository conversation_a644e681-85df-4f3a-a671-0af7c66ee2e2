<!DOCTYPE html>
<html class="pixel-ratio-1"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>订单支付系统 - 站长源码库（zzmaku.com） </title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/sm.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/common.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/sm-extend.min.css">
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/home/<USER>/newindex-09d04b32f3.css">
	<style type="text/css">
		.btn00{
			width: 200px;height: 40px;background: #36648B ;border: 1px solid #eee;border-radius: 15px;color: #fff}
	</style>
</head>
<body>
<div class="comm_top_nav">
	<div class="am-g">
		<b>
			<div class="am-u-sm-2" onclick="javascript:window.location.href='{:U('Order/billinfo')}'"><i
					class="am-icon-angle-left am-icon-fw"></i></div>
			<div class="am-u-sm-8">收银台</div>
			<div class="am-u-sm-2"></div>
		</b>
	</div>
</div>
<div class="content">
	<!--订单信息-->
	<php>
		$stat=array("","季息","年息");
	</php>
	<div class="content-block-title">确认订单信息</div>
	<div class="card">
		<div class="card-header">
			单号:{$data.ordernum}	<span>{$stat[$data['lixi']]}:￥{$data['money']}元</span>
		</div>
		<div class="card-content">
			<div class="card-content-inner">
				说明:<span style="color:red">{$msg}</span>
			</div>
		</div>
	</div>
	<!--订单信息-->

	<!--支付方式-->
	<div class="content-block-title">选择支付方式</div>
	<div class="list-block inset">
		<ul>

			<li>
				<a href="javascript:code2();" class="item-link list-button">
					银行卡转帐
				</a>
			</li>
			<li>
				<a href="javascript:code();" class="item-link list-button">
					微信转帐
				</a>
			</li>
			<li>
				<a href="{:U('Order/voucher',array('type'=>$data['type'],'id'=>$data['id'],'ordernum'=>$data['ordernum']))}" class="item-link list-button">
					我已验证,上传支付凭证
				</a>
			</li>
		</ul>
	</div>
	<!-- <div class="content-block-title">收款二维码</div>
    <div class="list-block inset">
        <div style="text-align: center;"><img src="http://yrd.s1xu4w.cn./Upload/code/qr_code4534.jpg" style="width:55%;"></div>
    </div> -->
	<!--支付方式-->
	<!--         <div style="text-align: center;">
        <input type="button" value="我已还款" class="btn00" onclick="ispay()">
    </div>
     -->
	<div>

	</div>
</div>



<div id="loadDiv" style="display: none;">
	<div class="modal-overlay modal-overlay-visible"></div>
	<div class="modal  modal-no-buttons modal-in" style="display: block;top: 40%;">
		<div class="modal-inner">
			<div class="modal-title">加载中</div>
			<div class="modal-text">
				<div class="preloader"></div>
			</div>
		</div>
		<div class="modal-buttons "></div>
	</div>
</div>

<div class="emask" id="mask3" style="display: none;"></div>
<div class="deowin2" style="display:none;text-align: center; position: absolute; top:25%;left: 4.9%;width: 90%;padding: 0;" id="deowin31">
	<div class="deocon2">
		<div class="divpad2" style="text-align:center;height:80%;font-size: 16px">
			<div>请扫码支付或者是保存二维码扫码支付</div>
			<!--  <div>点击下方的按钮及联系人工客服</div> -->
			<div><img src="{:C('cfg_weixin')}" style="width:100%;"></div>
		</div>
		<div class="wobtn" style="display: none;">
			<!-- 一个按钮用这个结构 -->
			<a id="winbtn3" href="javascript:;">确定</a>
		</div>
	</div>
</div>

<div class="deowin2" style="display:none;text-align: center; position: absolute; top:25%;left: 4.9%;width: 90%;padding: 0;" id="deowin32">
	<div class="deocon2">
		<div class="divpad2" style="text-align:center;height:80%;font-size: 16px">

			<!--  <div>点击下方的按钮及联系人工客服</div> -->
			<div class="list-block inset">
				<ul>
					<li>
                                    <span class="item-link list-button">
                                        开户行:{:C('cfg_bank_name')}
                                    </span>
					</li>
					<li>
                                    <span class="item-link list-button">
                                        户名:{:C('cfg_bank_names')}
                                    </span>
					</li>
					<li>
                                   <span class="item-link list-button">
                                        卡号:<b id="numss">{:C('cfg_bank_num')}</span>
						</span>
					</li>

				</ul>
			</div>

		</div>
	</div>
	<div class="wobtn" style="display: none;">
		<!-- 一个按钮用这个结构 -->
		<a id="winbtn3" href="javascript:;">确定</a>
	</div>
</div>
</div>
<div class="modal toast modal-in" id="msgDiv" style="display: none; left: 70%; top: 50%;"></div>
<script type="text/javascript" src="__PUBLIC__/home/<USER>/zepto.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__PUBLIC__/home/<USER>/sm.min.js" charset="utf-8"></script>
<script type="text/javascript" src="__PUBLIC__/home/<USER>/sm-extend.min.js" charset="utf-8"></script>
<script src="__PUBLIC__/home/<USER>/jquery.min.js"></script>
<script src="__PUBLIC__/layer/layer.js"></script>

<script>
	var ispost = 0;
	$(function(){
		$('#mask3').click(function(){
			$('#deowin31').hide();
			$('#mask3').hide();
			$('#deowin32').hide();
		});

	});
	function jsCopy(){
		var Url2=document.getElementById("numss").innerText;
		var oInput = document.createElement('input');
		oInput.value = Url2;
		document.body.appendChild(oInput);
		oInput.select(); // 选择对象
		document.execCommand("Copy"); // 执行浏览器复制命令
		oInput.className = 'oInput';
		oInput.style.display='none';
		layer.msg('复制成功');
	}
	function showMsg(msg){
		$("#msgDiv").html(msg);
		$("#msgDiv").show();
		setTimeout(function(){
			$("#msgDiv").hide();
		},2000);
	}
	function beginPay(msg){
		ispost = 1;
		$(".modal-title").html(msg);
		$("#loadDiv").show();
	}
	function endPay(){
		ispost = 0;
		$("#loadDiv").hide();
	}
	function pay(pay_channel){
		if(ispost == 1){
			showMsg("页面正在加载,请稍后...");
		}else{
			beginPay("正在请求支付");
			$.post(
					"/index.php?g=Pay&m=Index&a=geturl",
					{
						channel:pay_channel,
						ordernum:"H725013363129890"
					},
					function(data,state){
						if(state != "success"){
							endPay();
							showMsg("请求出错!");
						}else if(data.status == 1){
							console.log('success');
							setTimeout(function(){
								endPay();
								window.location.href = data.url;
							},1000);
						}else{
							endPay();
							showMsg(data.msg);
						}
					}
			);
		}
	}

	function ispay(){
		$.post(
				"/index.php?g=Pay&m=Index&a=ispay",
				{
					ordernum:"H725013363129890",
				},
				function($data){
					if($data.status == 1){
						// showMsg("等待入账中");
						// $('.btn00').val('等待入账中');
						// $('.btn00').attr('onclick','');
						// $('.btn00').css('background-color','#BFBFBF');
						window.location.href = "/index.php?g=Home&m=Order&a=bills";
					}else{
						showMsg("请重新进入该页面");
					}
				}
		);
	}

	function nopay(){
		window.location.href = "/index.php?g=Home&m=Order&a=bills";
	}


	function code(){
		//   $("#deowin31").show();
		// $('#mask3').show();
		layer.open({
			type: 1,
			shadeClose: true, //点击遮罩区域是否关闭页面
			shade: 0.8,  //遮罩透明度
			maxmin:true,
			skin: 'layui-layer-lan', //加上边框
			area: ['300px', '500px'], //宽高
			title:'扫码支付',
			content: $("#deowin31").html()
		});
	}

	function code2(){
		layer.open({
			type: 1,
			shadeClose: true, //点击遮罩区域是否关闭页面
			skin: 'layui-layer-lan', //加上边框
			area: ['300px', '380px'], //宽高
			title:'银行卡转账',
			content: $("#deowin32").html()
		});


		//$("#deowin32").show();
		// $('#mask3').show();
	}
</script>

</body></html>