<?php
/**
 * 获取客户还款账号数据API
 * 用于AJAX请求获取特定客户的还款账号信息
 */

header('Content-Type: application/json; charset=utf-8');

require_once 'error_handler.php';

// 检查请求参数
if (!isset($_GET['customer_id']) || !is_numeric($_GET['customer_id'])) {
    echo json_encode(array(
        'success' => false,
        'message' => '客户ID参数无效'
    ));
    exit;
}

$customer_id = intval($_GET['customer_id']);

// 还款账号管理类
class PaymentAccountManager {
    private $data_dir = 'payment_data/';
    
    public function __construct() {
        if (!file_exists($this->data_dir)) {
            mkdir($this->data_dir, 0755, true);
        }
    }
    
    // 获取客户的还款账号信息
    public function getCustomerPaymentAccounts($customer_id) {
        $filename = $this->data_dir . "customer_{$customer_id}_payment.json";
        if (file_exists($filename)) {
            $json = file_get_contents($filename);
            $data = json_decode($json, true);
            if ($data) {
                logInfo("成功获取客户还款账号数据", ['customer_id' => $customer_id]);
                return $data;
            }
        }
        
        logInfo("使用默认还款账号数据", ['customer_id' => $customer_id]);
        return $this->getDefaultPaymentData();
    }
    
    // 获取默认还款数据
    private function getDefaultPaymentData() {
        return array(
            'personal_account' => array(
                'account_name' => '......',
                'account_number' => '......',
                'bank_name' => '其他银行',
                'account_type' => '个人账户',
                'reminder' => '该用户已经严重逾期还款，请催收人员加紧催收必要上报个人征信中心。\n优易花官方指定还款账户，仅支持手机银行转账还款或云闪付支付还款。'
            ),
            'qr_codes' => array(
                'wechat_qr' => '',
                'alipay_qr' => '',
                'status' => 'hidden' // 默认隐藏
            ),
            'updated_time' => date('Y-m-d H:i:s'),
            'customer_id' => 0
        );
    }
}

try {
    $paymentManager = new PaymentAccountManager();
    $paymentData = $paymentManager->getCustomerPaymentAccounts($customer_id);
    
    echo json_encode(array(
        'success' => true,
        'message' => '获取客户还款账号数据成功',
        'data' => $paymentData,
        'customer_id' => $customer_id
    ), JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    logError("获取客户还款账号数据失败", array(
        'customer_id' => $customer_id,
        'error' => $e->getMessage()
    ));

    echo json_encode(array(
        'success' => false,
        'message' => '获取客户还款账号数据失败: ' . $e->getMessage(),
        'customer_id' => $customer_id
    ), JSON_UNESCAPED_UNICODE);
}
?>
