<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>个人中心 - 小贷助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
        }
        
        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* APP状态栏 */
        .app-status-bar {
            height: 44px;
            background: rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .back-btn {
            color: white;
            text-decoration: none;
            font-size: 18px;
        }
        
        /* APP头部 */
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            color: white;
            text-align: center;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px 20px;
            color: white;
            text-align: center;
        }

        .header h1 {
            color: white;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        /* APP主体内容 */
        .app-content {
            background: #f5f5f5;
            min-height: calc(100vh - 44px);
            border-radius: 20px 20px 0 0;
            margin-top: -20px;
            position: relative;
            z-index: 10;
            padding: 20px 15px;
        }

        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        
        .profile-info {
            display: grid;
            gap: 15px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 15px 10px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .nav-bar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            margin-bottom: 20px;
            border-radius: 15px;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-link {
            color: #007cba;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: #007cba;
            color: white;
            text-decoration: none;
        }
        
        .nav-link.active {
            background: #007cba;
            color: white;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
            margin: 0 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007cba, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,186,0.4);
            color: white;
            text-decoration: none;
        }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 10px 0 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #667eea;
        }

        .nav-item:hover, .nav-item:active {
            color: #667eea;
            text-decoration: none;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0;
            }

            .app-content {
                padding: 15px 10px;
            }

            .stats-grid {
                gap: 8px;
            }

            .stat-card {
                padding: 12px 8px;
            }

            .stat-number {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- APP状态栏 -->
        <div class="app-status-bar">
            <a href="{:U('Index/index')}" class="back-btn">← 返回</a>
            <span>👤 个人中心</span>
            <span></span>
        </div>

        <!-- APP头部 -->
        <div class="header">
            <div class="profile-avatar">👤</div>
            <h1>{$userInfo.customer_name|default='用户'}</h1>
            <p>{$userInfo.phone}</p>
        </div>

        <!-- APP主体内容 -->
        <div class="app-content">
        
            <!-- 统计卡片 -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number">{$loan_stats.total_loans|default=0}</div>
                    <div class="stat-label">总借款笔数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">¥{$loan_stats.total_amount|default=0}</div>
                    <div class="stat-label">累计金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{$loan_stats.normal_loans|default=0}</div>
                    <div class="stat-label">进行中</div>
                </div>
            </div>
        
            <!-- 个人信息 -->
            <div class="profile-card fade-in">
                <h3 style="margin-bottom: 15px; color: #333; display: flex; align-items: center;">
                    <span style="width: 4px; height: 18px; background: linear-gradient(135deg, #667eea, #764ba2); margin-right: 10px; border-radius: 2px;"></span>
                    📋 个人信息
                </h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">📱 手机号码</span>
                        <span class="info-value">{$user_info.phone}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">👤 用户名</span>
                        <span class="info-value">{$user_info.username}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">📅 注册时间</span>
                        <span class="info-value">{$user_info.regtime|date='Y-m-d',###}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">🔐 账户状态</span>
                        <span class="info-value" style="color: #28a745;">✅ 正常</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">💎 会员等级</span>
                        <span class="info-value">普通会员</span>
                    </div>
                </div>
            </div>
        
            <!-- 快捷操作 -->
            <div class="profile-card fade-in">
                <h3 style="margin-bottom: 15px; color: #333; display: flex; align-items: center;">
                    <span style="width: 4px; height: 18px; background: linear-gradient(135deg, #667eea, #764ba2); margin-right: 10px; border-radius: 2px;"></span>
                    ⚡ 快捷操作
                </h3>
                <a href="{:U('User/loanInfo')}" class="btn btn-primary" style="width: 100%; margin: 0;">
                    📊 查看借款信息
                </a>
            </div>

            <!-- 底部安全区域 -->
            <div style="height: 80px;"></div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="{:U('Index/index')}" class="nav-item">
                    <span class="nav-icon">🏠</span>
                    <span>首页</span>
                </a>
                <a href="{:U('User/loanInfo')}" class="nav-item">
                    <span class="nav-icon">💳</span>
                    <span>借款信息</span>
                </a>
                <a href="{:U('User/profile')}" class="nav-item active">
                    <span class="nav-icon">👤</span>
                    <span>个人中心</span>
                </a>
                <a href="{:U('User/logout')}" class="nav-item">
                    <span class="nav-icon">🚪</span>
                    <span>退出</span>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.5s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 添加触摸反馈
        document.addEventListener('touchstart', function(e) {
            if (e.target.classList.contains('btn') ||
                e.target.classList.contains('nav-item')) {
                e.target.style.opacity = '0.7';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.classList.contains('btn') ||
                e.target.classList.contains('nav-item')) {
                setTimeout(() => {
                    e.target.style.opacity = '1';
                }, 150);
            }
        });
    </script>
</body>
</html>
