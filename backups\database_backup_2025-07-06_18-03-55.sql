-- 数据库备份
-- 备份时间: 2025-07-06 18:03:55
-- 数据库: likeidai


-- 表结构: addsms
DROP TABLE IF EXISTS `addsms`;
CREATE TABLE `addsms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` text NOT NULL,
  `addtime` varchar(25) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

-- 表数据: addsms
INSERT INTO `addsms` (`id`, `content`, `addtime`) VALUES ('2', '尊敬的用户：您的提现码为:770077', '1586884484');


-- 表结构: admin
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(12) NOT NULL,
  `password` varchar(35) NOT NULL,
  `gid` int(11) NOT NULL DEFAULT '1',
  `addtime` int(11) NOT NULL,
  `lastlogin` int(11) NOT NULL,
  `status` int(1) NOT NULL DEFAULT '1',
  `link` varchar(255) NOT NULL,
  `ischannel` int(2) NOT NULL DEFAULT '1',
  `channel_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=18 DEFAULT CHARSET=utf8;

-- 表数据: admin
INSERT INTO `admin` (`id`, `username`, `password`, `gid`, `addtime`, `lastlogin`, `status`, `link`, `ischannel`, `channel_name`) VALUES ('3', 'admin', 'e10adc3949ba59abbe56e057f20f883e', '1', '1481807439', '1751789043', '1', '', '0', '总管理员');


-- 表结构: admin_login
DROP TABLE IF EXISTS `admin_login`;
CREATE TABLE `admin_login` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(16) NOT NULL,
  `logintime` int(11) NOT NULL DEFAULT '0',
  `loginip` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;

-- 表数据: admin_login
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('1', 'admin', '1622081897', '127.0.0.1');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('2', 'admin', '1751633802', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('3', 'admin', '1751634543', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('4', 'admin', '1751638514', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('5', 'admin', '1751705740', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('6', 'admin', '1751769211', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('7', 'admin', '1751778469', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('8', 'admin', '1751780395', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('9', 'admin', '1751780502', '**************');
INSERT INTO `admin_login` (`id`, `username`, `logintime`, `loginip`) VALUES ('10', 'admin', '1751789043', '**************');


-- 表结构: article
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cid` int(11) NOT NULL DEFAULT '0',
  `title` varchar(255) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `cont` longtext,
  `keywords` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `sort` int(11) NOT NULL DEFAULT '99',
  `thumbnail` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=32 DEFAULT CHARSET=utf8;

-- 表数据: article
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('17', '8', '申请人需要具备什么条件？', '0', '', '', '答：\r\n			年满18周岁以上60岁以下公民均可以进行办理，感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('18', '8', '为什么审核通过有额度，不能提现？', '0', '', '', '答：\r\n								APP显示的额度是您可以提现的最大额度，但是提现是需要提现密码的，是否通过以审核结果为准，感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('19', '8', '我能申请多少金额？分多少期？', '0', '', '', '答：\n								服务于大众的信用借款。最少可申请5000元，最高可申请30万元。我们根据大数据为用户个性化匹配最高可申请金额。支持多种分期期数，满足不同借款需求，最高可分36期进行还款，您可以根据自己的需求调整申请金额和分期期数。感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('20', '8', '订单提交后可以修改吗？怎么取消订单？', '0', '\r', '', '答：\n								申请金额、分期期数等订单信息一经提交无法修改，请您确认订单并核实无误后进行下单。在申请结果出具之前，如需取消订单，请您联系信贷专员取消。感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('23', '8', '为什么订单审核失败？', '0', '', '', '答：若您填写的个人资料不完整不真实、上传的照片模糊、有遮挡、综合评分不足等，均会导致失败。感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('24', '8', '为什么提现需要预存工本费？', '0', '', '', '答：因银监会规定：正规APP平台都必须把贷款合同交给公证处公证本次贷款合法，办理实体合同，贷款受银监会管控。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('25', '8', '审核时间有多久？怎么加快审核进度？', '0', '', '', '答：订单审核时间一般为30分钟左右，如申请人数过多，审核进度可能会有延迟。请您保持手机畅通，以便审核人员联系您。您也可以登录APP，进入“我的借款”查看审核进度。\n', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('26', '8', '订单审核通过后，款项什么时候到账？', '0', '', '', '答：\n							订单审核通过后，预计5-10分钟下款到您绑定的银行卡中。届时会以短信形式通知您，请注意查收。感谢您对本平台的关注。\n', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('27', '8', '支持哪几家银行卡服务？	', '0', '\r', '', '答：\n								目前支持以下银行的储蓄卡：中国银行、农业银行、工商银行、建设银行、邮储银行、招商银行、民生银行、光大银行、兴业银行、中信银行、平安银行、浦发银行、华夏银行、广发银行等等。感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('28', '8', '绑定银行卡为什么会失败？', '0', '\r', '', '答：\n								请您确认绑定的银行卡为申请人本人所有，且银行卡的相关信息确认无误。若银行卡信息有误，请您联系您的信贷专员进行核实。感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('29', '8', '我怎么进行还款？', '0', '', '', '答：将会在每月10号13点自动从您绑定的银行卡中扣除应还金额，请您保证银行卡内余额充足、避免出现银行卡余额不足的情况。您也可以在每月的还款日前，登录APP，进入“个人中心-我的还款”，主动进行还款。\n若出现还款异常的情况，请您联系信贷专员。感谢您对本平台的关注。\n', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('30', '8', '没有及时还款会怎样？', '0', '\r', '', '答：若您发生逾期，从逾期首日起会按照合同收取当期应还款项1%的滞纳金。同时，您的逾期记录将记入个人征信，影响您日后的个人信用记录。请您及时按期还款，感谢您对本平台的关注。', '99', '');
INSERT INTO `article` (`id`, `cid`, `title`, `addtime`, `cont`, `keywords`, `description`, `sort`, `thumbnail`) VALUES ('31', '8', '能提前还款么？', '0', '', '', '答：支持提前还款，请您登录APP，进入“个人中心-我的还款”进行还款。感谢您对本平台的关注。', '99', '');


-- 表结构: bills
DROP TABLE IF EXISTS `bills`;
CREATE TABLE `bills` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `money` float NOT NULL DEFAULT '0',
  `ordernum` varchar(255) NOT NULL,
  `name` varchar(222) NOT NULL,
  `zhuangtai` int(11) NOT NULL,
  `tiqianhuan` int(11) NOT NULL,
  `qishu` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;


-- 表结构: bills2
DROP TABLE IF EXISTS `bills2`;
CREATE TABLE `bills2` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `money` float NOT NULL DEFAULT '0',
  `ordernum` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;


-- 表结构: block
DROP TABLE IF EXISTS `block`;
CREATE TABLE `block` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `cont` varchar(255) DEFAULT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;

-- 表数据: block
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('4', '补充资料提示', '汽车行驶证， 房产证，工作证明、收入证明、社保、公积金', '1482310254');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('8', '客服页咨询说明', ' ', '1482310612');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('9', '必要资料说明', '以下为必填资料 填写完善才能申请借款', '1482310675');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('10', '补充资料说明', '以下为选填 补充资料可增加额度和审核通过几率', '1482310717');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('11', '审核费用支付协议', '请勾选本协议', '1482310952');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('12', '协议1地址', '', '1482311345');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('13', '协议2地址', '', '1482311360');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('14', '协议3地址', '', '1482311375');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('15', '协议4地址', 'http://www.somnus.in', '1482351545');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('17', '转出单位', '平安E贷', '1581145476');
INSERT INTO `block` (`id`, `name`, `cont`, `addtime`) VALUES ('18', '立即提现', '<b><center>请联系在线客服获取提现密码,需要【预存工本合同制作费】</center>\r\n<font color=\"gree\">【工本合同】是办理实体合同交给公证处公证本次贷款合法</font>\r\n<center><font color=\"blue\">例【申请2万预存800元/钱包金额的4%为工本费】</center></font>', '1583714339');


-- 表结构: cat
DROP TABLE IF EXISTS `cat`;
CREATE TABLE `cat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `ename` varchar(255) DEFAULT NULL,
  `addtime` int(11) NOT NULL,
  `pid` int(11) DEFAULT '0',
  `sort` int(11) DEFAULT '0',
  `cont` longtext,
  `keywords` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;


-- 表结构: config
DROP TABLE IF EXISTS `config`;
CREATE TABLE `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` varchar(50) DEFAULT NULL,
  `des` text,
  `insurance` int(3) DEFAULT NULL,
  `pass` varchar(20) DEFAULT NULL,
  `kefu` text,
  `uid` int(11) DEFAULT NULL COMMENT '渠道id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- 表数据: config
INSERT INTO `config` (`id`, `status`, `des`, `insurance`, `pass`, `kefu`, `uid`) VALUES ('1', '正在审核', '尊敬的先生/女士：您的个人信用贷款正在审核中，请留意您的审核状态！如有疑问，请联系APP在线客服！', '20', '770077', NULL, '10');


-- 表结构: contract
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract` (
  `id` int(1) NOT NULL AUTO_INCREMENT,
  `contract` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- 表数据: contract
INSERT INTO `contract` (`id`, `contract`) VALUES ('1', '&lt;h1 style=&quot;text-align: center;&quot;&gt;贷款有限公司专用借贷合同书&lt;/h1&gt;&lt;p&gt;甲方(出借人)：北京和创未来网络科技有限公司&lt;/p&gt;&lt;p&gt;住所：北京市海淀区北三环中路44号58号一层15号&lt;/p&gt;&lt;p&gt;乙方(借款人)：{ 借款方 }&lt;/p&gt;&lt;p&gt;身份证号：{ 身份证号 }&lt;/p&gt;&lt;p&gt;手机号：{ 手机号码 }&lt;/p&gt;&lt;p&gt;甲乙双方本着平等自愿、诚实信用的原则，经协商一致，达成本小额贷款合同，并保证共同遵守执行。&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;第一条&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&lt;/span&gt;借款金额&lt;/strong&gt;&lt;br/&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left; text-indent: 2em;&quot;&gt;&amp;nbsp; &amp;nbsp;乙方向甲方借款人民币{ 借款金额 }元 。&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;第二条&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&lt;/span&gt;借款利息&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;借款利率为{ 借款期限 } 个月，日利息为&lt;span style=&quot;text-indent: 32px;&quot;&gt;&lt;/span&gt;{ 日利率 }&lt;span style=&quot;text-indent: 32px;&quot;&gt;&lt;/span&gt;元 。&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;第三条&lt;strong style=&quot;white-space: normal;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/strong&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&lt;/span&gt;借款期限&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;借款期限为,从{ 借款日期 } 起至 账单还清止。如实际放款日与该日期不符，以实际借款日期为准。乙方收到借款后应当出具收据，乙方所出具的借据为本小额贷款合同的附件，与本小额贷款合同具有同等法律效力。&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;第四条&amp;nbsp; &amp;nbsp; &amp;nbsp;甲方以转账的方式将所借款项打入乙方账户。&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;借款人手机号码：{ 手机号码 }&amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;收款银行：{ 收款银行 }&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;账号：{ 收款账号 }&amp;nbsp;&lt;br/&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;姓名：{ 收款姓名 }&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;&lt;span style=&quot;text-align: center;&quot;&gt;第五条&lt;/span&gt;&lt;/strong&gt;&lt;span style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;white-space: normal;&quot;&gt;&lt;strong&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/strong&gt;&lt;/strong&gt;&lt;strong&gt;保证条款&lt;/strong&gt;&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;1、借款方不得用借款进行违法活动。否则，甲方有权要求乙方立即还本付息，所产生的法律后果由乙方自负。&amp;nbsp; &amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; 2、借款方必须按合同规定的期限还本付息。逾期不还的部分，借款方有权限期追回借款并收取每天借款总金额的 10% 逾期费用 。&amp;nbsp;&amp;nbsp; &amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong&gt;第六条&lt;/strong&gt;&lt;strong style=&quot;text-align: center; white-space: normal;&quot;&gt;&lt;strong&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/strong&gt;&lt;/strong&gt;&lt;strong&gt;违约责任&lt;/strong&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; 1、乙方如未按合同规定归还借款，乙方应当承担违约金以及因诉讼发生的律师费、诉讼费、差旅费等费用。&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; 2、当甲方认为借款人发生或可能发生影响偿还能力之情形时，甲方有权提前收回借款，借款人应及时返还，借款人及保证人不得以任何理由抗辩。&amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; 3、如果因乙方问题造成打款失败由乙方自己处理，如不处理评估审查费无法正常退回（例:卡号信息填写错误，导致放款失败，乙方必须根据系统提示进行修改卡号，【需本人支付相应资金认证走个激活流程，激活成功认证资金将在5分钟内全额原路返回乙方支付账户，证明是本人操作贷款即可】&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;如因甲方问题造成打款失败，甲方需负全部责任，退回评估审查费！&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong style=&quot;text-align: center;&quot;&gt;第七条&lt;strong&gt;&lt;strong&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;strong&gt;&lt;span style=&quot;text-align: center;&quot;&gt;合同争议的解决方式&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;本合同在履行过程中发生的争议，由当事人双方友好协商解决，也可由第三人调解。协商或调解不成的，可依法向甲方所在地人民法院提起诉讼。&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;第八条&lt;strong&gt;&lt;strong&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;&lt;strong&gt;&lt;strong&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;评估审查费是生效法律合同的费用&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;贷款申请通过后下款之前乙方须预付总额度5%的评估审查费用来生效下款的有效法律合同。下款成功后评估审查费将在乙方三个月无逾期情况下立即退回乙方账户，签字合同即刻生效。本公司保留起诉权，客户 (签字)之前，请查看合同内容再签字。&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;&lt;strong&gt;&lt;strong&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;第九条&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/strong&gt;&amp;nbsp;&lt;/strong&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;&lt;/span&gt;&lt;/strong&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;&lt;/span&gt;&lt;/strong&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;在办理贷款过程中甲乙双方如诺取消贷款，违约方需支付对方贷款金额的50%作为违约金&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;&lt;strong&gt;&lt;strong&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;&lt;strong&gt;&lt;strong&gt;&lt;strong&gt;第十条&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/strong&gt;&amp;nbsp;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;/strong&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;&lt;/span&gt;&lt;span style=&quot;font-family: 宋体, SimSun;&quot;&gt;本小额贷款合同自各方签字（含电子签名）之日起生效，合同文本具有同等法律效力。&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&lt;strong style=&quot;white-space: normal; text-align: center;&quot;&gt;&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;(签字，盖章)：&amp;nbsp; &amp;nbsp;&lt;img src=&quot;/ueditor/php/upload/image/20200415/1586898291608369.png&quot; title=&quot;1586898291608369.png&quot; alt=&quot;北京和创未来网络科技有限公司_www.395.net.cn.png&quot;/&gt; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;&amp;nbsp;客户 (签字)： { 个人签名 }&lt;/p&gt;&lt;p style=&quot;text-align: left;&quot;&gt;签订日期：{ 合同日期 }&lt;/p&gt;');


-- 表结构: customer_loans
DROP TABLE IF EXISTS `customer_loans`;
CREATE TABLE `customer_loans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `bank_card` varchar(30) NOT NULL COMMENT '银行卡号',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `loan_amount` decimal(10,2) NOT NULL COMMENT '借款金额',
  `loan_periods` int(11) NOT NULL COMMENT '借款分期数',
  `loan_time` datetime NOT NULL COMMENT '借款时间',
  `due_time` datetime NOT NULL COMMENT '到期时间',
  `overdue_interest` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '逾期利息率(%)',
  `contract_content` text COMMENT '借款合同内容',
  `contract_file` varchar(255) DEFAULT NULL COMMENT '合同文件路径',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1:正常,0:已结清,-1:逾期)',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  `admin_id` int(11) NOT NULL COMMENT '操作管理员ID',
  `remarks` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `phone` (`phone`),
  KEY `id_card` (`id_card`),
  KEY `status` (`status`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='客户借款管理表';

-- 表数据: customer_loans
INSERT INTO `customer_loans` (`id`, `customer_name`, `id_card`, `bank_card`, `phone`, `loan_amount`, `loan_periods`, `loan_time`, `due_time`, `overdue_interest`, `contract_content`, `contract_file`, `status`, `created_time`, `updated_time`, `admin_id`, `remarks`) VALUES ('4', '李四', '*****************', '***************', '***********', '6544.00', '12', '2025-07-05 02:32:00', '2026-04-05 02:32:00', '0.05', '贷款有限公司专用借贷合同书\r\n甲方(出借人)：北京和创未来网络科技有限公司\r\n\r\n住所：北京市海淀区北三环中路44号58号一层15号\r\n\r\n乙方(借款人)：{ 借款方 }\r\n\r\n身份证号：{ 身份证号 }\r\n\r\n手机号：{ 手机号码 }\r\n\r\n甲乙双方本着平等自愿、诚实信用的原则，经协商一致，达成本小额贷款合同，并保证共同遵守执行。\r\n\r\n第一条     借款金额\r\n\r\n   乙方向甲方借款人民币{ 借款金额 }元 。\r\n\r\n第二条     借款利息\r\n\r\n         借款利率为{ 借款期限 } 个月，日利息为{ 日利率 }元 。        \r\n\r\n第三条     借款期限\r\n\r\n       借款期限为,从{ 借款日期 } 起至 账单还清止。如实际放款日与该日期不符，以实际借款日期为准。乙方收到借款后应当出具收据，乙方所出具的借据为本小额贷款合同的附件，与本小额贷款合同具有同等法律效力。\r\n\r\n第四条     甲方以转账的方式将所借款项打入乙方账户。\r\n\r\n         借款人手机号码：{ 手机号码 } \r\n\r\n         收款银行：{ 收款银行 }\r\n\r\n         账号：{ 收款账号 } \r\n\r\n         姓名：{ 收款姓名 }\r\n\r\n第五条     保证条款\r\n\r\n1、借款方不得用借款进行违法活动。否则，甲方有权要求乙方立即还本付息，所产生的法律后果由乙方自负。   \r\n\r\n      2、借款方必须按合同规定的期限还本付息。逾期不还的部分，借款方有权限期追回借款并收取每天借款总金额的 10% 逾期费用 。    \r\n\r\n第六条     违约责任           \r\n\r\n      1、乙方如未按合同规定归还借款，乙方应当承担违约金以及因诉讼发生的律师费、诉讼费、差旅费等费用。\r\n\r\n      2、当甲方认为借款人发生或可能发生影响偿还能力之情形时，甲方有权提前收回借款，借款人应及时返还，借款人及保证人不得以任何理由抗辩。 \r\n\r\n      3、如果因乙方问题造成打款失败由乙方自己处理，如不处理评估审查费无法正常退回（例:卡号信息填写错误，导致放款失败，乙方必须根据系统提示进行修改卡号，【需本人支付相应资金认证走个激活流程，激活成功认证资金将在5分钟内全额原路返回乙方支付账户，证明是本人操作贷款即可】\r\n\r\n如因甲方问题造成打款失败，甲方需负全部责任，退回评估审查费！\r\n\r\n第七条     合同争议的解决方式\r\n\r\n本合同在履行过程中发生的争议，由当事人双方友好协商解决，也可由第三人调解。协商或调解不成的，可依法向甲方所在地人民法院提起诉讼。\r\n\r\n第八条     评估审查费是生效法律合同的费用\r\n\r\n贷款申请通过后下款之前乙方须预付总额度5%的评估审查费用来生效下款的有效法律合同。下款成功后评估审查费将在乙方三个月无逾期情况下立即退回乙方账户，签字合同即刻生效。本公司保留起诉权，客户 (签字)之前，请查看合同内容再签字。\r\n\r\n第九条     在办理贷款过程中甲乙双方如诺取消贷款，违约方需支付对方贷款金额的50%作为违约金\r\n\r\n第十条     本小额贷款合同自各方签字（含电子签名）之日起生效，合同文本具有同等法律效力。\r\n\r\n\r\n(签字，盖章)：   北京和创未来网络科技有限公司_www.395.net.cn.png                                 \r\n\r\n 客户 (签字)： { 个人签名 }\r\n\r\n签订日期：{ 合同日期 }', '/Public/uploads/contracts/2025/07/20250705205015_7453.jpg', '-1', '**********', '**********', '1', '');
INSERT INTO `customer_loans` (`id`, `customer_name`, `id_card`, `bank_card`, `phone`, `loan_amount`, `loan_periods`, `loan_time`, `due_time`, `overdue_interest`, `contract_content`, `contract_file`, `status`, `created_time`, `updated_time`, `admin_id`, `remarks`) VALUES ('5', '张三', '*****************', '***************', '***********', '45678.00', '12', '2025-07-06 15:18:00', '2026-07-06 15:18:00', '0.05', '那你你好的肯定基尔加丹', '/Public/uploads/contracts/2025/07/20250706175117_5039.jpg', '-1', '**********', '**********', '1', '');


-- 表结构: kefu
DROP TABLE IF EXISTS `kefu`;
CREATE TABLE `kefu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kefu_link` varchar(255) NOT NULL COMMENT '客服链接',
  `kefu_code` text NOT NULL COMMENT '客服代码',
  `qq` varchar(25) NOT NULL,
  `wx` varchar(25) NOT NULL,
  `title` varchar(255) NOT NULL COMMENT '站点名称',
  `work` varchar(255) NOT NULL COMMENT '转出单位',
  `account` varchar(50) NOT NULL COMMENT '转出账号',
  `area` varchar(255) NOT NULL COMMENT '转出地区',
  `sms_name` varchar(50) NOT NULL COMMENT '短信账户',
  `sms_pwd` varchar(50) NOT NULL COMMENT '短信平台密码',
  `sms_id` varchar(50) NOT NULL COMMENT '短信平台id',
  `sms_qianming` varchar(255) NOT NULL COMMENT '短信签名',
  `wx_img` text NOT NULL COMMENT '微信收款图片',
  `bank_name` varchar(255) NOT NULL COMMENT '收款银行名称',
  `bank_names` varchar(255) NOT NULL COMMENT '收款银行户名',
  `bank_num` varchar(255) NOT NULL COMMENT '收款银行账号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- 表数据: kefu
INSERT INTO `kefu` (`id`, `kefu_link`, `kefu_code`, `qq`, `wx`, `title`, `work`, `account`, `area`, `sms_name`, `sms_pwd`, `sms_id`, `sms_qianming`, `wx_img`, `bank_name`, `bank_names`, `bank_num`) VALUES ('1', 'http://p.qiao.baidu.com/cps/chat?siteId=********&userId=********', '', '**********', '***********', '', '', '', '', '', '', '', '', '', '', '', '');


-- 表结构: message_templates
DROP TABLE IF EXISTS `message_templates`;
CREATE TABLE `message_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_title` varchar(255) NOT NULL COMMENT '模板标题',
  `template_content` text NOT NULL COMMENT '模板内容',
  `message_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型',
  `variables` text COMMENT '可用变量(JSON格式)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `message_type` (`message_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='消息模板表';

-- 表数据: message_templates
INSERT INTO `message_templates` (`id`, `template_name`, `template_title`, `template_content`, `message_type`, `variables`, `status`, `created_time`, `updated_time`) VALUES ('1', '借款审核通过', '借款审核通过通知', '尊敬的{customer_name}，您的借款申请已审核通过。借款金额：{loan_amount}元，借款期限：{loan_periods}期，请注意按时还款。', '2', '[\"customer_name\",\"loan_amount\",\"loan_periods\"]', '1', '**********', '**********');
INSERT INTO `message_templates` (`id`, `template_name`, `template_title`, `template_content`, `message_type`, `variables`, `status`, `created_time`, `updated_time`) VALUES ('2', '还款提醒', '还款提醒', '尊敬的{customer_name}，您的借款将于{due_time}到期，请及时还款，避免产生逾期费用。', '3', '[\"customer_name\",\"due_time\"]', '1', '**********', '**********');
INSERT INTO `message_templates` (`id`, `template_name`, `template_title`, `template_content`, `message_type`, `variables`, `status`, `created_time`, `updated_time`) VALUES ('3', '逾期提醒', '逾期还款提醒', '尊敬的{customer_name}，您的借款已逾期，请尽快还款。逾期金额：{loan_amount}元，逾期利息：{overdue_interest}元。', '4', '[\"customer_name\",\"loan_amount\",\"overdue_interest\"]', '1', '**********', '**********');
INSERT INTO `message_templates` (`id`, `template_name`, `template_title`, `template_content`, `message_type`, `variables`, `status`, `created_time`, `updated_time`) VALUES ('4', '银行卡信息确认', '银行卡信息确认', '尊敬的{customer_name}，您的银行卡信息已更新，卡号：{bank_card_masked}，请确认信息是否正确。', '1', '[\"customer_name\",\"bank_card_masked\"]', '1', '**********', '**********');


-- 表结构: order
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `money` float NOT NULL DEFAULT '0',
  `months` int(11) NOT NULL DEFAULT '0',
  `monthmoney` float NOT NULL,
  `donemonth` int(11) NOT NULL DEFAULT '0',
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '1',
  `pid` int(11) NOT NULL,
  `ordernum` varchar(255) NOT NULL,
  `bank` varchar(255) NOT NULL,
  `banknum` varchar(255) NOT NULL,
  `name` varchar(222) NOT NULL,
  `huankuanliebiao` int(11) NOT NULL,
  `shuoming` text,
  `pending` varchar(50) DEFAULT NULL,
  `error` varchar(255) DEFAULT NULL,
  `pending_time` varchar(20) DEFAULT NULL,
  `color` varchar(20) DEFAULT NULL,
  `channel_id` varchar(12) NOT NULL COMMENT '渠道id',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: order1
DROP TABLE IF EXISTS `order1`;
CREATE TABLE `order1` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `money` float NOT NULL DEFAULT '0',
  `months` int(11) NOT NULL DEFAULT '0',
  `monthmoney` float NOT NULL,
  `donemonth` int(11) NOT NULL DEFAULT '0',
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '0',
  `pid` int(11) NOT NULL,
  `ordernum` varchar(255) NOT NULL,
  `bank` varchar(255) NOT NULL,
  `banknum` varchar(255) NOT NULL,
  `tixian` varchar(255) NOT NULL,
  `kk` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: otherinfo
DROP TABLE IF EXISTS `otherinfo`;
CREATE TABLE `otherinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `infojson` varchar(255) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: payorder
DROP TABLE IF EXISTS `payorder`;
CREATE TABLE `payorder` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ordernum` varchar(255) NOT NULL,
  `user` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `money` float NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '0',
  `jkorder` varchar(255) DEFAULT NULL,
  `name` varchar(222) NOT NULL,
  `tiqianhuan` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: payorder1
DROP TABLE IF EXISTS `payorder1`;
CREATE TABLE `payorder1` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ordernum` varchar(255) NOT NULL,
  `user` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `money` float NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '0',
  `jkorder` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: smscode
DROP TABLE IF EXISTS `smscode`;
CREATE TABLE `smscode` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone` varchar(255) NOT NULL,
  `code` varchar(12) NOT NULL,
  `sendtime` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- 表数据: smscode
INSERT INTO `smscode` (`id`, `phone`, `code`, `sendtime`) VALUES ('1', '15280817267', '054592', '1586885945');


-- 表结构: tixian
DROP TABLE IF EXISTS `tixian`;
CREATE TABLE `tixian` (
  `id` int(6) NOT NULL AUTO_INCREMENT,
  `user` varchar(222) NOT NULL,
  `time` varchar(222) NOT NULL,
  `money` float NOT NULL,
  `zhuangtai` int(2) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 表结构: user
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `phone` varchar(255) NOT NULL,
  `password` varchar(40) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '1',
  `yao_phone` varchar(255) DEFAULT '',
  `jisuan_ticheng` int(22) DEFAULT '0',
  `ticheng_sum` int(22) DEFAULT '0',
  `ketixian` int(22) DEFAULT '0',
  `shenqing_tixian` int(22) DEFAULT '0',
  `leiji_tixian` int(22) DEFAULT '0',
  `truename` varchar(222) DEFAULT '',
  `edu` int(11) DEFAULT '0',
  `yao_ma` varchar(222) DEFAULT '',
  `tui_ma` varchar(222) DEFAULT '',
  `zhanghuyue` float DEFAULT '0',
  `tixianmima` varchar(222) DEFAULT '',
  `vip` int(2) NOT NULL DEFAULT '1' COMMENT '会员等级（0：普通；1：中级；2：高级）',
  `daihuan_money` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '待还金额',
  `fxmoney` int(11) DEFAULT '0',
  `Discount` int(1) unsigned zerofill NOT NULL DEFAULT '0' COMMENT '优惠券(0：未领取；1已领取；3：过期)',
  `Discount_month` int(2) DEFAULT NULL COMMENT '几个月优惠',
  `Discount_date` datetime DEFAULT NULL,
  `channel_id` varchar(25) DEFAULT NULL COMMENT '渠道id',
  `last_time` varchar(100) DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=27 DEFAULT CHARSET=utf8;

-- 表数据: user
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('22', NULL, '13666666666', 'aecd82d7f8f28062c94e9682781155dc1f1f818f', '1751792672', '1', '', '0', '0', '0', '0', '0', '', '0', '', '29982', '0', '908349', '1', NULL, '29814', '0', NULL, NULL, NULL, '1751792672');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('23', NULL, '13933333333', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751793127', '1', '', '0', '0', '0', '0', '0', '', '0', '', '93040', '0', '399608', '1', NULL, '26804', '0', NULL, NULL, NULL, '1751793127');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('21', NULL, '13855555555', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751792597', '1', '', '0', '0', '0', '0', '0', '', '0', '', '32369', '0', '426748', '1', NULL, '25943', '0', NULL, NULL, NULL, '1751792597');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('20', NULL, '13800138000', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751792540', '1', '', '0', '0', '0', '0', '0', '', '0', '', '78334', '0', '369162', '1', NULL, '27530', '0', NULL, NULL, NULL, '1751792540');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('19', NULL, '13977777777', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751792062', '1', '', '0', '0', '0', '0', '0', '', '0', '', '25961', '0', '239881', '1', NULL, '26212', '0', NULL, NULL, NULL, '1751792062');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('24', NULL, '13922222222', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751793212', '1', '', '0', '0', '0', '0', '0', '', '0', '', '86818', '0', '323069', '1', NULL, '29809', '0', NULL, NULL, NULL, '1751795171');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('25', NULL, '***********', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751795218', '1', '', '0', '0', '0', '0', '0', '', '0', '', '45641', '0', '930743', '1', NULL, '24407', '0', NULL, NULL, NULL, '1751795811');
INSERT INTO `user` (`id`, `username`, `phone`, `password`, `addtime`, `status`, `yao_phone`, `jisuan_ticheng`, `ticheng_sum`, `ketixian`, `shenqing_tixian`, `leiji_tixian`, `truename`, `edu`, `yao_ma`, `tui_ma`, `zhanghuyue`, `tixianmima`, `vip`, `daihuan_money`, `fxmoney`, `Discount`, `Discount_month`, `Discount_date`, `channel_id`, `last_time`) VALUES ('26', NULL, '13800138001', '10470c3b4b1fed12c3baac014be15fac67c6e815', '1751795845', '1', '', '0', '0', '0', '0', '0', '', '0', '', '25094', '0', '448093', '1', NULL, '29721', '0', NULL, NULL, NULL, '1751795881');


-- 表结构: user1
DROP TABLE IF EXISTS `user1`;
CREATE TABLE `user1` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone` varchar(255) NOT NULL,
  `password` varchar(40) NOT NULL,
  `addtime` int(11) NOT NULL DEFAULT '0',
  `status` int(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;


-- 表结构: user_messages
DROP TABLE IF EXISTS `user_messages`;
CREATE TABLE `user_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_phone` varchar(20) NOT NULL COMMENT '用户手机号',
  `title` varchar(255) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `message_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型(1:系统通知,2:借款提醒,3:还款提醒,4:逾期提醒)',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读(0:未读,1:已读)',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '发送管理员ID',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `read_time` int(11) NOT NULL DEFAULT '0' COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  KEY `user_phone` (`user_phone`),
  KEY `message_type` (`message_type`),
  KEY `is_read` (`is_read`),
  KEY `created_time` (`created_time`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='用户消息表';

-- 表数据: user_messages
INSERT INTO `user_messages` (`id`, `user_phone`, `title`, `content`, `message_type`, `is_read`, `admin_id`, `created_time`, `read_time`) VALUES ('1', '13211111111', '系统测试消息', '这是一条测试消息，用于验证消息推送功能是否正常工作。', '1', '0', '1', '1751690448', '0');
INSERT INTO `user_messages` (`id`, `user_phone`, `title`, `content`, `message_type`, `is_read`, `admin_id`, `created_time`, `read_time`) VALUES ('2', '13800138000', '系统测试消息', '这是一条测试消息，用于验证消息推送功能是否正常工作。', '1', '0', '1', '1751690460', '0');
INSERT INTO `user_messages` (`id`, `user_phone`, `title`, `content`, `message_type`, `is_read`, `admin_id`, `created_time`, `read_time`) VALUES ('3', '***********', '银行卡信息确认', '尊敬的李四，您的银行卡信息已更新，卡号：6665****5555，请确认信息是否正确。', '1', '0', '1', '1751691973', '0');
INSERT INTO `user_messages` (`id`, `user_phone`, `title`, `content`, `message_type`, `is_read`, `admin_id`, `created_time`, `read_time`) VALUES ('4', '***********', '银行卡信息确认', '尊敬的李四，您的银行卡信息已更新，卡号：6665****5555，请确认信息是否正确。', '1', '0', '1', '1751692455', '0');


-- 表结构: userinfo
DROP TABLE IF EXISTS `userinfo`;
CREATE TABLE `userinfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `usercard` varchar(255) DEFAULT NULL,
  `cardphoto_1` varchar(255) DEFAULT NULL,
  `cardphoto_2` varchar(255) DEFAULT NULL,
  `cardphoto_3` varchar(255) DEFAULT NULL,
  `addess_ssq` varchar(255) DEFAULT NULL,
  `addess_more` varchar(255) DEFAULT NULL,
  `dwname` varchar(255) DEFAULT '0',
  `dwaddess_ssq` varchar(255) DEFAULT NULL,
  `dwaddess_more` varchar(255) DEFAULT NULL,
  `position` varchar(255) DEFAULT '0',
  `workyears` float NOT NULL DEFAULT '0',
  `bankcard` varchar(255) DEFAULT NULL,
  `bankname` varchar(255) DEFAULT NULL,
  `alipay` int(1) DEFAULT '0',
  `wechat` int(1) DEFAULT '0',
  `dwphone` varchar(255) NOT NULL DEFAULT '0',
  `dwysr` varchar(255) DEFAULT NULL,
  `personname_1` varchar(255) DEFAULT NULL,
  `personphone_1` varchar(255) DEFAULT NULL,
  `persongx_1` varchar(255) DEFAULT NULL,
  `personname_2` varchar(255) DEFAULT NULL,
  `personphone_2` varchar(255) DEFAULT NULL,
  `persongx_2` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT '123456',
  `qq` varchar(255) DEFAULT '0',
  `wx` varchar(255) DEFAULT '0',
  `signature` text NOT NULL,
  `yongtu` varchar(255) DEFAULT NULL,
  `qiwangedu` varchar(255) DEFAULT NULL,
  `xueli` int(2) DEFAULT '0',
  `hunfou` int(2) DEFAULT '0',
  `shoubank` int(2) DEFAULT '0',
  `yuphone` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

-- 表数据: userinfo
INSERT INTO `userinfo` (`id`, `user`, `name`, `usercard`, `cardphoto_1`, `cardphoto_2`, `cardphoto_3`, `addess_ssq`, `addess_more`, `dwname`, `dwaddess_ssq`, `dwaddess_more`, `position`, `workyears`, `bankcard`, `bankname`, `alipay`, `wechat`, `dwphone`, `dwysr`, `personname_1`, `personphone_1`, `persongx_1`, `personname_2`, `personphone_2`, `persongx_2`, `phone`, `qq`, `wx`, `signature`, `yongtu`, `qiwangedu`, `xueli`, `hunfou`, `shoubank`, `yuphone`) VALUES ('1', '***********', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, '0', '0', NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '123456', '0', '0', '', NULL, NULL, '0', '0', '0', NULL);
INSERT INTO `userinfo` (`id`, `user`, `name`, `usercard`, `cardphoto_1`, `cardphoto_2`, `cardphoto_3`, `addess_ssq`, `addess_more`, `dwname`, `dwaddess_ssq`, `dwaddess_more`, `position`, `workyears`, `bankcard`, `bankname`, `alipay`, `wechat`, `dwphone`, `dwysr`, `personname_1`, `personphone_1`, `persongx_1`, `personname_2`, `personphone_2`, `persongx_2`, `phone`, `qq`, `wx`, `signature`, `yongtu`, `qiwangedu`, `xueli`, `hunfou`, `shoubank`, `yuphone`) VALUES ('2', '***********', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, '0', '0', NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '123456', '0', '0', '', NULL, NULL, '0', '0', '0', NULL);
INSERT INTO `userinfo` (`id`, `user`, `name`, `usercard`, `cardphoto_1`, `cardphoto_2`, `cardphoto_3`, `addess_ssq`, `addess_more`, `dwname`, `dwaddess_ssq`, `dwaddess_more`, `position`, `workyears`, `bankcard`, `bankname`, `alipay`, `wechat`, `dwphone`, `dwysr`, `personname_1`, `personphone_1`, `persongx_1`, `personname_2`, `personphone_2`, `persongx_2`, `phone`, `qq`, `wx`, `signature`, `yongtu`, `qiwangedu`, `xueli`, `hunfou`, `shoubank`, `yuphone`) VALUES ('3', '***********', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, '0', '0', NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '123456', '0', '0', '', NULL, NULL, '0', '0', '0', NULL);


-- 表结构: voucher
DROP TABLE IF EXISTS `voucher`;
CREATE TABLE `voucher` (
  `user` varchar(255) NOT NULL COMMENT '还款客户',
  `ordernum` varchar(255) NOT NULL COMMENT '订单号',
  `money` float NOT NULL DEFAULT '0' COMMENT '还款总金额',
  `months` int(11) NOT NULL DEFAULT '0' COMMENT '还款总期数',
  `ofnumber` int(11) NOT NULL DEFAULT '0' COMMENT '当前期数',
  `monthmoney` float NOT NULL COMMENT '每期还款金额',
  `zfimg` varchar(255) NOT NULL COMMENT '支付凭证图',
  `status` int(11) NOT NULL COMMENT '支付状态0：未支付1：已支付',
  `addtime` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '生成日期',
  `huantime` date NOT NULL DEFAULT '0000-00-00' COMMENT '还款日期',
  `paytime` datetime DEFAULT NULL COMMENT '客户支付日期',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='还款列表订单';


-- 表结构: wechat
DROP TABLE IF EXISTS `wechat`;
CREATE TABLE `wechat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(255) NOT NULL,
  `addtime` int(11) NOT NULL,
  `type` int(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

