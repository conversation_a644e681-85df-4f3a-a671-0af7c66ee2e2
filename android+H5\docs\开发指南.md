# 优易花安卓原生壳+H5开发指南

## 项目概述
这是一个混合开发项目，使用安卓原生壳承载H5应用，旨在降低APK报毒率，同时保持完整的用户体验。

## 开发环境搭建

### 1. 安装Android Studio
- 下载地址：https://developer.android.com/studio
- 安装JDK 11或更高版本
- 配置Android SDK

### 2. 导入项目
1. 打开Android Studio
2. 选择"Open an existing project"
3. 选择`android-shell`文件夹
4. 等待Gradle同步完成

### 3. 配置签名（发布时需要）
在`app/build.gradle`中配置签名信息：
```gradle
signingConfigs {
    release {
        storeFile file('your-keystore.jks')
        storePassword 'your-store-password'
        keyAlias 'your-key-alias'
        keyPassword 'your-key-password'
    }
}
```

## H5应用开发

### 1. 文件结构
```
h5-app/
├── index.html          # 主页面
├── assets/
│   ├── css/
│   │   └── app.css     # 样式文件
│   └── js/
│       └── app.js      # JavaScript文件
└── pages/              # 其他页面
```

### 2. 与原生交互
H5页面可以通过JavaScript调用原生功能：

```javascript
// 显示Toast
Android.showToast('消息内容');

// 获取APP版本
var version = Android.getAppVersion();

// 分享功能
Android.shareText('分享内容');

// 退出APP
Android.exitApp();
```

### 3. 检测运行环境
```javascript
if (typeof Android !== 'undefined') {
    // 运行在APP中
    console.log('在APP中运行');
} else {
    // 运行在浏览器中
    console.log('在浏览器中运行');
}
```

## 开发流程

### 1. H5页面开发
1. 在`h5-app`目录下开发H5页面
2. 使用浏览器测试功能
3. 确保移动端适配良好

### 2. 安卓壳测试
1. 在Android Studio中运行项目
2. 在模拟器或真机上测试
3. 检查H5页面加载是否正常

### 3. 功能集成
1. 添加需要的原生功能到MainActivity
2. 在H5页面中调用原生接口
3. 测试原生与H5的交互

### 4. 打包发布
1. 配置签名信息
2. 生成Release版本APK
3. 测试APK安装和运行

## 注意事项

### 1. 网络配置
- 确保`AndroidManifest.xml`中有网络权限
- 在`application`标签中添加`android:usesCleartextTraffic="true"`

### 2. WebView配置
- 启用JavaScript：`settings.setJavaScriptEnabled(true)`
- 启用DOM存储：`settings.setDomStorageEnabled(true)`
- 允许混合内容：`settings.setMixedContentMode(MIXED_CONTENT_ALWAYS_ALLOW)`

### 3. 安全考虑
- 只在可信的域名下加载H5页面
- 验证JavaScript接口调用的安全性
- 避免在H5页面中处理敏感信息

### 4. 性能优化
- 启用WebView硬件加速
- 合理使用缓存策略
- 优化H5页面加载速度

## 常见问题

### 1. WebView无法加载HTTPS页面
检查网络权限和证书配置

### 2. JavaScript接口调用失败
确保接口已正确添加到WebView

### 3. 页面显示异常
检查CSS样式和移动端适配

### 4. APK体积过大
移除不必要的资源和依赖

## 发布清单

- [ ] 功能测试完成
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 签名配置正确
- [ ] APK体积合理
- [ ] 报毒率测试
- [ ] 用户体验验收

## 联系支持
如有问题，请联系开发团队。
