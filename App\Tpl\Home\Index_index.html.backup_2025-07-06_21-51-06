<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>小贷助手 - 移动端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        html {
            font-size: 16px;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* APP状态栏 */
        .app-status-bar {
            height: 44px;
            background: rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        /* APP头部 */
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            color: white;
            text-align: center;
            position: relative;
        }

        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        /* APP主体内容 */
        .app-content {
            background: #f5f5f5;
            min-height: calc(100vh - 44px);
            border-radius: 20px 20px 0 0;
            margin-top: -20px;
            position: relative;
            z-index: 10;
        }

        .header {
            text-align: center;
            color: white;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            font-weight: 600;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: rgba(255,255,255,0.2);
            color: white;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid white;
        }
        
        .btn-secondary:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 60px;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
            font-size: 16px;
        }
        
        .stats {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 16px;
        }
        
        .services {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .services h2 {
            text-align: center;
            font-size: 32px;
            margin-bottom: 30px;
            color: #333;
        }
        
        .service-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .service-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .service-item:hover {
            background: #e9ecef;
            transform: translateY(-3px);
        }
        
        .service-icon {
            font-size: 40px;
            margin-bottom: 15px;
            display: block;
        }
        
        .service-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .service-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .footer {
            text-align: center;
            color: white;
            padding: 40px 0;
            opacity: 0.8;
        }
        
        .user-info {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .user-welcome {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .user-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .quick-nav {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .quick-nav h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        
        /* 移动端卡片网格 */
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 0;
        }

        .nav-item {
            background: white;
            padding: 20px 15px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
            min-height: 85px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .nav-item:hover, .nav-item:active {
            transform: scale(0.98);
            box-shadow: 0 4px 12px rgba(0,0,0,0.12);
            text-decoration: none;
            color: #333;
            background: #f8f9fa;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        /* 用户信息横排布局 */
        .user-section {
            padding: 20px;
            margin: 20px 0;
        }

        .user-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .user-info-horizontal {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            flex-shrink: 0;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-welcome {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .user-phone {
            font-size: 14px;
            color: #666;
        }

        .user-actions-horizontal {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }

        .action-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .action-btn:hover, .action-btn:active {
            transform: scale(0.95);
            text-decoration: none;
            color: white;
        }

        /* 登录卡片 */
        .login-section {
            padding: 20px;
            margin: 20px 0;
        }

        .login-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .login-card h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }

        .login-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 20px 15px;
            }

            .header h1 {
                font-size: 28px;
            }

            .header p {
                font-size: 14px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features {
                grid-template-columns: 1fr;
                padding: 15px;
                gap: 15px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            /* 移动端导航优化 */
            .quick-nav {
                padding: 20px 15px;
                margin: 15px 0;
                border-radius: 12px;
            }

            .nav-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                padding: 0;
            }

            .nav-item {
                padding: 16px 10px;
                border-radius: 10px;
                min-height: 75px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                margin: 0;
                box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            }

            .nav-icon {
                font-size: 20px;
                margin-bottom: 6px;
                display: block;
            }

            .nav-item div {
                font-size: 13px;
                font-weight: 500;
                line-height: 1.2;
            }

            .user-section, .login-section {
                padding: 15px;
                margin: 15px 0;
            }

            .user-card, .login-card {
                padding: 15px;
                border-radius: 12px;
            }

            .user-info-horizontal {
                gap: 10px;
            }

            .user-avatar {
                width: 45px;
                height: 45px;
                font-size: 20px;
            }

            .user-welcome {
                font-size: 14px;
            }

            .user-phone {
                font-size: 12px;
            }

            .action-btn {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }

            /* 移动端底部导航 */
            .bottom-nav {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: white;
                padding: 10px 0 20px;
                box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
                z-index: 1000;
            }

            .nav-items {
                display: flex;
                justify-content: space-around;
                align-items: center;
            }

            .nav-item-bottom {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-decoration: none;
                color: #666;
                font-size: 12px;
                transition: all 0.2s ease;
            }

            .nav-item-bottom.active {
                color: #667eea;
            }

            .nav-item-bottom:hover, .nav-item-bottom:active {
                color: #667eea;
                text-decoration: none;
            }

            .nav-icon-bottom {
                font-size: 20px;
                margin-bottom: 4px;
            }

            /* 底部安全区域 */
            .bottom-safe-area {
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 1. 页面标题 - 最顶部 -->
        <div class="header">
            <h1>💰 小贷系统</h1>
            <p>专业、安全、便捷的借贷服务平台</p>
        </div>
        
        <!-- 2. 快速导航 - 中间位置 -->
        <div class="quick-nav">
            <h3>🚀 快速导航</h3>
            <div class="nav-grid">
                <a href="{:U('Index/index')}" class="nav-item">
                    <span class="nav-icon">🏠</span>
                    <div>首页</div>
                </a>
                <a href="{:U('User/loanInfo')}" class="nav-item">
                    <span class="nav-icon">💳</span>
                    <div>借款信息</div>
                </a>
                <a href="{:U('User/profile')}" class="nav-item">
                    <span class="nav-icon">👤</span>
                    <div>个人中心</div>
                </a>
                <a href="{:U('User/logout')}" class="nav-item">
                    <span class="nav-icon">📤</span>
                    <div>退出</div>
                </a>
            </div>
        </div>
        
        <!-- 核心功能 -->
        <div class="features">
            <div class="feature-card">
                <span class="feature-icon">🔒</span>
                <div class="feature-title">安全可靠</div>
                <div class="feature-desc">
                    采用银行级安全标准，多重加密保护，确保您的资金和信息安全
                </div>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <div class="feature-title">便捷服务</div>
                <div class="feature-desc">
                    智能处理系统，便捷服务流程，高效业务办理，满足您的各种需求
                </div>
            </div>
            
            <div class="feature-card">
                <span class="feature-icon">💎</span>
                <div class="feature-title">灵活还款</div>
                <div class="feature-desc">
                    多种还款方式，灵活的还款期限，让您轻松管理财务，无压力还款
                </div>
            </div>
        </div>
        
        <!-- 平台数据 -->
        <div class="stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">10,000+</div>
                    <div class="stat-label">服务用户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">系统稳定性</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">在线服务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5分钟</div>
                    <div class="stat-label">平均审批时间</div>
                </div>
            </div>
        </div>
        
        <!-- 服务项目 -->
        <div class="services">
            <h2>🎯 我们的服务</h2>
            <div class="service-list">
                <div class="service-item">
                    <span class="service-icon">💰</span>
                    <div class="service-title">个人借款</div>
                    <div class="service-desc">
                        灵活的个人借款服务，满足您的各种资金需求
                    </div>
                </div>
                
                <div class="service-item">
                    <span class="service-icon">📊</span>
                    <div class="service-title">借款管理</div>
                    <div class="service-desc">
                        完善的借款管理系统，实时查看借款状态和还款计划
                    </div>
                </div>
                
                <div class="service-item">
                    <span class="service-icon">📱</span>
                    <div class="service-title">移动服务</div>
                    <div class="service-desc">
                        支持手机端操作，随时随地管理您的借款业务
                    </div>
                </div>
                
                <div class="service-item">
                    <span class="service-icon">🛡️</span>
                    <div class="service-title">隐私保护</div>
                    <div class="service-desc">
                        严格的隐私保护机制，确保您的个人信息安全
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3. 用户信息区域 - 最下面 -->
        <if condition="$user_phone">
            <div class="user-section">
                <div class="user-card">
                    <div class="user-info-horizontal">
                        <div class="user-avatar">👤</div>
                        <div class="user-details">
                            <div class="user-welcome">👋 欢迎回来！</div>
                            <div class="user-phone">手机号：{$user_phone}</div>
                        </div>
                        <div class="user-actions-horizontal">
                            <a href="{:U('User/loanInfo')}" class="action-btn">📊</a>
                            <a href="{:U('User/profile')}" class="action-btn">👤</a>
                            <a href="{:U('User/logout')}" class="action-btn">🚪</a>
                        </div>
                    </div>
                </div>
            </div>
        <else/>
            <!-- 未登录用户的登录按钮 -->
            <div class="login-section">
                <div class="login-card">
                    <h3>🔐 账户登录</h3>
                    <div class="login-buttons">
                        <a href="{:U('User/login')}" class="btn btn-primary">
                            🔑 立即登录
                        </a>
                        <a href="{:U('User/signup')}" class="btn btn-secondary">
                            📝 注册账号
                        </a>
                    </div>
                </div>
            </div>
        </if>

        <!-- 底部安全区域 -->
        <div class="bottom-safe-area"></div>

        <!-- 页脚 -->
        <div class="footer">
            <p>© 2025 小贷系统 - 专业的借贷服务平台</p>
            <p>安全 • 便捷 • 可靠</p>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="{:U('Index/index')}" class="nav-item-bottom active">
                <span class="nav-icon-bottom">🏠</span>
                <span>首页</span>
            </a>
            <a href="{:U('User/loanInfo')}" class="nav-item-bottom">
                <span class="nav-icon-bottom">💳</span>
                <span>借款信息</span>
            </a>
            <a href="{:U('User/profile')}" class="nav-item-bottom">
                <span class="nav-icon-bottom">👤</span>
                <span>个人中心</span>
            </a>
            <a href="{:U('User/logout')}" class="nav-item-bottom">
                <span class="nav-icon-bottom">🚪</span>
                <span>退出</span>
            </a>
        </div>
    </div>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加进入动画
            const cards = document.querySelectorAll('.feature-card, .service-item, .nav-item');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
            
            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalText = stat.textContent;
                stat.textContent = '0';
                
                setTimeout(() => {
                    stat.style.transition = 'all 1s ease';
                    stat.textContent = finalText;
                }, 500);
            });
        });
    </script>
</body>
</html>
